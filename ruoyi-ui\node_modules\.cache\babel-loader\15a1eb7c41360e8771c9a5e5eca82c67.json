{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification\\one.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification\\one.vue", "mtime": 1753926699532}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\babel.config.js", "mtime": 1750995480034}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_review", "require", "_detail", "_expertStatus", "_entInfo", "data", "supplierOptions", "scoringSystem", "selectedFactorNode", "selectedSupplierName", "selectedSupplier", "expertInfo", "ratingStateMap", "fileInfo", "isResponseVisible", "isProcurementVisible", "isDoubleView", "entDocResponsePage", "factorDetailList", "factorsPageMap", "entDocProcurementPage", "pageProcurement", "attachmentsList", "supplierFactorPageMap", "responsePdfUrl", "procurementPdfUrl", "activeButton", "responsePdfRendered", "procurementPdfRendered", "helpImageList", "factorKeyMap", "checkResult", "checkResultLabel", "hoveredFactorNode", "tooltipTimer", "methods", "getCheckResultState", "factorName", "Object", "keys", "length", "state", "key", "validateAllRatings", "_iterator", "_createForOfIteratorHelper2", "default", "uitems", "_step", "s", "n", "done", "item", "value", "entMethodItemId", "reason", "trim", "$message", "warning", "concat", "itemName", "err", "e", "f", "generateSaveData", "ratingCopy", "JSON", "parse", "stringify", "_iterator2", "_step2", "itemId", "evaluationResult", "evaluationRemark", "push", "scoringMethodUitemId", "expertResultId", "resultId", "entId", "bidderId", "saveRatingTemp", "_this", "Promise", "resolve", "code", "success", "scoringFactors", "then", "response", "msg", "catch", "error", "submitRating", "_this2", "saveResult", "checkAndSubmitReview", "_this3", "projectId", "$route", "query", "scoringMethodItemId", "checkReviewSummary", "updateExpertScoreStatus", "$emit", "_this4", "status", "evalExpertScoreInfoId", "localStorage", "getItem", "evalState", "editEvalExpertScoreInfo", "res", "initPage", "initExpertInfo", "initEntDocResponsePage", "initEntDocProcurementPage", "loadSupplierOptions", "loadScoringSystem", "loadFiles", "expertInfoStr", "console", "log", "warn", "_this5", "supplierInfo", "rows", "filter", "isAbandonedBid", "_this6", "approvalProcess", "busiTenderNotice", "attachments", "fileType", "scoringMethodUinfo", "scoringMethodItems", "find", "setItem", "evalProjectEvaluationProcess", "initRatingStateMapBySystem", "reduce", "acc", "_this7", "filesById", "tenderNoticeFilePath", "handleSupplierChange", "supplierName", "bidderName", "loadSupplierFactorDetail", "loadSupplierCheckResult", "showResponseFile", "_this8", "clearRatingStateMap", "detailData", "getDetailByPsxx", "_this8$factorDetailLi", "factor", "evalExpertEvaluationDetails", "setRatingStateMapByFactor", "_this9", "for<PERSON>ach", "_this0", "reviewData", "resDocReviewFactorsDecision", "_this1", "_this10", "file", "showFileContrast", "showProcurementFile", "pageProcurementArr", "jumpToPage", "i", "j", "_objectSpread2", "jumpToFactorPage", "factorItem", "canJumpToPage", "$refs", "procurement", "skipPage", "handlePdfRenderStatusChange", "isRendered", "pdfType", "goToSecondOffer", "zjhm", "$router", "path", "goToBidInquiry", "loadFactorsPageMap", "downloadFile", "$download", "zip", "filePath", "fileName", "showFactorTooltip", "_this11", "itemRemark", "clearTimeout", "setTimeout", "hideFactorTooltip", "_this12", "clearTooltipTimer", "mounted", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/expertReview/qualification/one.vue"], "sourcesContent": ["<template>\r\n\t<div class=\"main-container-one\">\r\n\t\t<div class=\"left-panel\">\r\n\t\t\t<div class=\"header-bar\">\r\n\t\t\t\t<div class=\"header-title\">\r\n\t\t\t\t\t<div>资格性评审</div>\r\n\t\t\t\t\t<div class=\"header-steps\">\r\n\t\t\t\t\t\t<div class=\"steps-tip\">该页面操作说明</div>\r\n\t\t\t\t\t\t<el-image class=\"steps-img\" :src=\"helpImageList[0]\" :preview-src-list=\"helpImageList\">\r\n\t\t\t\t\t\t</el-image>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 文件列表 -->\r\n\t\t\t\t<div class=\"fileList\" style=\"width: 200px; border-right: 1px solid #e6e6e6; border-left: 1px solid #e6e6e6; padding: 10px; overflow-y: auto;\">\r\n\t\t\t\t\t<div style=\"font-weight: bold; margin-bottom: 10px; color: #333;\">响应文件附件下载</div>\r\n\t\t\t\t\t<el-card\r\n\t\t\t\t\t\tv-for=\"(item, index) in attachmentsList\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\tclass=\"fileItem\"\r\n\t\t\t\t\t\tshadow=\"hover\"\r\n\t\t\t\t\t\**************=\"downloadFile(item)\"\r\n\t\t\t\t\t\tstyle=\"margin-bottom: 8px; cursor: pointer;\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div style=\"display: flex; align-items: center; padding: 5px;\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-document\" style=\"margin-right: 8px; color: #409EFF;\"></i>\r\n\t\t\t\t\t\t\t<span style=\"font-size: 12px; flex: 1; word-break: break-all;\">{{ item.fileName }}</span>\r\n\t\t\t\t\t\t\t<i class=\"el-icon-download\" style=\"margin-left: 8px; color: #999;\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-card>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div class=\"header-btns\">\r\n\t\t\t\t\t<el-button class=\"item-button\" @click=\"goToBidInquiry\">询标</el-button>\r\n\t\t\t\t\t<!-- <el-button class=\"item-button\" @click=\"secondOffer\">发起二次报价</el-button> -->\r\n\t\t\t\t\t<div class=\"header-btns-group\">\r\n\t\t\t\t\t\t<el-button\r\n\t\t\t\t\t\t\t:class=\"['item-button', activeButton === 'procurement' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']\"\r\n\t\t\t\t\t\t\t@click=\"showProcurementFile\">采购文件</el-button>\r\n\t\t\t\t\t\t<el-button\r\n\t\t\t\t\t\t\t:class=\"['item-button', activeButton === 'response' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']\"\r\n\t\t\t\t\t\t\t@click=\"showResponseFile\">响应文件</el-button>\r\n\t\t\t\t\t\t<el-button\r\n\t\t\t\t\t\t\t:class=\"['item-button', activeButton === 'contrast' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']\"\r\n\t\t\t\t\t\t\t@click=\"showFileContrast\">对比</el-button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div style=\"height:82%\">\r\n\t\t\t\t<!-- PDF预览区域 -->\r\n\t\t\t\t<div class=\"pdf-container\">\r\n\t\t\t\t\t<div v-show=\"isProcurementVisible\" :class=\"['pdf-view', { 'border-right': isDoubleView }]\">\r\n<!--\t\t\t\t\t\t<pdfView ref=\"procurement\" :pdfurl=\"procurementPdfUrl\" :uni_key=\"'procurement'\"></pdfView>-->\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<PdfViewImproved\r\n\t\t\t\t\t\t\tref=\"procurement\"\r\n\t\t\t\t\t\t\t:pdfurl=\"procurementPdfUrl\"\r\n\t\t\t\t\t\t\t:page-height=\"800\"\r\n\t\t\t\t\t\t\t:buffer-size=\"2\"\r\n\t\t\t\t\t\t\t@render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div v-show=\"isResponseVisible\" :class=\"['pdf-view', { 'border-left': isDoubleView }]\">\r\n<!--\t\t\t\t\t\t<pdfView ref=\"response\" :pdfurl=\"responsePdfUrl\" :uni_key=\"'response'\"></pdfView>-->\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<PdfViewImproved\r\n\t\t\t\t\t\t\tref=\"response\"\r\n\t\t\t\t\t\t\t:pdfurl=\"responsePdfUrl\"\r\n\t\t\t\t\t\t\t:page-height=\"800\"\r\n\t\t\t\t\t\t\t:buffer-size=\"2\"\r\n\t\t\t\t\t\t\t@render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"divider\"></div>\r\n\t\t<div class=\"right-panel\">\r\n\t\t\t<div class=\"right-header\">\r\n\t\t\t\t<el-select style=\"width:100%\" v-model=\"selectedSupplierName\" placeholder=\"请选择供应商\" @change=\"handleSupplierChange\">\r\n\t\t\t\t\t<el-option v-for=\"item in supplierOptions\" :key=\"item.bidderName\" :label=\"item.bidderName\" :value=\"item.bidderName\">\r\n\t\t\t\t\t</el-option>\r\n\t\t\t\t</el-select>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"right-content\" v-if=\"isResponseVisible\">\r\n\t\t\t\t<!-- PDF渲染状态提示 -->\r\n\t\t\t\t<div v-if=\"!responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t\t\t\t<i class=\"el-icon-loading\"></i>\r\n\t\t\t\t\t<span>响应文件正在渲染中，请稍候...</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div v-else class=\"render-status-tip success\">\r\n\t\t\t\t\t<i class=\"el-icon-success\"></i>\r\n\t\t\t\t\t<span>响应文件渲染完成，可以点击跳转</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div v-for=\"(item, index) in scoringSystem.uitems\" :key=\"index\" class=\"factor-item\"\r\n\t\t\t\t\t@mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t<!-- 悬浮框 -->\r\n\t\t\t\t\t<div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t\t\t\tclass=\"factor-tooltip\"\r\n\t\t\t\t\t\t@mouseenter=\"clearTooltipTimer\"\r\n\t\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t\t<div class=\"tooltip-header\">\r\n\t\t\t\t\t\t\t<div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t\t\t<i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<div class=\"factors\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclass=\"factor-title\"\r\n\t\t\t\t\t\t\t\t:class=\"{ 'disabled': !canJumpToPage() }\"\r\n\t\t\t\t\t\t\t\t@click=\"jumpToFactorPage(item)\">\r\n\t\t\t\t\t\t\t\t{{ item.itemName }}\r\n\t\t\t\t\t\t\t\t<i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"factor-radio-group\">\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<el-input v-if=\"(ratingStateMap[item.entMethodItemId].state == 0)\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n\t\t\t\t\t\t</el-input>\r\n\t\t\t\t\t\t<span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n\t\t\t\t\t\t\t{{checkResultLabel[item.itemName]}}</span>\r\n\t\t\t\t\t\t<div class=\"factor-divider\"></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"right-btns\">\r\n\t\t\t\t\t<!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"save\">保存</el-button></div> -->\r\n\t\t\t\t\t<div><el-button class=\"item-button-little\" @click=\"submitRating\">提交</el-button></div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"review-content\">\r\n\t\t\t\t\t<div class=\"review-title\">评审内容：</div>\r\n\t\t\t\t\t<div class=\"review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<div class=\"right-content\" v-else>\r\n\t\t\t\t<!-- PDF渲染状态提示 -->\r\n\t\t\t\t<div v-if=\"!procurementPdfRendered\" class=\"render-status-tip\">\r\n\t\t\t\t\t<i class=\"el-icon-loading\"></i>\r\n\t\t\t\t\t<span>采购文件正在渲染中，请稍候...</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div v-else class=\"render-status-tip success\">\r\n\t\t\t\t\t<i class=\"el-icon-success\"></i>\r\n\t\t\t\t\t<span>采购文件渲染完成，可以点击跳转</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div v-for=\"(item, index) in pageProcurement\" :key=\"index\" class=\"factor-item\"\r\n\t\t\t\t\t@mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t<!-- 悬浮框 -->\r\n\t\t\t\t\t<div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t\t\t\tclass=\"factor-tooltip\"\r\n\t\t\t\t\t\t@mouseenter=\"clearTooltipTimer\"\r\n\t\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t\t<div class=\"tooltip-header\">\r\n\t\t\t\t\t\t\t<div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t\t\t<i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<div class=\"factors\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclass=\"factor-title\"\r\n\t\t\t\t\t\t\t\t:class=\"{ 'disabled': !canJumpToPage() }\"\r\n\t\t\t\t\t\t\t\t@click=\"jumpToFactorPage(item)\">\r\n\t\t\t\t\t\t\t\t{{ item.itemName }}\r\n\t\t\t\t\t\t\t\t<i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"factor-radio-group\">\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<el-input v-if=\"(ratingStateMap[item.entMethodItemId].state == 0)\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n\t\t\t\t\t\t</el-input>\r\n\t\t\t\t\t\t<span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n\t\t\t\t\t\t\t{{checkResultLabel[item.itemName]}}</span>\r\n\t\t\t\t\t\t<div class=\"factor-divider\"></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"right-btns\">\r\n\t\t\t\t\t<!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"save\">保存</el-button></div> -->\r\n\t\t\t\t\t<div><el-button class=\"item-button-little\" @click=\"submitRating\">提交</el-button></div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"review-content\">\r\n\t\t\t\t\t<div class=\"review-title\">评审内容：</div>\r\n\t\t\t\t\t<div class=\"review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n\tsupplierInfo,\r\n\tapprovalProcess,\r\n\tscoringFactors,\r\n\tcheckReviewSummary,\r\n\tfilesById,\r\n} from \"@/api/expert/review\";\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\";\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\r\nimport { resDocReviewFactorsDecision } from \"@/api/docResponse/entInfo\";\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tsupplierOptions: [], // 供应商下拉选项\r\n\t\t\tscoringSystem: [], // 当前评分体系\r\n\t\t\tselectedFactorNode: {}, // 当前选中评分项\r\n\t\t\tselectedSupplierName: \"\", // 当前选中供应商名称\r\n\t\t\tselectedSupplier: {}, // 当前选中供应商对象\r\n\t\t\texpertInfo: {}, // 专家信息\r\n\t\t\tratingStateMap: {}, // 评分项状态与原因\r\n\t\t\tfileInfo: {}, // 文件信息\r\n\t\t\tisResponseVisible: false, // 是否显示响应文件\r\n\t\t\tisProcurementVisible: false, // 是否显示采购文件\r\n\t\t\tisDoubleView: false, // 是否对比显示\r\n\t\t\t\r\n\t\t\tentDocResponsePage: {}, // 响应文件页码信息\r\n\t\t\t\r\n\t\t\tfactorDetailList: [], // 评分项详情\r\n\t\t\tfactorsPageMap: {}, // 评分项页码信息\r\n\t\t\t\r\n\t\t\tentDocProcurementPage: {}, // 采购文件页码信息\r\n\t\t\tpageProcurement:[], // 采购文件的评分项\r\n\t\t\tattachmentsList: [], // 文件列表\r\n\t\t\t\r\n\t\t\tsupplierFactorPageMap: {}, // 当前供应商评分项页码\r\n\t\t\tresponsePdfUrl: null, // 响应文件PDF路径\r\n\t\t\tprocurementPdfUrl: null, // 采购文件PDF路径\r\n\r\n\t\t\t// 按钮状态管理\r\n\t\t\tactiveButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n\t\t\t// PDF渲染状态管理\r\n\t\t\tresponsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n\t\t\tprocurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n\t\t\thelpImageList: [\"/evalution/help.jpg\"], // 步骤图片\r\n\t\t\tfactorKeyMap: { // 评分项与后端字段映射\r\n\t\t\t\t\"特定资格要求\": \"zgzs\",\r\n\t\t\t\t\"响应内容\": \"jsplb\",\r\n\t\t\t\t\"采购需求\": \"jsplb\",\r\n\t\t\t\t\"供货期限\": \"ghqx\",\r\n\t\t\t\t\"投标报价\": \"tbbj\"\r\n\t\t\t},\r\n\t\t\tcheckResult: {}, // 系统初验结果\r\n\t\t\tcheckResultLabel: { // 系统初验结果名称\r\n\t\t\t\t\"符合《中华人民共和国政府采购法》第二十二条规定\": \"系统初验通过\",\r\n\t\t\t\t\"特定资格要求\": \"系统初验通过\",\r\n\t\t\t\t\"信用查询\": \"系统初验通过\",\r\n\t\t\t\t\"响应人名称\": \"系统初验通过\",\r\n\t\t\t\t\"响应内容\": \"系统初验通过\",\r\n\t\t\t\t\"采购需求\": \"系统初验通过\",\r\n\t\t\t\t\"供货期限\": \"系统初验通过\",\r\n\t\t\t\t\"投标报价\": \"系统初验通过\"\r\n\t\t\t},\r\n\r\n\t\t\t// 悬停状态管理\r\n\t\t\thoveredFactorNode: null, // 悬停时的评分项\r\n\t\t\ttooltipTimer: null, // 悬浮框显示定时器\r\n\t\t};\r\n\t},\r\n\r\n\tmethods: {\r\n\t\t// ========== 评分相关 ==========\r\n\t\t/**\r\n\t\t * 系统初验结果判断\r\n\t\t * @param {string} factorName 评分项名称\r\n\t\t * @returns {string} 1-通过 0-未通过\r\n\t\t */\r\n\t\tgetCheckResultState(factorName) {\r\n\t\t\tif (!this.checkResult || Object.keys(this.checkResult).length === 0) return \"\"; // 如果没有系统初验结果，则返回空\r\n\t\t\tlet state = \"1\";\r\n\t\t\tconst key = this.factorKeyMap[factorName];\r\n\t\t\tif (key) {\r\n\t\t\t\tstate = this.checkResult[key];\r\n\t\t\t\tif (factorName === \"投标报价\" && state === \"1\") {\r\n\t\t\t\t\tstate = this.checkResult[\"mxbjb\"];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (state === \"0\") {\r\n\t\t\t\tthis.checkResultLabel[factorName] = \"系统初验未通过\";\r\n\t\t\t} else {\r\n\t\t\t\tstate = \"1\";\r\n\t\t\t\tthis.checkResultLabel[factorName] = \"系统初验通过\";\r\n\t\t\t}\r\n\t\t\treturn state;\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 校验所有评分项是否填写完整\r\n\t\t * @returns {boolean} 是否全部填写\r\n\t\t */\r\n\t\tvalidateAllRatings() {\r\n\t\t\tfor (const item of this.scoringSystem.uitems) {\r\n\t\t\t\tconst state = this.ratingStateMap[item.entMethodItemId].state;\r\n\t\t\t\tconst reason = this.ratingStateMap[item.entMethodItemId].reason;\r\n\t\t\t\t// 评分结果未填写\r\n\t\t\t\tif (state === null || state === '') {\r\n\t\t\t\t\t// this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t\t// 不通过但未填写原因\r\n\t\t\t\tif (state === \"0\" && (!reason || reason.trim() === '')) {\r\n\t\t\t\t\tthis.$message.warning(`${item.itemName}评审不通过但未填写备注，不进行保存`);\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t},\r\n\r\n\t\t// 生成保存数据\r\n\t\tgenerateSaveData() {\r\n\t\t\tconst ratingCopy = JSON.parse(JSON.stringify(this.ratingStateMap)); // 评分项状态\r\n\t\t\tconst data = []; // 保存数据\r\n\t\t\tfor (const item of this.scoringSystem.uitems) { // 遍历评分项\r\n\t\t\t\tconst itemId = item.entMethodItemId; // 评分项ID\r\n\t\t\t\tconst evaluationResult = ratingCopy[itemId].state; // 评分项状态\r\n\t\t\t\tif (evaluationResult === null || evaluationResult === \"\") continue; // 如果评分项状态为空，则跳过\r\n\t\t\t\t// 注意：不通过原因的校验已经在validateAllRatings中处理，这里只需要构建数据\r\n\t\t\t\tconst evaluationRemark = ratingCopy[itemId].reason || \"\"; // 评分项备注\r\n\t\t\t\tdata.push({\r\n\t\t\t\t\tscoringMethodUitemId: itemId, // 评分项ID\r\n\t\t\t\t\texpertResultId: this.expertInfo.resultId, // 专家ID\r\n\t\t\t\t\tentId: this.selectedSupplier.bidderId, // 供应商ID\r\n\t\t\t\t\tevaluationResult, // 评分项状态\r\n\t\t\t\t\tevaluationRemark // 评分项备注\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\treturn data; // 返回保存数据\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 临时保存评分结果\r\n\t\t * @returns {Promise}\r\n\t\t */\r\n\t\tsaveRatingTemp() {\r\n\t\t\t// 先校验所有评分项是否填写完整\r\n\t\t\tif (!this.validateAllRatings()) {\r\n\t\t\t\treturn Promise.resolve({ code: 0, success: false }); // 校验失败\r\n\t\t\t}\r\n\r\n\t\t\tconst data = this.generateSaveData(); // 生成保存数据\r\n\t\t\tif (data.length > 0) {\r\n\t\t\t\treturn scoringFactors(data).then(response => {\r\n\t\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t\tthis.$message.success(\"保存成功\");\r\n\t\t\t\t\t\treturn { code: 200, success: true };\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t\t\treturn { code: response.code, success: false };\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch((error) => {\r\n\t\t\t\t\tthis.$message.error(\"保存失败\");\r\n\t\t\t\t\treturn { code: 0, success: false };\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\treturn Promise.resolve({ code: 200, success: true }); // 没有数据需要保存时也返回成功\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 提交评分结果\r\n\t\t */\r\n\t\tsubmitRating() {\r\n\t\t\tthis.saveRatingTemp().then((saveResult) => {\r\n\t\t\t\t// 检查保存结果，如果校验失败则不继续提交\r\n\t\t\t\tif (!saveResult || saveResult.success === false) {\r\n\t\t\t\t\treturn; // 校验失败，不继续提交流程\r\n\t\t\t\t}\r\n\t\t\t\tthis.checkAndSubmitReview();\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 检查并提交评审汇总\r\n\t\tcheckAndSubmitReview() {\r\n\t\t\t\tconst data = {\r\n\t\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\t\texpertResultId: this.expertInfo.resultId,\r\n\t\t\t\t\tscoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n\t\t\t\t};\r\n\t\t\t\tcheckReviewSummary(data).then((response) => {\r\n\t\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t\tthis.updateExpertScoreStatus(); // 修改专家进度状态\r\n\t\t\t\t\t\tthis.$emit(\"send\", \"two\"); // 跳转至二次报价\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 修改专家进度状态\r\n\t\tupdateExpertScoreStatus() {\r\n\t\t\tconst status = {\r\n\t\t\t\tevalExpertScoreInfoId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\")).evalExpertScoreInfoId,\r\n\t\t\t\tevalState: 1,\r\n\t\t\t};\r\n\t\t\teditEvalExpertScoreInfo(status).then((res) => {\r\n\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\tthis.$message.success(\"提交成功\");\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// ========== 初始化相关 ==========\r\n\t\t/**\r\n\t\t * 初始化页面数据\r\n\t\t */\r\n\t\tinitPage() {\r\n\t\t\tthis.initExpertInfo();// 初始化专家信息\r\n\t\t\tthis.initEntDocResponsePage(); // 初始化响应文件页码信息\r\n\t\t\tthis.initEntDocProcurementPage(); // 初始化采购文件页码信息\r\n\t\t\tthis.loadSupplierOptions();// 加载供应商下拉选项\r\n\t\t\tthis.loadScoringSystem(); // 加载评分体系\r\n\t\t\tthis.loadFiles(); // 加载文件信息\r\n\t\t},\r\n\t\t// 初始化专家信息\r\n\t\tinitExpertInfo() {\r\n\t\t\ttry {\r\n\t\t\t\tconst expertInfoStr = localStorage.getItem(\"expertInfo\");\r\n\t\t\t\tif (expertInfoStr) {\r\n\t\t\t\t\tthis.expertInfo = JSON.parse(expertInfoStr);\r\n\t\t\t\t\tconsole.log(\"专家信息已初始化\", this.expertInfo);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn(\"localStorage中未找到expertInfo\");\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"初始化专家信息失败:\", error);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 初始化响应文件页码信息\r\n\t\tinitEntDocResponsePage() {\r\n\t\t\tthis.entDocResponsePage = JSON.parse(localStorage.getItem(\"entDocResponsePage\"));\r\n\t\t},\r\n\t\t// 初始化采购文件页码信息\r\n\t\tinitEntDocProcurementPage() {\r\n\t\t\tthis.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\"));\r\n\t\t},\r\n\t\t// 加载供应商下拉选项\r\n\t\tloadSupplierOptions() {\r\n\t\t\tsupplierInfo({ projectId: this.$route.query.projectId }).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\tthis.supplierOptions = response.rows.filter(item => item.isAbandonedBid === 0); // 过滤掉被放弃的投标\r\n\t\t\t\t\tconsole.log(this.supplierOptions);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 加载评分体系及初始化评分项状态\r\n\t\tloadScoringSystem() {\r\n\t\t\tapprovalProcess(this.$route.query.projectId, this.expertInfo.resultId).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t// 文件列表\r\n\t\t\t\t\tthis.attachmentsList = response.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n\r\n\t\t\t\t\tthis.scoringSystem = response.data.scoringMethodUinfo.scoringMethodItems.find(\r\n\t\t\t\t\t\titem => item.scoringMethodItemId == this.$route.query.scoringMethodItemId\r\n\t\t\t\t\t); // 获取当前评分项\r\n\t\t\t\t\tlocalStorage.setItem(\r\n\t\t\t\t\t\t\"evalProjectEvaluationProcess\",\r\n\t\t\t\t\t\tJSON.stringify(this.scoringSystem.evalProjectEvaluationProcess)\r\n\t\t\t\t\t); // 保存评分体系\r\n\t\t\t\t\tconsole.log(this.scoringSystem);\r\n\t\t\t\t\tthis.initRatingStateMapBySystem(); // 初始化评分项状态\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 初始化评分项状态（根据评分体系）\r\n\t\tinitRatingStateMapBySystem() {\r\n\t\t\tthis.ratingStateMap = this.scoringSystem.uitems.reduce((acc, item) => {\r\n\t\t\t\tacc[item.entMethodItemId] = { state: null, reason: \"\" };\r\n\t\t\t\treturn acc;\r\n\t\t\t}, {}); // 初始化评分项状态\r\n\t\t},\r\n\t\t// 加载文件信息\r\n\t\tloadFiles() {\r\n\t\t\tfilesById(this.$route.query.projectId).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\tthis.fileInfo = response.data; // 文件信息\r\n\t\t\t\t\tif (this.fileInfo.tenderNoticeFilePath) {\r\n\t\t\t\t\t\tthis.procurementPdfUrl = this.fileInfo.tenderNoticeFilePath; // 采购文件\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 不自动设置响应文件URL，只在需要时才设置\r\n\t\t\t\t\t// if (this.fileInfo.file) {\r\n\t\t\t\t\t// \tthis.responsePdfUrl = this.fileInfo.file[0]; // 响应文件\r\n\t\t\t\t\t// }\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// ========== 供应商相关 ==========\r\n\t\t/**\r\n\t\t * 供应商切换时处理\r\n\t\t * @param {string} supplierName 供应商名称\r\n\t\t */\r\n\t\thandleSupplierChange(supplierName) {\r\n\t\t\tthis.isResponseVisible = true\r\n\t\t\tif (Object.keys(this.selectedSupplier).length !== 0) {\r\n\t\t\t\tthis.saveRatingTemp();\r\n\t\t\t}\r\n\t\t\tthis.selectedSupplier = this.supplierOptions.find(item => item.bidderName === supplierName) || {}; // 获取当前供应商\r\n\t\t\tthis.supplierFactorPageMap = this.factorsPageMap[this.selectedSupplier.bidderId] || {}; // 获取当前供应商评分项页码\r\n\t\t\tthis.loadSupplierFactorDetail(supplierName); // 加载当前供应商评分项详情\r\n\t\t\tthis.loadSupplierCheckResult(); // 加载当前供应商系统初验结果\r\n\t\t\tthis.showResponseFile(); // 显示响应文件\r\n\t\t},\r\n\t\t// 加载当前供应商评分项详情\r\n\t\tloadSupplierFactorDetail(bidderName) {\r\n\t\t\tthis.clearRatingStateMap();\r\n\t\t\t\r\n\t\t\tconst detailData = {\r\n\t\t\t\texpertResultId: this.expertInfo.resultId,\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tscoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n\t\t\t};\r\n\t\t\tgetDetailByPsxx(detailData).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\tthis.factorDetailList = response.data;\r\n\t\t\t\t\tconst factor = this.factorDetailList.find(item => item.bidderName === bidderName)?.evalExpertEvaluationDetails;\r\n\t\t\t\t\tif (factor) {\r\n\t\t\t\t\t\tthis.setRatingStateMapByFactor(factor);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.clearRatingStateMap();\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 根据评分详情设置评分项状态\r\n\t\tsetRatingStateMapByFactor(factor) {\r\n\t\t\tfactor.forEach(item => {\r\n\t\t\t\tthis.ratingStateMap[item.scoringMethodUitemId].reason = item.evaluationRemark;\r\n\t\t\t\tthis.ratingStateMap[item.scoringMethodUitemId].state = item.evaluationResult;\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 加载当前供应商系统初验结果\r\n\t\tloadSupplierCheckResult() {\r\n\t\t\tconst reviewData = {\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tentId: this.selectedSupplier.bidderId,\r\n\t\t\t};\r\n\t\t\tresDocReviewFactorsDecision(reviewData).then((res) => {\r\n\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\tthis.checkResult = res.data;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 初始化评分项状态（清空）\r\n\t\tclearRatingStateMap() {\r\n\t\t\tObject.keys(this.ratingStateMap).forEach(key => {\r\n\t\t\t\tthis.ratingStateMap[key].state = null;\r\n\t\t\t\tthis.ratingStateMap[key].reason = \"\";\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// ========== 文件相关 ==========\r\n\t\t/**\r\n\t\t * 显示响应文件\r\n\t\t */\r\n\t\tshowResponseFile() {\r\n\t\t\tif (Object.keys(this.selectedSupplier).length === 0) {\r\n\t\t\t\tthis.$message.warning(\"请选择供应商\");\r\n\t\t\t} else {\r\n\t\t\t\tthis.activeButton = 'response'; // 设置当前激活按钮\r\n\t\t\t\tthis.isDoubleView = false; // 关闭对比\r\n\t\t\t\tthis.isProcurementVisible = false; // 关闭采购文件\r\n\t\t\t\tthis.isResponseVisible = true; // 显示响应文件\r\n\t\t\t\t\r\n\t\t\t\t// 🎯 只在用户点击时才设置响应文件URL，开始渲染\r\n\t\t\t\tthis.responsePdfUrl = this.fileInfo.file[this.selectedSupplier.bidderId]; // 响应文件\r\n\t\t\t\t\r\n\t\t\t\t// 右侧评分项显示为响应文件的评分项\r\n\t\t\t\tif (Object.keys(this.selectedSupplier).length !== 0) {\r\n\t\t\t\t\tthis.saveRatingTemp();\r\n\t\t\t\t}\r\n\t\t\t\tthis.selectedSupplier = this.supplierOptions.find(item => item.bidderName === this.selectedSupplier.bidderName) || {}; // 获取当前供应商\r\n\t\t\t\tthis.supplierFactorPageMap = this.factorsPageMap[this.selectedSupplier.bidderId] || {}; // 获取当前供应商评分项页码\r\n\t\t\t\tthis.loadSupplierFactorDetail(this.selectedSupplier.bidderName); // 加载当前供应商评分项详情\r\n\t\t\t\tthis.loadSupplierCheckResult(); // 加载当前供应商系统初验结果\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 文件对比显示\r\n\t\t */\r\n\t\tshowFileContrast() {\r\n\t\t\tif (Object.keys(this.selectedSupplier).length === 0) {\r\n\t\t\t\tthis.$message.warning(\"请选择供应商\");\r\n\t\t\t} else {\r\n\t\t\t\tthis.activeButton = 'contrast'; // 设置当前激活按钮\r\n\t\t\t\tthis.isDoubleView = true;\r\n\t\t\t\tthis.isProcurementVisible = true;\r\n\t\t\t\tthis.isResponseVisible = true;\r\n\t\t\t\t\r\n\t\t\t\t// 🎯 只在用户点击对比时才设置响应文件URL，开始渲染\r\n\t\t\t\tthis.responsePdfUrl = this.fileInfo.file[this.selectedSupplier.bidderId]; // 响应文件\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 查看采购文件\r\n\t\t */\r\n\t\tshowProcurementFile() {\r\n\t\t\tthis.activeButton = 'procurement'; // 设置当前激活按钮\r\n\t\t\tthis.isDoubleView = false;\r\n\t\t\tthis.isResponseVisible = false;\r\n\t\t\tthis.isProcurementVisible = true;\r\n\t\t\t// 右侧评分项显示为采购文件的评分项\r\n\t\t\tlet pageProcurementArr = [];\r\n\t\t\tfor (let item in this.entDocProcurementPage){\r\n\t\t\t\tpageProcurementArr.push({\r\n\t\t\t\t\titemName: item,\r\n\t\t\t\t\tjumpToPage: this.entDocProcurementPage[item]\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconsole.log(this.scoringSystem.uitems);\r\n\t\t\tconsole.log(pageProcurementArr)\r\n\t\t\tthis.pageProcurement = [];\r\n\t\t\tfor (let i = 0; i < this.scoringSystem.uitems.length;i++){\r\n\t\t\t\tfor (let j = 0; j < pageProcurementArr.length;j++){\r\n\t\t\t\t\tif (this.scoringSystem.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n\t\t\t\t\t\tthis.pageProcurement.push({...this.scoringSystem.uitems[i],...pageProcurementArr[j]});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tconsole.log(this.pageProcurement)\r\n\t\t},\r\n\r\n\t\t// ========== 页面跳转相关 ==========\r\n\t\t/**\r\n\t\t * 跳转到评分项对应页码\r\n\t\t * @param {Object} factorItem 评分项对象\r\n\t\t */\r\n\t\tjumpToFactorPage(factorItem) {\r\n\t\t\t// 检查PDF是否渲染完成\r\n\t\t\tif (!this.canJumpToPage()) {\r\n\t\t\t\tthis.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.selectedFactorNode = factorItem; // 设置当前选中因子\r\n\r\n\t\t\t// 如果只显示采购文件，使用采购文件页码信息\r\n\t\t\tif (this.isProcurementVisible && !this.isResponseVisible) {\r\n\t\t\t\tif (!this.procurementPdfRendered) {\r\n\t\t\t\t\tthis.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (factorItem.jumpToPage) {\r\n\t\t\t\t\tthis.$refs.procurement.skipPage(factorItem.jumpToPage); // 采购文件跳页\r\n\t\t\t\t} else if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n\t\t\t\t\tthis.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果显示响应文件或对比模式，需要选择供应商\r\n\t\t\tif (!this.supplierFactorPageMap || Object.keys(this.supplierFactorPageMap).length === 0) {\r\n\t\t\t\tthis.$message.warning(\"请先选择供应商\"); // 未选供应商提示\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 跳转到响应文件对应页码\r\n\t\t\tif (this.isResponseVisible && this.$refs.response) {\r\n\t\t\t\tif (!this.responsePdfRendered) {\r\n\t\t\t\t\tthis.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.response.skipPage(this.supplierFactorPageMap[this.selectedFactorNode.itemName]); // 响应文件跳页\r\n\t\t\t}\r\n\r\n\t\t\t// 跳转到采购文件对应页码\r\n\t\t\tif (this.isProcurementVisible && this.$refs.procurement) {\r\n\t\t\t\tif (!this.procurementPdfRendered) {\r\n\t\t\t\t\tthis.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n\t\t\t\tif (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n\t\t\t\t\tthis.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n\t\t\t\t\t// 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 检查是否可以跳转页面\r\n\t\t * @returns {boolean} 是否可以跳转\r\n\t\t */\r\n\t\tcanJumpToPage() {\r\n\t\t\t// 如果只显示采购文件\r\n\t\t\tif (this.isProcurementVisible && !this.isResponseVisible) {\r\n\t\t\t\treturn this.procurementPdfRendered;\r\n\t\t\t}\r\n\t\t\t// 如果只显示响应文件\r\n\t\t\tif (this.isResponseVisible && !this.isProcurementVisible) {\r\n\t\t\t\treturn this.responsePdfRendered;\r\n\t\t\t}\r\n\t\t\t// 如果对比模式（两个都显示）\r\n\t\t\tif (this.isResponseVisible && this.isProcurementVisible) {\r\n\t\t\t\treturn this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t\t}\r\n\t\t\treturn false;\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 处理PDF渲染状态变化\r\n\t\t * @param {boolean} isRendered 是否渲染完成\r\n\t\t * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t\t */\r\n\t\thandlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t\t\tif (pdfType === 'response') {\r\n\t\t\t\tthis.responsePdfRendered = isRendered;\r\n\t\t\t} else if (pdfType === 'procurement') {\r\n\t\t\t\tthis.procurementPdfRendered = isRendered;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (isRendered) {\r\n\t\t\t\tconsole.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 跳转到二次报价\r\n\t\t */\r\n\t\tgoToSecondOffer() {\r\n\t\t\tconst query = {\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tzjhm: this.$route.query.zjhm,\r\n\t\t\t\tscoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")),\r\n\t\t\t};\r\n\t\t\tthis.$router.push({ path: \"/secondOffer\", query });\r\n\t\t},\r\n\t\t/**\r\n\t\t * 跳转到询标\r\n\t\t */\r\n\t\tgoToBidInquiry() {\r\n\t\t\tconst query = {\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tzjhm: this.$route.query.zjhm,\r\n\t\t\t\tscoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")),\r\n\t\t\t};\r\n\t\t\tthis.$router.push({ path: \"/bidInquiry\", query });\r\n\t\t},\r\n\t\t/**\r\n\t\t * 获取因素对应页码\r\n\t\t */\r\n\t\tloadFactorsPageMap() {\r\n\t\t\tthis.factorsPageMap = JSON.parse(localStorage.getItem(\"entDocResponsePage\"));\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 下载文件\r\n\t\t * @param {Object} item - 文件对象\r\n\t\t */\r\n\t\tdownloadFile(item) {\r\n\t\t\tthis.$download.zip(item.filePath, item.fileName);\r\n\t\t},\r\n\r\n\t\t// ========== 悬停相关 ==========\r\n\t\t/**\r\n\t\t * 显示评分项悬浮框\r\n\t\t * @param {Object} factorItem 评分项对象\r\n\t\t */\r\n\t\tshowFactorTooltip(factorItem) {\r\n\t\t\tif (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\r\n\t\t\t// 清除之前的定时器\r\n\t\t\tif (this.tooltipTimer) {\r\n\t\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\t}\r\n\r\n\t\t\t// 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t\tthis.tooltipTimer = setTimeout(() => {\r\n\t\t\t\tthis.hoveredFactorNode = factorItem;\r\n\t\t\t}, 300); // 300ms延迟\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 隐藏评分项悬浮框\r\n\t\t */\r\n\t\thideFactorTooltip() {\r\n\t\t\t// 清除定时器\r\n\t\t\tif (this.tooltipTimer) {\r\n\t\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\t\tthis.tooltipTimer = null;\r\n\t\t\t}\r\n\r\n\t\t\t// 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.hoveredFactorNode = null;\r\n\t\t\t}, 100);\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t\t */\r\n\t\tclearTooltipTimer() {\r\n\t\t\tif (this.tooltipTimer) {\r\n\t\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\t\tthis.tooltipTimer = null;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n},\r\n\r\n\tmounted() {\r\n\t\tthis.initPage();\r\n\t\tthis.loadFactorsPageMap();\r\n\t},\r\n\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.main-container-one {\r\n\tmin-height: 57vh;\r\n\tdisplay: flex;\r\n}\r\n.left-panel {\r\n\tmin-height: 57vh;\r\n\twidth: 79%;\r\n}\r\n.header-bar {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tborder-bottom: 2px solid #176ADB;\r\n\tpadding: 15px 20px;\r\n}\r\n.header-title {\r\n\tdisplay: flex;\r\n\theight: 36px;\r\n\tfont-weight: 700;\r\n\tfont-size: 24px;\r\n\tcolor: #333;\r\n}\r\n.header-steps {\r\n\tdisplay: grid;\r\n\tjustify-items: center;\r\n\tposition: relative;\r\n\tbottom: -30px;\r\n}\r\n.steps-tip {\r\n\tfont-size: 12px;\r\n}\r\n.steps-img {\r\n\twidth: 80px;\r\n\theight: 30px;\r\n\tmargin-right: 20px;\r\n}\r\n.header-btns {\r\n\ttext-align: right;\r\n}\r\n.header-btns-group {\r\n\tmargin-top: 20px;\r\n}\r\n.item-button.main {\r\n\tbackground-color: #176ADB;\r\n\tcolor: #fff;\r\n\tborder: 1px solid #176ADB;\r\n}\r\n.pdf-container {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\theight: 100%;\r\n\tmin-height: 600px;\r\n}\r\n.pdf-view {\r\n\twidth: 49%;\r\n}\r\n.border-right {\r\n\tborder-right: 1px solid #176ADB;\r\n}\r\n.border-left {\r\n\tborder-left: 1px solid #176ADB;\r\n}\r\n.divider {\r\n\tmin-height: 57vh;\r\n\twidth: 1%;\r\n\tbackground-color: #F5F5F5;\r\n}\r\n.right-panel {\r\n\tmin-height: 57vh;\r\n\twidth: 20%;\r\n}\r\n.right-header {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tborder-bottom: 2px solid #176ADB;\r\n\tpadding: 15px 20px;\r\n}\r\n.right-content {\r\n\tpadding: 15px 20px;\r\n}\r\n.factor-item {\r\n\tmargin-bottom: 10px;\r\n}\r\n.factors {\r\n\tdisplay: flex;\r\n\tjustify-content: flex-start;\r\n\tflex-wrap: wrap;\r\n\talign-items: center;\r\n\tmargin-bottom: 10px;\r\n}\r\n.factor-title {\r\n\tcursor: pointer;\r\n\tfont-family: SourceHanSansSC-Bold;\r\n\tfont-weight: 700;\r\n\tfont-size: 16px;\r\n\tcolor: #333;\r\n\tletter-spacing: 0;\r\n\twidth: auto;\r\n\ttext-align: left;\r\n\ttransition: all 0.3s ease;\r\n\tpadding: 4px 8px;\r\n\tborder-radius: 4px;\r\n\r\n\t&:hover {\r\n\t\tbackground-color: #f0f8ff;\r\n\t\tcolor: #176ADB;\r\n\t\ttransform: translateX(2px);\r\n\t}\r\n}\r\n.factor-radio-group {\r\n\tdisplay: flex;\r\n\twidth: 100%;\r\n\tjustify-content: flex-end;\r\n}\r\n.factor-divider {\r\n\theight: 1px;\r\n\tbackground-color: #DCDFE6;\r\n\tmargin-top: 10px;\r\n}\r\n.right-btns {\r\n\tdisplay: flex;\r\n\tmargin: 32px 0;\r\n\tjustify-content: space-evenly;\r\n}\r\n.review-content {\r\n\ttext-align: left;\r\n\tfont-size: 14px;\r\n}\r\n.review-title {\r\n\tfont-family: SourceHanSansSC-Bold;\r\n\tfont-weight: 700;\r\n\tfont-size: 15px;\r\n\tcolor: #176ADB;\r\n\tletter-spacing: 0;\r\n}\r\n.review-html {\r\n\tpadding: 6px 30px;\r\n}\r\n.item-button {\r\n\tborder: 1px solid #979797;\r\n\twidth: 150px;\r\n\theight: 36px;\r\n\tmargin: 0 10px;\r\n\tfont-weight: 700;\r\n\tfont-size: 17px;\r\n\tborder-radius: 6px;\r\n\tcolor: #333;\r\n\t&:hover {\r\n\t\tcolor: #333;\r\n\t}\r\n}\r\n.qualification-blue-btn {\r\n\tbackground-color: #176ADB !important;\r\n\tcolor: #fff !important;\r\n\tborder: 1px solid #176ADB !important;\r\n}\r\n.qualification-blue-btn-active {\r\n\tbackground-color: #FF6B35 !important;\r\n\tcolor: #fff !important;\r\n\tborder: 1px solid #FF6B35 !important;\r\n\tbox-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.item-button-little {\r\n\twidth: 124px;\r\n\theight: 36px;\r\n\tfont-weight: 700;\r\n\tfont-size: 18px;\r\n\tcolor: #fff;\r\n\tbackground-color: #176ADB;\r\n\t&:hover {\r\n\t\tcolor: #fff;\r\n\t}\r\n}\r\n.text {\r\n\t::v-deep .el-textarea__inner {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder-radius: 0;\r\n\t\tborder: 1px solid #f5f5f5;\r\n\t}\r\n}\r\n\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n\t.fileItem {\r\n\t\ttransition: all 0.3s ease;\r\n\t\t&:hover {\r\n\t\t\ttransform: translateY(-2px);\r\n\t\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\t\t}\r\n\r\n\t\t::v-deep .el-card__body {\r\n\t\t\tpadding: 0;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA+MA,IAAAA,OAAA,GAAAC,OAAA;AAOA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,aAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,eAAA;MAAA;MACAC,aAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,UAAA;MAAA;MACAC,cAAA;MAAA;MACAC,QAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,YAAA;MAAA;;MAEAC,kBAAA;MAAA;;MAEAC,gBAAA;MAAA;MACAC,cAAA;MAAA;;MAEAC,qBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,eAAA;MAAA;;MAEAC,qBAAA;MAAA;MACAC,cAAA;MAAA;MACAC,iBAAA;MAAA;;MAEA;MACAC,YAAA;MAAA;;MAEA;MACAC,mBAAA;MAAA;MACAC,sBAAA;MAAA;;MAEAC,aAAA;MAAA;MACAC,YAAA;QAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACAC,WAAA;MAAA;MACAC,gBAAA;QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;MACAC,iBAAA;MAAA;MACAC,YAAA;IACA;EACA;EAEAC,OAAA;IACA;IACA;AACA;AACA;AACA;AACA;IACAC,mBAAA,WAAAA,oBAAAC,UAAA;MACA,UAAAN,WAAA,IAAAO,MAAA,CAAAC,IAAA,MAAAR,WAAA,EAAAS,MAAA;MACA,IAAAC,KAAA;MACA,IAAAC,GAAA,QAAAZ,YAAA,CAAAO,UAAA;MACA,IAAAK,GAAA;QACAD,KAAA,QAAAV,WAAA,CAAAW,GAAA;QACA,IAAAL,UAAA,eAAAI,KAAA;UACAA,KAAA,QAAAV,WAAA;QACA;MACA;MACA,IAAAU,KAAA;QACA,KAAAT,gBAAA,CAAAK,UAAA;MACA;QACAI,KAAA;QACA,KAAAT,gBAAA,CAAAK,UAAA;MACA;MACA,OAAAI,KAAA;IACA;IAEA;AACA;AACA;AACA;IACAE,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAC,OAAA,EACA,KAAAvC,aAAA,CAAAwC,MAAA;QAAAC,KAAA;MAAA;QAAA,KAAAJ,SAAA,CAAAK,CAAA,MAAAD,KAAA,GAAAJ,SAAA,CAAAM,CAAA,IAAAC,IAAA;UAAA,IAAAC,IAAA,GAAAJ,KAAA,CAAAK,KAAA;UACA,IAAAZ,KAAA,QAAA7B,cAAA,CAAAwC,IAAA,CAAAE,eAAA,EAAAb,KAAA;UACA,IAAAc,MAAA,QAAA3C,cAAA,CAAAwC,IAAA,CAAAE,eAAA,EAAAC,MAAA;UACA;UACA,IAAAd,KAAA,aAAAA,KAAA;YACA;YACA;UACA;UACA;UACA,IAAAA,KAAA,cAAAc,MAAA,IAAAA,MAAA,CAAAC,IAAA;YACA,KAAAC,QAAA,CAAAC,OAAA,IAAAC,MAAA,CAAAP,IAAA,CAAAQ,QAAA;YACA;UACA;QACA;MAAA,SAAAC,GAAA;QAAAjB,SAAA,CAAAkB,CAAA,CAAAD,GAAA;MAAA;QAAAjB,SAAA,CAAAmB,CAAA;MAAA;MACA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAA;MACA,IAAAC,UAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAxD,cAAA;MACA,IAAAP,IAAA;MAAA,IAAAgE,UAAA,OAAAxB,2BAAA,CAAAC,OAAA,EACA,KAAAvC,aAAA,CAAAwC,MAAA;QAAAuB,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAApB,CAAA,MAAAqB,MAAA,GAAAD,UAAA,CAAAnB,CAAA,IAAAC,IAAA;UAAA,IAAAC,IAAA,GAAAkB,MAAA,CAAAjB,KAAA;UAAA;UACA,IAAAkB,MAAA,GAAAnB,IAAA,CAAAE,eAAA;UACA,IAAAkB,gBAAA,GAAAP,UAAA,CAAAM,MAAA,EAAA9B,KAAA;UACA,IAAA+B,gBAAA,aAAAA,gBAAA;UACA;UACA,IAAAC,gBAAA,GAAAR,UAAA,CAAAM,MAAA,EAAAhB,MAAA;UACAlD,IAAA,CAAAqE,IAAA;YACAC,oBAAA,EAAAJ,MAAA;YAAA;YACAK,cAAA,OAAAjE,UAAA,CAAAkE,QAAA;YAAA;YACAC,KAAA,OAAApE,gBAAA,CAAAqE,QAAA;YAAA;YACAP,gBAAA,EAAAA,gBAAA;YAAA;YACAC,gBAAA,EAAAA,gBAAA;UACA;QACA;MAAA,SAAAZ,GAAA;QAAAQ,UAAA,CAAAP,CAAA,CAAAD,GAAA;MAAA;QAAAQ,UAAA,CAAAN,CAAA;MAAA;MACA,OAAA1D,IAAA;IACA;IAEA;AACA;AACA;AACA;IACA2E,cAAA,WAAAA,eAAA;MAAA,IAAAC,KAAA;MACA;MACA,UAAAtC,kBAAA;QACA,OAAAuC,OAAA,CAAAC,OAAA;UAAAC,IAAA;UAAAC,OAAA;QAAA;MACA;MAEA,IAAAhF,IAAA,QAAA2D,gBAAA;MACA,IAAA3D,IAAA,CAAAmC,MAAA;QACA,WAAA8C,sBAAA,EAAAjF,IAAA,EAAAkF,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAJ,IAAA;YACAH,KAAA,CAAAxB,QAAA,CAAA4B,OAAA;YACA;cAAAD,IAAA;cAAAC,OAAA;YAAA;UACA;YACAJ,KAAA,CAAAxB,QAAA,CAAAC,OAAA,CAAA8B,QAAA,CAAAC,GAAA;YACA;cAAAL,IAAA,EAAAI,QAAA,CAAAJ,IAAA;cAAAC,OAAA;YAAA;UACA;QACA,GAAAK,KAAA,WAAAC,KAAA;UACAV,KAAA,CAAAxB,QAAA,CAAAkC,KAAA;UACA;YAAAP,IAAA;YAAAC,OAAA;UAAA;QACA;MACA;QACA,OAAAH,OAAA,CAAAC,OAAA;UAAAC,IAAA;UAAAC,OAAA;QAAA;MACA;IACA;IAEA;AACA;AACA;IACAO,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAb,cAAA,GAAAO,IAAA,WAAAO,UAAA;QACA;QACA,KAAAA,UAAA,IAAAA,UAAA,CAAAT,OAAA;UACA;QACA;QACAQ,MAAA,CAAAE,oBAAA;MACA;IACA;IAEA;IACAA,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,IAAA3F,IAAA;QACA4F,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QACArB,cAAA,OAAAjE,UAAA,CAAAkE,QAAA;QACAuB,mBAAA,OAAAF,MAAA,CAAAC,KAAA,CAAAC;MACA;MACA,IAAAC,0BAAA,EAAAhG,IAAA,EAAAkF,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAJ,IAAA;UACAY,MAAA,CAAAM,uBAAA;UACAN,MAAA,CAAAO,KAAA;QACA;UACAP,MAAA,CAAAvC,QAAA,CAAAC,OAAA,CAAA8B,QAAA,CAAAC,GAAA;QACA;MACA;IACA;IAEA;IACAa,uBAAA,WAAAA,wBAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,MAAA;QACAC,qBAAA,EAAAxC,IAAA,CAAAC,KAAA,CAAAwC,YAAA,CAAAC,OAAA,yBAAAF,qBAAA;QACAG,SAAA;MACA;MACA,IAAAC,qCAAA,EAAAL,MAAA,EAAAlB,IAAA,WAAAwB,GAAA;QACA,IAAAA,GAAA,CAAA3B,IAAA;UACAoB,MAAA,CAAA/C,QAAA,CAAA4B,OAAA;QACA;MACA;IACA;IAEA;IACA;AACA;AACA;IACA2B,QAAA,WAAAA,SAAA;MACA,KAAAC,cAAA;MACA,KAAAC,sBAAA;MACA,KAAAC,yBAAA;MACA,KAAAC,mBAAA;MACA,KAAAC,iBAAA;MACA,KAAAC,SAAA;IACA;IACA;IACAL,cAAA,WAAAA,eAAA;MACA;QACA,IAAAM,aAAA,GAAAZ,YAAA,CAAAC,OAAA;QACA,IAAAW,aAAA;UACA,KAAA5G,UAAA,GAAAuD,IAAA,CAAAC,KAAA,CAAAoD,aAAA;UACAC,OAAA,CAAAC,GAAA,kBAAA9G,UAAA;QACA;UACA6G,OAAA,CAAAE,IAAA;QACA;MACA,SAAA/B,KAAA;QACA6B,OAAA,CAAA7B,KAAA,eAAAA,KAAA;MACA;IACA;IACA;IACAuB,sBAAA,WAAAA,uBAAA;MACA,KAAAjG,kBAAA,GAAAiD,IAAA,CAAAC,KAAA,CAAAwC,YAAA,CAAAC,OAAA;IACA;IACA;IACAO,yBAAA,WAAAA,0BAAA;MACA,KAAA/F,qBAAA,GAAA8C,IAAA,CAAAC,KAAA,CAAAwC,YAAA,CAAAC,OAAA;IACA;IACA;IACAQ,mBAAA,WAAAA,oBAAA;MAAA,IAAAO,MAAA;MACA,IAAAC,oBAAA;QAAA3B,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF;MAAA,GAAAV,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAJ,IAAA;UACAuC,MAAA,CAAArH,eAAA,GAAAkF,QAAA,CAAAqC,IAAA,CAAAC,MAAA,WAAA1E,IAAA;YAAA,OAAAA,IAAA,CAAA2E,cAAA;UAAA;UACAP,OAAA,CAAAC,GAAA,CAAAE,MAAA,CAAArH,eAAA;QACA;UACAqH,MAAA,CAAAlE,QAAA,CAAAC,OAAA,CAAA8B,QAAA,CAAAC,GAAA;QACA;MACA;IACA;IACA;IACA4B,iBAAA,WAAAA,kBAAA;MAAA,IAAAW,MAAA;MACA,IAAAC,uBAAA,OAAA/B,MAAA,CAAAC,KAAA,CAAAF,SAAA,OAAAtF,UAAA,CAAAkE,QAAA,EAAAU,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAJ,IAAA;UACA;UACA4C,MAAA,CAAA1G,eAAA,GAAAkE,QAAA,CAAAnF,IAAA,CAAA6H,gBAAA,CAAAC,WAAA,CAAAL,MAAA,WAAA1E,IAAA;YAAA,OAAAA,IAAA,CAAAgF,QAAA;UAAA;UAEAJ,MAAA,CAAAzH,aAAA,GAAAiF,QAAA,CAAAnF,IAAA,CAAAgI,kBAAA,CAAAC,kBAAA,CAAAC,IAAA,CACA,UAAAnF,IAAA;YAAA,OAAAA,IAAA,CAAAgD,mBAAA,IAAA4B,MAAA,CAAA9B,MAAA,CAAAC,KAAA,CAAAC,mBAAA;UAAA,CACA;UACAO,YAAA,CAAA6B,OAAA,CACA,gCACAtE,IAAA,CAAAE,SAAA,CAAA4D,MAAA,CAAAzH,aAAA,CAAAkI,4BAAA,CACA;UACAjB,OAAA,CAAAC,GAAA,CAAAO,MAAA,CAAAzH,aAAA;UACAyH,MAAA,CAAAU,0BAAA;QACA;UACAV,MAAA,CAAAvE,QAAA,CAAAC,OAAA,CAAA8B,QAAA,CAAAC,GAAA;QACA;MACA;IACA;IACA;IACAiD,0BAAA,WAAAA,2BAAA;MACA,KAAA9H,cAAA,QAAAL,aAAA,CAAAwC,MAAA,CAAA4F,MAAA,WAAAC,GAAA,EAAAxF,IAAA;QACAwF,GAAA,CAAAxF,IAAA,CAAAE,eAAA;UAAAb,KAAA;UAAAc,MAAA;QAAA;QACA,OAAAqF,GAAA;MACA;IACA;IACA;IACAtB,SAAA,WAAAA,UAAA;MAAA,IAAAuB,MAAA;MACA,IAAAC,iBAAA,OAAA5C,MAAA,CAAAC,KAAA,CAAAF,SAAA,EAAAV,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAJ,IAAA;UACAyD,MAAA,CAAAhI,QAAA,GAAA2E,QAAA,CAAAnF,IAAA;UACA,IAAAwI,MAAA,CAAAhI,QAAA,CAAAkI,oBAAA;YACAF,MAAA,CAAApH,iBAAA,GAAAoH,MAAA,CAAAhI,QAAA,CAAAkI,oBAAA;UACA;UACA;UACA;UACA;UACA;QACA;UACAF,MAAA,CAAApF,QAAA,CAAAC,OAAA,CAAA8B,QAAA,CAAAC,GAAA;QACA;MACA;IACA;IAEA;IACA;AACA;AACA;AACA;IACAuD,oBAAA,WAAAA,qBAAAC,YAAA;MACA,KAAAnI,iBAAA;MACA,IAAAwB,MAAA,CAAAC,IAAA,MAAA7B,gBAAA,EAAA8B,MAAA;QACA,KAAAwC,cAAA;MACA;MACA,KAAAtE,gBAAA,QAAAJ,eAAA,CAAAiI,IAAA,WAAAnF,IAAA;QAAA,OAAAA,IAAA,CAAA8F,UAAA,KAAAD,YAAA;MAAA;MACA,KAAA1H,qBAAA,QAAAJ,cAAA,MAAAT,gBAAA,CAAAqE,QAAA;MACA,KAAAoE,wBAAA,CAAAF,YAAA;MACA,KAAAG,uBAAA;MACA,KAAAC,gBAAA;IACA;IACA;IACAF,wBAAA,WAAAA,yBAAAD,UAAA;MAAA,IAAAI,MAAA;MACA,KAAAC,mBAAA;MAEA,IAAAC,UAAA;QACA5E,cAAA,OAAAjE,UAAA,CAAAkE,QAAA;QACAoB,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QACAG,mBAAA,OAAAF,MAAA,CAAAC,KAAA,CAAAC;MACA;MACA,IAAAqD,uBAAA,EAAAD,UAAA,EAAAjE,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAJ,IAAA;UAAA,IAAAsE,qBAAA;UACAJ,MAAA,CAAApI,gBAAA,GAAAsE,QAAA,CAAAnF,IAAA;UACA,IAAAsJ,MAAA,IAAAD,qBAAA,GAAAJ,MAAA,CAAApI,gBAAA,CAAAqH,IAAA,WAAAnF,IAAA;YAAA,OAAAA,IAAA,CAAA8F,UAAA,KAAAA,UAAA;UAAA,gBAAAQ,qBAAA,uBAAAA,qBAAA,CAAAE,2BAAA;UACA,IAAAD,MAAA;YACAL,MAAA,CAAAO,yBAAA,CAAAF,MAAA;UACA;YACAL,MAAA,CAAAC,mBAAA;UACA;QACA;UACAD,MAAA,CAAA7F,QAAA,CAAAC,OAAA,CAAA8B,QAAA,CAAAC,GAAA;QACA;MACA;IACA;IACA;IACAoE,yBAAA,WAAAA,0BAAAF,MAAA;MAAA,IAAAG,MAAA;MACAH,MAAA,CAAAI,OAAA,WAAA3G,IAAA;QACA0G,MAAA,CAAAlJ,cAAA,CAAAwC,IAAA,CAAAuB,oBAAA,EAAApB,MAAA,GAAAH,IAAA,CAAAqB,gBAAA;QACAqF,MAAA,CAAAlJ,cAAA,CAAAwC,IAAA,CAAAuB,oBAAA,EAAAlC,KAAA,GAAAW,IAAA,CAAAoB,gBAAA;MACA;IACA;IACA;IACA4E,uBAAA,WAAAA,wBAAA;MAAA,IAAAY,MAAA;MACA,IAAAC,UAAA;QACAhE,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QACAnB,KAAA,OAAApE,gBAAA,CAAAqE;MACA;MACA,IAAAmF,oCAAA,EAAAD,UAAA,EAAA1E,IAAA,WAAAwB,GAAA;QACA,IAAAA,GAAA,CAAA3B,IAAA;UACA4E,MAAA,CAAAjI,WAAA,GAAAgF,GAAA,CAAA1G,IAAA;QACA;MACA;IACA;IACA;IACAkJ,mBAAA,WAAAA,oBAAA;MAAA,IAAAY,MAAA;MACA7H,MAAA,CAAAC,IAAA,MAAA3B,cAAA,EAAAmJ,OAAA,WAAArH,GAAA;QACAyH,MAAA,CAAAvJ,cAAA,CAAA8B,GAAA,EAAAD,KAAA;QACA0H,MAAA,CAAAvJ,cAAA,CAAA8B,GAAA,EAAAa,MAAA;MACA;IACA;IAEA;IACA;AACA;AACA;IACA8F,gBAAA,WAAAA,iBAAA;MAAA,IAAAe,OAAA;MACA,IAAA9H,MAAA,CAAAC,IAAA,MAAA7B,gBAAA,EAAA8B,MAAA;QACA,KAAAiB,QAAA,CAAAC,OAAA;MACA;QACA,KAAAhC,YAAA;QACA,KAAAV,YAAA;QACA,KAAAD,oBAAA;QACA,KAAAD,iBAAA;;QAEA;QACA,KAAAU,cAAA,QAAAX,QAAA,CAAAwJ,IAAA,MAAA3J,gBAAA,CAAAqE,QAAA;;QAEA;QACA,IAAAzC,MAAA,CAAAC,IAAA,MAAA7B,gBAAA,EAAA8B,MAAA;UACA,KAAAwC,cAAA;QACA;QACA,KAAAtE,gBAAA,QAAAJ,eAAA,CAAAiI,IAAA,WAAAnF,IAAA;UAAA,OAAAA,IAAA,CAAA8F,UAAA,KAAAkB,OAAA,CAAA1J,gBAAA,CAAAwI,UAAA;QAAA;QACA,KAAA3H,qBAAA,QAAAJ,cAAA,MAAAT,gBAAA,CAAAqE,QAAA;QACA,KAAAoE,wBAAA,MAAAzI,gBAAA,CAAAwI,UAAA;QACA,KAAAE,uBAAA;MACA;IACA;IACA;AACA;AACA;IACAkB,gBAAA,WAAAA,iBAAA;MACA,IAAAhI,MAAA,CAAAC,IAAA,MAAA7B,gBAAA,EAAA8B,MAAA;QACA,KAAAiB,QAAA,CAAAC,OAAA;MACA;QACA,KAAAhC,YAAA;QACA,KAAAV,YAAA;QACA,KAAAD,oBAAA;QACA,KAAAD,iBAAA;;QAEA;QACA,KAAAU,cAAA,QAAAX,QAAA,CAAAwJ,IAAA,MAAA3J,gBAAA,CAAAqE,QAAA;MACA;IACA;IACA;AACA;AACA;IACAwF,mBAAA,WAAAA,oBAAA;MACA,KAAA7I,YAAA;MACA,KAAAV,YAAA;MACA,KAAAF,iBAAA;MACA,KAAAC,oBAAA;MACA;MACA,IAAAyJ,kBAAA;MACA,SAAApH,IAAA,SAAAhC,qBAAA;QACAoJ,kBAAA,CAAA9F,IAAA;UACAd,QAAA,EAAAR,IAAA;UACAqH,UAAA,OAAArJ,qBAAA,CAAAgC,IAAA;QACA;MACA;MAEAoE,OAAA,CAAAC,GAAA,MAAAlH,aAAA,CAAAwC,MAAA;MACAyE,OAAA,CAAAC,GAAA,CAAA+C,kBAAA;MACA,KAAAnJ,eAAA;MACA,SAAAqJ,CAAA,MAAAA,CAAA,QAAAnK,aAAA,CAAAwC,MAAA,CAAAP,MAAA,EAAAkI,CAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,GAAAH,kBAAA,CAAAhI,MAAA,EAAAmI,CAAA;UACA,SAAApK,aAAA,CAAAwC,MAAA,CAAA2H,CAAA,EAAA9G,QAAA,IAAA4G,kBAAA,CAAAG,CAAA,EAAA/G,QAAA;YACA,KAAAvC,eAAA,CAAAqD,IAAA,KAAAkG,cAAA,CAAA9H,OAAA,MAAA8H,cAAA,CAAA9H,OAAA,WAAAvC,aAAA,CAAAwC,MAAA,CAAA2H,CAAA,IAAAF,kBAAA,CAAAG,CAAA;UACA;QACA;MACA;MACAnD,OAAA,CAAAC,GAAA,MAAApG,eAAA;IACA;IAEA;IACA;AACA;AACA;AACA;IACAwJ,gBAAA,WAAAA,iBAAAC,UAAA;MACA;MACA,UAAAC,aAAA;QACA,KAAAtH,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAlD,kBAAA,GAAAsK,UAAA;;MAEA;MACA,SAAA/J,oBAAA,UAAAD,iBAAA;QACA,UAAAc,sBAAA;UACA,KAAA6B,QAAA,CAAAC,OAAA;UACA;QACA;QACA,IAAAoH,UAAA,CAAAL,UAAA;UACA,KAAAO,KAAA,CAAAC,WAAA,CAAAC,QAAA,CAAAJ,UAAA,CAAAL,UAAA;QACA,gBAAArJ,qBAAA,SAAAA,qBAAA,CAAA0J,UAAA,CAAAlH,QAAA;UACA,KAAAoH,KAAA,CAAAC,WAAA,CAAAC,QAAA,MAAA9J,qBAAA,CAAA0J,UAAA,CAAAlH,QAAA;QACA;QACA;MACA;;MAEA;MACA,UAAArC,qBAAA,IAAAe,MAAA,CAAAC,IAAA,MAAAhB,qBAAA,EAAAiB,MAAA;QACA,KAAAiB,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,SAAA5C,iBAAA,SAAAkK,KAAA,CAAAxF,QAAA;QACA,UAAA7D,mBAAA;UACA,KAAA8B,QAAA,CAAAC,OAAA;UACA;QACA;QACA,KAAAsH,KAAA,CAAAxF,QAAA,CAAA0F,QAAA,MAAA3J,qBAAA,MAAAf,kBAAA,CAAAoD,QAAA;MACA;;MAEA;MACA,SAAA7C,oBAAA,SAAAiK,KAAA,CAAAC,WAAA;QACA,UAAArJ,sBAAA;UACA,KAAA6B,QAAA,CAAAC,OAAA;UACA;QACA;QACA;QACA,SAAAtC,qBAAA,SAAAA,qBAAA,CAAA0J,UAAA,CAAAlH,QAAA;UACA,KAAAoH,KAAA,CAAAC,WAAA,CAAAC,QAAA,MAAA9J,qBAAA,CAAA0J,UAAA,CAAAlH,QAAA;QACA;UACA;UACA;QAAA;MAEA;IACA;IAEA;AACA;AACA;AACA;IACAmH,aAAA,WAAAA,cAAA;MACA;MACA,SAAAhK,oBAAA,UAAAD,iBAAA;QACA,YAAAc,sBAAA;MACA;MACA;MACA,SAAAd,iBAAA,UAAAC,oBAAA;QACA,YAAAY,mBAAA;MACA;MACA;MACA,SAAAb,iBAAA,SAAAC,oBAAA;QACA,YAAAY,mBAAA,SAAAC,sBAAA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAuJ,2BAAA,WAAAA,4BAAAC,UAAA,EAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAA1J,mBAAA,GAAAyJ,UAAA;MACA,WAAAC,OAAA;QACA,KAAAzJ,sBAAA,GAAAwJ,UAAA;MACA;MAEA,IAAAA,UAAA;QACA5D,OAAA,CAAAC,GAAA,IAAA9D,MAAA,CAAA0H,OAAA;MACA;IACA;IACA;AACA;AACA;IACAC,eAAA,WAAAA,gBAAA;MACA,IAAAnF,KAAA;QACAF,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QACAsF,IAAA,OAAArF,MAAA,CAAAC,KAAA,CAAAoF,IAAA;QACAnF,mBAAA,EAAAlC,IAAA,CAAAC,KAAA,CAAAwC,YAAA,CAAAC,OAAA;MACA;MACA,KAAA4E,OAAA,CAAA9G,IAAA;QAAA+G,IAAA;QAAAtF,KAAA,EAAAA;MAAA;IACA;IACA;AACA;AACA;IACAuF,cAAA,WAAAA,eAAA;MACA,IAAAvF,KAAA;QACAF,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QACAsF,IAAA,OAAArF,MAAA,CAAAC,KAAA,CAAAoF,IAAA;QACAnF,mBAAA,EAAAlC,IAAA,CAAAC,KAAA,CAAAwC,YAAA,CAAAC,OAAA;MACA;MACA,KAAA4E,OAAA,CAAA9G,IAAA;QAAA+G,IAAA;QAAAtF,KAAA,EAAAA;MAAA;IACA;IACA;AACA;AACA;IACAwF,kBAAA,WAAAA,mBAAA;MACA,KAAAxK,cAAA,GAAA+C,IAAA,CAAAC,KAAA,CAAAwC,YAAA,CAAAC,OAAA;IACA;IAEA;AACA;AACA;AACA;IACAgF,YAAA,WAAAA,aAAAxI,IAAA;MACA,KAAAyI,SAAA,CAAAC,GAAA,CAAA1I,IAAA,CAAA2I,QAAA,EAAA3I,IAAA,CAAA4I,QAAA;IACA;IAEA;IACA;AACA;AACA;AACA;IACAC,iBAAA,WAAAA,kBAAAnB,UAAA;MAAA,IAAAoB,OAAA;MACA,KAAApB,UAAA,CAAAqB,UAAA;;MAEA;MACA,SAAAjK,YAAA;QACAkK,YAAA,MAAAlK,YAAA;MACA;;MAEA;MACA,KAAAA,YAAA,GAAAmK,UAAA;QACAH,OAAA,CAAAjK,iBAAA,GAAA6I,UAAA;MACA;IACA;IAEA;AACA;AACA;IACAwB,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MACA;MACA,SAAArK,YAAA;QACAkK,YAAA,MAAAlK,YAAA;QACA,KAAAA,YAAA;MACA;;MAEA;MACAmK,UAAA;QACAE,OAAA,CAAAtK,iBAAA;MACA;IACA;IAEA;AACA;AACA;IACAuK,iBAAA,WAAAA,kBAAA;MACA,SAAAtK,YAAA;QACAkK,YAAA,MAAAlK,YAAA;QACA,KAAAA,YAAA;MACA;IACA;EAEA;EAEAuK,OAAA,WAAAA,QAAA;IACA,KAAAzF,QAAA;IACA,KAAA2E,kBAAA;EACA;EAEAe,aAAA,WAAAA,cAAA;IACA;IACA,SAAAxK,YAAA;MACAkK,YAAA,MAAAlK,YAAA;MACA,KAAAA,YAAA;IACA;EACA;AACA", "ignoreList": []}]}