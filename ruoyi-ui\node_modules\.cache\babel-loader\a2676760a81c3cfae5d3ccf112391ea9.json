{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business\\one.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business\\one.vue", "mtime": 1753923382623}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\babel.config.js", "mtime": 1750995480034}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_review", "require", "_detail", "_expertStatus", "data", "options", "scoringSystem", "selectNode", "supplier", "selectSupplier", "expertInfo", "defaultRatingArray", "file", "responseShow", "procurementShow", "double", "entDocResponsePage", "factorsPage", "bidderFactor", "responsePdf", "procurementPdf", "currentMaxScore", "activeButton", "entDocProcurementPage", "pageProcurement", "attachmentsList", "responsePdfRendered", "procurementPdfRendered", "srcList", "hoveredFactorNode", "tooltipTimer", "methods", "only<PERSON><PERSON><PERSON>", "event", "charCode", "which", "keyCode", "preventDefault", "handleScoreInput", "itemId", "value", "cleanValue", "replace", "parts", "split", "length", "slice", "join", "substring", "state", "validateScore", "init", "_this", "JSON", "parse", "localStorage", "getItem", "supplierInfo", "projectId", "$route", "query", "isAbandonedBid", "then", "response", "code", "rows", "filter", "item", "$message", "warning", "msg", "approvalProcess", "resultId", "busiTenderNotice", "attachments", "fileType", "scoringMethodUinfo", "scoringMethodItems", "find", "scoringMethodItemId", "setItem", "stringify", "evalProjectEvaluationProcess", "uitems", "reduce", "acc", "_", "index", "entMethodItemId", "reason", "console", "log", "$messgae", "filesById", "tenderNoticeFilePath", "undefined", "initExpertInfo", "itemString", "warn", "error", "handleChange", "_this2", "Object", "keys", "tmpSave", "bidderName", "bidderId", "expertResultId", "getDetailByPsxx", "factorList", "factor", "evalExpertEvaluationDetails", "map", "scoringMethodUitemId", "evaluationRemark", "evaluationResult", "for<PERSON>ach", "key", "showResponseFile", "inputValue", "parseFloat", "currentItem", "maxScore", "scoreLevel", "scoreLevels", "trim", "isNaN", "Math", "max", "apply", "_toConsumableArray2", "default", "score", "concat", "fileContrast", "showInfo", "canJumpToPage", "jumpToPage", "$refs", "procurement", "skipPage", "itemName", "initDefaultRatingArray", "_this3", "validateAllRatings", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "getMaxScore", "level", "includes", "toString", "err", "e", "f", "_this4", "Promise", "resolve", "success", "ratingArray", "push", "entId", "scoringFactors", "catch", "save", "_this5", "generatingSavedData", "submit", "_this6", "saveResult", "checkReviewSummary", "status", "evalExpertScoreInfoId", "evalState", "editEvalExpertScoreInfo", "res", "$emit", "viewPurchasing", "pageProcurementArr", "i", "j", "_objectSpread2", "jumpToProcurementPage", "handlePdfRenderStatusChange", "isRendered", "pdfType", "secondOffer", "zjhm", "$router", "path", "bidInquiry", "getFactorsPage", "downloadFile", "$download", "zip", "filePath", "fileName", "showFactorTooltip", "factorItem", "_this7", "itemRemark", "clearTimeout", "setTimeout", "hideFactorTooltip", "_this8", "clearTooltipTimer", "mounted", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/expertReview/business/one.vue"], "sourcesContent": ["<template>\r\n  <div class=\"business-review-container\">\r\n    <div class=\"business-review-main\">\r\n      <div class=\"business-review-header\">\r\n        <div class=\"business-review-title-group\">\r\n          <div class=\"business-review-title\">商务标评审</div>\r\n          <div class=\"business-review-step-group\">\r\n            <div class=\"business-review-step-text\">该页面操作说明</div>\r\n            <el-image class=\"business-review-step-img\" :src=\"srcList[0]\" :preview-src-list=\"srcList\">\r\n            </el-image>\r\n          </div>\r\n        </div>\r\n\t      \r\n\t      <!-- 文件列表 -->\r\n\t      <div class=\"fileList\" style=\"width: 200px; border-right: 1px solid #e6e6e6; border-left: 1px solid #e6e6e6; padding: 10px; overflow-y: auto;\">\r\n\t\t      <div style=\"font-weight: bold; margin-bottom: 10px; color: #333;\">响应文件附件下载</div>\r\n\t\t      <el-card\r\n\t\t\t      v-for=\"(item, index) in attachmentsList\"\r\n\t\t\t      :key=\"index\"\r\n\t\t\t      class=\"fileItem\"\r\n\t\t\t      shadow=\"hover\"\r\n\t\t\t      @click.native=\"downloadFile(item)\"\r\n\t\t\t      style=\"margin-bottom: 8px; cursor: pointer;\"\r\n\t\t      >\r\n\t\t\t      <div style=\"display: flex; align-items: center; padding: 5px;\">\r\n\t\t\t\t      <i class=\"el-icon-document\" style=\"margin-right: 8px; color: #409EFF;\"></i>\r\n\t\t\t\t      <span style=\"font-size: 12px; flex: 1; word-break: break-all;\">{{ item.fileName }}</span>\r\n\t\t\t\t      <i class=\"el-icon-download\" style=\"margin-left: 8px; color: #999;\"></i>\r\n\t\t\t      </div>\r\n\t\t      </el-card>\r\n\t      </div>\r\n\t      \r\n        <div class=\"business-review-header-btns\">\r\n          <el-button class=\"item-button\" @click=\"bidInquiry\">询标</el-button>\r\n          <el-button class=\"item-button\" v-if=\"expertInfo.expertLeader==1\" @click=\"secondOffer\">发起二次报价</el-button>\r\n          <div class=\"business-review-header-btns-group\">\r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'procurement' ? 'business-blue-btn-active' : 'business-blue-btn']\"\r\n              @click=\"viewPurchasing\">采购文件</el-button>\r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'response' ? 'business-blue-btn-active' : 'business-blue-btn']\"\r\n              @click=\"showResponseFile()\">响应文件</el-button>\r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'contrast' ? 'business-blue-btn-active' : 'business-blue-btn']\"\r\n              @click=\"fileContrast\">对比</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div style=\"height:82%\">\r\n        <!-- PDF预览区域 - 保持原始尺寸 -->\r\n        <div class=\"business-review-pdf-group\">\r\n\t        <div v-show=\"procurementShow\" :class=\"['business-review-pdf', double ? 'business-review-pdf-border-left' : '']\">\r\n\t\t        <!--            <pdfView ref=\"procurement\" :pdfurl=\"procurementPdf\" :uni_key=\"'procurement'\"></pdfView>-->\r\n\t\t        <PdfViewImproved ref=\"procurement\"  :pdfurl=\"procurementPdf\"  :page-height=\"800\" :buffer-size=\"2\"  @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"/>\r\n\t        \r\n\t        </div>\r\n\t        \r\n          <div v-show=\"responseShow\" :class=\"['business-review-pdf', double ? 'business-review-pdf-border-right' : '']\">\r\n<!--            <pdfView ref=\"response\" :pdfurl=\"responsePdf\" :uni_key=\"'response'\"></pdfView>-->\r\n\t          <PdfViewImproved ref=\"response\"  :pdfurl=\"responsePdf\"  :page-height=\"800\" :buffer-size=\"2\" @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"/>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"business-review-divider\"></div>\r\n    <div class=\"business-review-side\">\r\n      <div class=\"business-review-select-group\">\r\n        <el-select class=\"business-review-select\" v-model=\"supplier\" placeholder=\"请选择供应商\" @change=\"handleChange\">\r\n          <el-option v-for=\"item in options\" :key=\"item.bidderName\" :label=\"item.bidderName\" :value=\"item.bidderName\">\r\n          </el-option>\r\n        </el-select>\r\n      </div>\r\n      <div class=\"business-review-side-content\">\r\n        <!-- 响应文件评分项显示 -->\r\n\t      <template v-if=\"responseShow || double\">\r\n\t\t      <!-- PDF渲染状态提示 -->\r\n\t\t      <div v-if=\"!responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t\t      <i class=\"el-icon-loading\"></i>\r\n\t\t\t      <span>响应文件正在渲染中，请稍候...</span>\r\n\t\t      </div>\r\n\t\t      <div v-else class=\"render-status-tip success\">\r\n\t\t\t      <i class=\"el-icon-success\"></i>\r\n\t\t\t      <span>响应文件渲染完成，可以点击跳转</span>\r\n\t\t      </div>\r\n\t\t      <div v-for=\"(item, index) in scoringSystem.uitems\" :key=\"'response-' + index\"\r\n\t\t           class=\"factor-item business-review-factor-item\"\r\n\t\t           @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t      <!-- 悬浮框 -->\r\n\t\t\t      <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t           class=\"factor-tooltip\"\r\n\t\t\t           @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t      <div class=\"tooltip-header\">\r\n\t\t\t\t\t      <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t      <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t      </div>\r\n\t\t\t\t      <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t      </div>\r\n\t\t\t      \r\n\t\t\t      <div>\r\n\t\t\t\t      <div class=\"factors\">\r\n\t\t\t\t\t      <div class=\"business-review-factor-title-group\">\r\n\t\t\t\t\t\t      <div class=\"business-review-factor-title\" @click=\"showInfo(item)\">\r\n\t\t\t\t\t\t\t      {{ item.itemName }}\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t\t      <div class=\"business-review-factor-score-group\">\r\n\t\t\t\t\t\t      <div v-if=\"!(item.scoreLevel.length == 0 || item.scoreLevel == null || item.scoreLevel == undefined)\">\r\n\t\t\t\t\t\t\t      <el-radio v-for=\"(score,index) in item.scoreLevel.split(',')\" :key=\"index\" v-model=\"defaultRatingArray[item.entMethodItemId].state\" :label=\"score\"><span class=\"business-review-factor-score\">{{ score }}</span></el-radio>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t\t      <div v-else>\r\n\t\t\t\t\t\t\t      <el-input\r\n\t\t\t\t\t\t\t\t      placeholder=\"请输入分数\"\r\n\t\t\t\t\t\t\t\t      v-model=\"defaultRatingArray[item.entMethodItemId].state\"\r\n\t\t\t\t\t\t\t\t      @input=\"handleScoreInput(item.entMethodItemId, $event)\"\r\n\t\t\t\t\t\t\t\t      @keypress=\"onlyNumber\"\r\n\t\t\t\t\t\t\t\t      type=\"number\"\r\n\t\t\t\t\t\t\t\t      step=\"0.1\"\r\n\t\t\t\t\t\t\t\t      min=\"0\">\r\n\t\t\t\t\t\t\t      </el-input>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t      </div>\r\n\t\t\t      </div>\r\n\t\t      </div>\r\n\t      </template>\r\n\t      <template v-else-if=\"procurementShow\" >\r\n\t\t      <!-- PDF渲染状态提示 -->\r\n\t\t      <div v-if=\"!procurementPdfRendered\" class=\"render-status-tip\">\r\n\t\t\t      <i class=\"el-icon-loading\"></i>\r\n\t\t\t      <span>采购文件正在渲染中，请稍候...</span>\r\n\t\t      </div>\r\n\t\t      <div v-else class=\"render-status-tip success\">\r\n\t\t\t      <i class=\"el-icon-success\"></i>\r\n\t\t\t      <span>采购文件渲染完成，可以点击跳转</span>\r\n\t\t      </div>\r\n\t\t      \r\n\t\t      <!-- 采购文件评分项显示 -->\r\n\t\t      <div v-for=\"(item, index) in pageProcurement\" :key=\"'procurement-' + index\"\r\n\t\t           class=\"factor-item business-review-factor-item\"\r\n\t\t           @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t      <!-- 悬浮框 -->\r\n\t\t\t      <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t           class=\"factor-tooltip\"\r\n\t\t\t           @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t      <div class=\"tooltip-header\">\r\n\t\t\t\t\t      <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t      <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t      </div>\r\n\t\t\t\t      <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t      </div>\r\n\t\t\t      \r\n\t\t\t      <div>\r\n\t\t\t\t      <div class=\"factors\">\r\n\t\t\t\t\t      <div class=\"business-review-factor-title-group\">\r\n\t\t\t\t\t\t      <div class=\"business-review-factor-title\" @click=\"jumpToProcurementPage(item)\">\r\n\t\t\t\t\t\t\t      {{ item.itemName }}\r\n\t\t\t\t\t\t\t      <span style=\"font-size: 12px;color: red;\"> 最高{{ getMaxScore(item) }}分</span>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t      </div>\r\n\t\t\t      </div>\r\n\t\t      </div>\r\n\t      </template>\r\n       \r\n\t      <div class=\"business-review-submit-group\">\r\n          <!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"save\">保存</el-button></div> -->\r\n          <div><el-button class=\"item-button-little business-review-submit-btn\" @click=\"submit\">提交</el-button></div>\r\n        </div>\r\n\r\n        <div class=\"business-review-content\">\r\n          <div class=\"business-review-content-title\">评审内容：</div>\r\n          <div class=\"business-review-content-remark\" v-html=\"selectNode.itemRemark\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  supplierInfo,\r\n  approvalProcess,\r\n  scoringFactors,\r\n  checkReviewSummary,\r\n  filesById,\r\n} from \"@/api/expert/review\";\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\";\r\nimport { reEvaluate } from \"@/api/expert/review\";\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      options: [],\r\n      scoringSystem: [],\r\n      selectNode: {},\r\n      supplier: \"\",\r\n      selectSupplier: {},\r\n      expertInfo: {},\r\n      defaultRatingArray: {},\r\n      file: {},\r\n      responseShow: false,\r\n      procurementShow: false,\r\n      double: false,\r\n\r\n      entDocResponsePage: {},\r\n      factorsPage: {},\r\n      bidderFactor: {},\r\n      responsePdf: null,\r\n      procurementPdf: null,\r\n\t    currentMaxScore: null,\r\n\r\n      // 按钮状态管理\r\n      activeButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n      // 采购文件相关数据\r\n      entDocProcurementPage: {}, // 采购文件页码信息\r\n      pageProcurement: [], // 采购文件的评分项\r\n      attachmentsList:[], // 文件列表\r\n\t    \r\n\t    // PDF渲染状态管理\r\n\t    responsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n\t    procurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n      srcList: [\"/evalution/help.jpg\"],\r\n\t    \r\n\t    // 悬停状态管理\r\n\t    hoveredFactorNode: null, // 悬停时的评分项\r\n\t    tooltipTimer: null, // 悬浮框显示定时器\r\n    };\r\n  },\r\n  methods: {\r\n    /**\r\n     * 限制输入框只能输入数字和小数点\r\n     * @param {Event} event - 键盘事件\r\n     */\r\n    onlyNumber(event) {\r\n      // 获取按键的字符码\r\n      const charCode = event.which || event.keyCode;\r\n\r\n      // 允许的字符：数字(48-57)、小数点(46)、退格(8)、删除(46)、Tab(9)、Enter(13)、方向键(37-40)\r\n      if (\r\n        (charCode >= 48 && charCode <= 57) || // 数字 0-9\r\n        charCode === 46 || // 小数点\r\n        charCode === 8 ||  // 退格键\r\n        charCode === 9 ||  // Tab键\r\n        charCode === 13 || // Enter键\r\n        (charCode >= 37 && charCode <= 40) // 方向键\r\n      ) {\r\n        return true;\r\n      }\r\n\r\n      // 阻止其他字符输入\r\n      event.preventDefault();\r\n      return false;\r\n    },\r\n\r\n    /**\r\n     * 处理分数输入，确保只能输入有效的数字\r\n     * @param {string} itemId - 评估项ID\r\n     * @param {string} value - 输入值\r\n     */\r\n    handleScoreInput(itemId, value) {\r\n      // 移除非数字字符（保留小数点）\r\n      let cleanValue = value.replace(/[^\\d.]/g, '');\r\n\r\n      // 确保只有一个小数点\r\n      const parts = cleanValue.split('.');\r\n      if (parts.length > 2) {\r\n        cleanValue = parts[0] + '.' + parts.slice(1).join('');\r\n      }\r\n\r\n      // 限制小数点后最多2位\r\n      if (parts.length === 2 && parts[1].length > 2) {\r\n        cleanValue = parts[0] + '.' + parts[1].substring(0, 2);\r\n      }\r\n\r\n      // 更新值\r\n      this.defaultRatingArray[itemId].state = cleanValue;\r\n\r\n      // 调用原有的验证方法\r\n      this.validateScore(itemId, cleanValue);\r\n    },\r\n\r\n    init() {\r\n      const expertInfo = JSON.parse(localStorage.getItem(\"expertInfo\"));\r\n      this.entDocResponsePage = JSON.parse(\r\n        localStorage.getItem(\"entDocResponsePage\")\r\n      );\r\n      // 初始化采购文件页码信息\r\n      this.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\"));\r\n      supplierInfo({ projectId: this.$route.query.projectId, isAbandonedBid: 0 }).then(\r\n        (response) => {\r\n          if (response.code == 200) {\r\n            this.options = response.rows.filter(item => item.isAbandonedBid == 0);\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        }\r\n      );\r\n      approvalProcess(this.$route.query.projectId, expertInfo.resultId).then(\r\n        (response) => {\r\n          if (response.code == 200) {\r\n            // 文件列表\r\n            this.attachmentsList = response.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n\r\n            this.scoringSystem =\r\n              response.data.scoringMethodUinfo.scoringMethodItems.find(\r\n                (item) => {\r\n                  return (\r\n                    item.scoringMethodItemId ==\r\n                    this.$route.query.scoringMethodItemId\r\n                  );\r\n                }\r\n              );\r\n            localStorage.setItem(\r\n              \"evalProjectEvaluationProcess\",\r\n              JSON.stringify(this.scoringSystem.evalProjectEvaluationProcess)\r\n            );\r\n\r\n            this.defaultRatingArray = this.scoringSystem.uitems.reduce(\r\n              (acc, _, index) => {\r\n                acc[this.scoringSystem.uitems[index].entMethodItemId] = {\r\n                  state: null,\r\n                  reason: \"\",\r\n                };\r\n                return acc;\r\n              },\r\n              {}\r\n            );\r\n            console.log(\"this.scoringSystem.items\", this.scoringSystem.uitems);\r\n          } else {\r\n            this.$messgae.warning(response.msg);\r\n          }\r\n        }\r\n      );\r\n      filesById(this.$route.query.projectId).then((response) => {\r\n        if (response.code == 200) {\r\n          this.file = response.data;\r\n          if (this.file.tenderNoticeFilePath != undefined) {\r\n            this.procurementPdf = this.file.tenderNoticeFilePath;\r\n          }\r\n          // if (this.file.file != undefined) {\r\n          //   this.responsePdf = this.file.file[0];\r\n          // }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      // 初始化专家信息\r\n      this.initExpertInfo();\r\n    },\r\n\r\n    /**\r\n     * 初始化专家信息\r\n     */\r\n    initExpertInfo() {\r\n      try {\r\n        const itemString = localStorage.getItem(\"expertInfo\");\r\n        if (itemString) {\r\n          this.expertInfo = JSON.parse(itemString);\r\n          console.log(\"专家信息已初始化\", this.expertInfo);\r\n        } else {\r\n          console.warn(\"localStorage中未找到expertInfo\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"初始化专家信息失败:\", error);\r\n      }\r\n    },\r\n\r\n    handleChange(value) {\r\n      if(Object.keys(this.selectSupplier).length != 0){\r\n        this.tmpSave();\r\n      }\r\n      this.selectSupplier = this.options.find((item) => {\r\n        return item.bidderName == value;\r\n      });\r\n\r\n      // 根据bidderid获取供应商因素及其对应页码\r\n      this.bidderFactor = this.factorsPage[this.selectSupplier.bidderId];\r\n\r\n      const data = {\r\n        expertResultId: this.expertInfo.resultId,\r\n        projectId: this.$route.query.projectId,\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n      };\r\n      getDetailByPsxx(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.factorList = response.data;\r\n          const factor = this.factorList.find((item) => {\r\n            return item.bidderName == value;\r\n          }).evalExpertEvaluationDetails;\r\n          if (factor != null) {\r\n            factor.map((item) => {\r\n              this.defaultRatingArray[item.scoringMethodUitemId].reason =\r\n                item.evaluationRemark;\r\n              this.defaultRatingArray[item.scoringMethodUitemId].state =\r\n                item.evaluationResult;\r\n            });\r\n          } else {\r\n            Object.keys(this.defaultRatingArray).forEach((key) => {\r\n              this.defaultRatingArray[key].state = null;\r\n              this.defaultRatingArray[key].reason = \"\";\r\n            });\r\n          }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      this.showResponseFile();\r\n    },\r\n\t  validateScore(itemId, event) {\r\n\t\t  const inputValue = parseFloat(event);\r\n\t\t  console.log(\"inputValue\", inputValue);\r\n\t\t  \r\n\t\t  // 获取当前评分项的最大分值\r\n\t\t  const currentItem = this.scoringSystem.uitems.find(item => item.entMethodItemId === itemId);\r\n\t\t  let maxScore = null;\r\n\t\t  \r\n\t\t  if (currentItem) {\r\n\t\t\t  // 如果有分数挡位，使用挡位中的最大值\r\n\t\t\t  if (currentItem.scoreLevel && currentItem.scoreLevel.length > 0) {\r\n\t\t\t\t  const scoreLevels = currentItem.scoreLevel.split(',').map(item => parseFloat(item.trim())).filter(item => !isNaN(item));\r\n\t\t\t\t  if (scoreLevels.length > 0) {\r\n\t\t\t\t\t  maxScore = Math.max(...scoreLevels);\r\n\t\t\t\t  }\r\n\t\t\t  } else {\r\n\t\t\t\t  // 否则使用配置的最大分值\r\n\t\t\t\t  maxScore = parseFloat(currentItem.score);\r\n\t\t\t  }\r\n\t\t  }\r\n\t\t  \r\n\t\t  console.log(\"maxScore\", maxScore);\r\n\t\t  \r\n\t\t  if (!isNaN(inputValue) && maxScore !== null) {\r\n\t\t\t  if (inputValue > maxScore) {\r\n\t\t\t\t  this.$message.warning(`输入分数不能超过${maxScore}分，请重新输入`);\r\n\t\t\t\t  // 将输入值限制为最大分数值\r\n\t\t\t\t  this.defaultRatingArray[itemId].state = \"\";\r\n\t\t\t  } else if (inputValue < 0) {\r\n\t\t\t\t  this.$message.warning(\"输入分数不能小于0分\");\r\n\t\t\t\t  this.defaultRatingArray[itemId].state = \"\";\r\n\t\t\t  }\r\n\t\t  }\r\n\t  },\r\n    showResponseFile() {\r\n      if (Object.keys(this.selectSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        this.activeButton = 'response'; // 设置当前激活按钮\r\n        this.double = false;\r\n        this.procurementShow = false;\r\n        this.responseShow = true;\r\n        this.responsePdf = this.file.file[this.selectSupplier.bidderId];\r\n      }\r\n    },\r\n    fileContrast() {\r\n      if (Object.keys(this.selectSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        this.activeButton = 'contrast'; // 设置当前激活按钮\r\n        this.double = true;\r\n        this.procurementShow = true;\r\n        this.responseShow = true;\r\n        this.responsePdf = this.file.file[this.selectSupplier.bidderId];\r\n      }\r\n    },\r\n    showInfo(item) {\r\n\t    // 检查PDF是否渲染完成\r\n\t    if (!this.canJumpToPage()) {\r\n\t\t    this.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t    return;\r\n\t    }\r\n\t\t\t\r\n      this.selectNode = item;\r\n\r\n      // 如果只显示采购文件，使用采购文件页码信息\r\n      if (this.procurementShow && !this.responseShow) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        if (item.jumpToPage) {\r\n          this.$refs.procurement.skipPage(item.jumpToPage);\r\n        } else if (this.entDocProcurementPage && this.entDocProcurementPage[item.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[item.itemName]);\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 如果显示响应文件或对比模式，需要选择供应商\r\n      if (Object.keys(this.bidderFactor).length != 0) {\r\n        // 跳转到响应文件对应页码\r\n        if (this.responseShow && this.$refs.response) {\r\n\t        if (!this.responsePdfRendered) {\r\n\t\t        this.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t        return;\r\n\t        }\r\n\t\t\t\t\t\r\n          this.$refs.response.skipPage(\r\n            this.bidderFactor[this.selectNode.itemName]\r\n          );\r\n        }\r\n\r\n        // 跳转到采购文件对应页码\r\n        if (this.procurementShow && this.$refs.procurement) {\r\n\t        if (!this.procurementPdfRendered) {\r\n\t\t        this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t        return;\r\n\t        }\r\n\t\t\t\t\t\r\n          // 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n          if (this.entDocProcurementPage && this.entDocProcurementPage[item.itemName]) {\r\n            this.$refs.procurement.skipPage(this.entDocProcurementPage[item.itemName]);\r\n          } else {\r\n            // 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n            // 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n          }\r\n        }\r\n      } else {\r\n        this.$message.warning(\"请先选择供应商\");\r\n      }\r\n    },\r\n    initDefaultRatingArray(){\r\n      Object.keys(this.defaultRatingArray).forEach((key) => {\r\n        this.defaultRatingArray[key].state = null;\r\n        this.defaultRatingArray[key].reason = \"\";\r\n      });\r\n    },\r\n    /**\r\n     * 校验所有评分项是否填写完整\r\n     * @returns {boolean} 是否全部填写\r\n     */\r\n    validateAllRatings() {\r\n      for (const item of this.scoringSystem.uitems) {\r\n        const state = this.defaultRatingArray[item.entMethodItemId].state;\r\n\r\n        // 评分结果未填写\r\n        if (state === null || state === '' || state === undefined) {\r\n          // this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n          return true;\r\n        }\r\n\r\n        // 对于分数评分，检查是否为有效数值\r\n        if (!item.scoreLevel || item.scoreLevel.length === 0 || item.scoreLevel === null || item.scoreLevel === undefined) {\r\n          const score = parseFloat(state);\r\n          if (isNaN(score) || score < 0) {\r\n            this.$message.warning(`${item.itemName}的评分必须是有效的数值且不能小于0`);\r\n            return false;\r\n          }\r\n          // 检查分数是否超过最大值\r\n          const maxScore = this.getMaxScore(item);\r\n          if (score > maxScore) {\r\n            this.$message.warning(`${item.itemName}的评分不能超过${maxScore}分`);\r\n            return false;\r\n          }\r\n        } else {\r\n          // 对于有挡位的评分，检查是否在允许的挡位范围内\r\n          const scoreLevels = item.scoreLevel.split(',').map(level => level.trim());\r\n          if (!scoreLevels.includes(state.toString())) {\r\n            this.$message.warning(`${item.itemName}的评分必须选择指定的挡位：${item.scoreLevel}`);\r\n            return false;\r\n          }\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n\r\n    tmpSave(){\r\n      console.log(\"-------开始保存评审结果----------------\");\r\n\r\n      // 先校验所有评分项是否填写完整\r\n      if (!this.validateAllRatings()) {\r\n        return Promise.resolve({ code: 0, success: false }); // 校验失败\r\n      }\r\n\r\n      var ratingArray = JSON.parse(JSON.stringify(this.defaultRatingArray));\r\n\r\n      var data = [];\r\n      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        const item = this.scoringSystem.uitems[index];\r\n        const itemId = item.entMethodItemId;\r\n        // 获取当前项对应的评分结果\r\n        const evaluationResult = ratingArray[itemId].state;\r\n        if (evaluationResult === null || evaluationResult === \"\") {\r\n          // 如果评分结果为空，则不保存此条信息\r\n          console.log(\"-------评分结果为空，不保存此条信息----------------\");\r\n          continue;\r\n        }\r\n          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n          const evaluationRemark = ratingArray[itemId].reason || \"\";\r\n          data.push({\r\n            scoringMethodUitemId: itemId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            entId: this.selectSupplier.bidderId,\r\n            evaluationResult: evaluationResult,\r\n            evaluationRemark: evaluationRemark\r\n          });\r\n        }\r\n        if(data.length>0){\r\n            console.log(\"-------开始后台保存评审结果----------------\");\r\n            return scoringFactors(data).then((response) => {\r\n              console.log(response.msg);\r\n              if (response.code == 200) {\r\n                this.$message.success(\"保存成功\");\r\n                return { code: 200, success: true };\r\n              } else {\r\n                this.$message.warning(response.msg);\r\n                return { code: response.code, success: false };\r\n              }\r\n            }).catch((error) => {\r\n              this.$message.error(\"保存失败\");\r\n              return { code: 0, success: false };\r\n            });\r\n        }else{\r\n          return Promise.resolve({ code: 200, success: true }); // 没有数据需要保存时也返回成功\r\n        }\r\n    },\r\n    save() {\r\n      if (this.supplier == \"\") {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        //const data = this.generatingSavedData();\r\n        var data = [];\r\n        for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n          const item = this.scoringSystem.uitems[index];\r\n          const itemId = item.entMethodItemId;\r\n          // 获取当前项对应的评分结果\r\n          const evaluationResult = this.defaultRatingArray[itemId].state;\r\n          console.log(evaluationResult)\r\n          if (evaluationResult === null || evaluationResult === \"\") {\r\n            // 如果评分结果为空，弹出提示，提示内容包含该项的itemName\r\n            this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n            return; // 直接返回，不再继续构建数据，等待用户填写完整\r\n          }\r\n          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n          const evaluationRemark = this.defaultRatingArray[itemId].reason || \"\";\r\n          data.push({\r\n            scoringMethodUitemId: itemId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            entId: this.selectSupplier.bidderId,\r\n            evaluationResult: evaluationResult,\r\n            evaluationRemark: evaluationRemark\r\n          });\r\n        }\r\n        scoringFactors(data).then((response) => {\r\n          if (response.code == 200) {\r\n            this.$message.success(response.msg);\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 生成保存数据\r\n    generatingSavedData() {\r\n      var data = [];\r\n      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        data.push({\r\n          scoringMethodUitemId:\r\n            this.scoringSystem.uitems[index].entMethodItemId,\r\n          expertResultId: this.expertInfo.resultId,\r\n          entId: this.selectSupplier.bidderId,\r\n          evaluationResult:\r\n            this.defaultRatingArray[\r\n              this.scoringSystem.uitems[index].entMethodItemId\r\n            ].state,\r\n          evaluationRemark:\r\n            this.defaultRatingArray[\r\n              this.scoringSystem.uitems[index].entMethodItemId\r\n            ].reason,\r\n        });\r\n      }\r\n      return data;\r\n    },\r\n    submit() {\r\n        this.tmpSave().then((saveResult) => {\r\n          // 检查保存结果，如果校验失败则不继续提交\r\n          if (!saveResult || saveResult.success === false) {\r\n            return; // 校验失败，不继续提交流程\r\n          }\r\n\r\n          const data = {\r\n            projectId: this.$route.query.projectId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n          };\r\n          checkReviewSummary(data).then((response) => {\r\n            if (response.code == 200) {\r\n            // 修改专家进度\r\n            const status = {\r\n              evalExpertScoreInfoId: JSON.parse(\r\n                localStorage.getItem(\"evalExpertScoreInfo\")\r\n              ).evalExpertScoreInfoId,\r\n              evalState: 1,\r\n            };\r\n            editEvalExpertScoreInfo(status).then((res) => {\r\n              if (res.code == 200) {\r\n                this.$message.success(\"提交成功\");\r\n              }\r\n            });\r\n            this.$emit(\"send\", \"two\");\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      })\r\n    },\r\n    /**\r\n     * 显示采购文件PDF\r\n     */\r\n    viewPurchasing() {\r\n      this.activeButton = 'procurement'; // 设置当前激活按钮\r\n      this.double = false; // 单文件模式\r\n      this.responseShow = false; // 不显示响应文件\r\n      this.procurementShow = true; // 显示采购文件\r\n\r\n      // 右侧评分项显示为采购文件的评分项\r\n      let pageProcurementArr = []; // 采购文件评分项数组\r\n      for (let item in this.entDocProcurementPage){\r\n        pageProcurementArr.push({\r\n          itemName: item,\r\n          jumpToPage: this.entDocProcurementPage[item]\r\n        })\r\n      }\r\n\r\n      console.log(this.scoringSystem.uitems);\r\n      console.log(pageProcurementArr)\r\n      this.pageProcurement = [];\r\n      for (let i = 0; i < this.scoringSystem.uitems.length;i++){\r\n        for (let j = 0; j < pageProcurementArr.length;j++){\r\n          if (this.scoringSystem.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n            this.pageProcurement.push({...this.scoringSystem.uitems[i],...pageProcurementArr[j]});\r\n          }\r\n        }\r\n      }\r\n      console.log(this.pageProcurement)\r\n    },\r\n\r\n    /**\r\n     * 跳转到采购文件对应页码\r\n     * @param {Object} item - 评分项对象\r\n     */\r\n    jumpToProcurementPage(item) {\r\n      if (item.jumpToPage && this.$refs.procurement) {\r\n        this.$refs.procurement.skipPage(item.jumpToPage);\r\n      }\r\n    },\r\n\t  \r\n\t  /**\r\n\t   * 检查是否可以跳转页面\r\n\t   * @returns {boolean} 是否可以跳转\r\n\t   */\r\n\t  canJumpToPage() {\r\n\t\t  // 如果只显示采购文件\r\n\t\t  if (this.procurementShow && !this.responseShow) {\r\n\t\t\t  return this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  // 如果只显示响应文件\r\n\t\t  if (this.responseShow && !this.procurementShow) {\r\n\t\t\t  return this.responsePdfRendered;\r\n\t\t  }\r\n\t\t  // 如果对比模式（两个都显示）\r\n\t\t  if (this.responseShow && this.procurementShow) {\r\n\t\t\t  return this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  return false;\r\n\t  },\r\n\t  /**\r\n\t   * 处理PDF渲染状态变化\r\n\t   * @param {boolean} isRendered 是否渲染完成\r\n\t   * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t   */\r\n\t  handlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t\t  if (pdfType === 'response') {\r\n\t\t\t  this.responsePdfRendered = isRendered;\r\n\t\t  } else if (pdfType === 'procurement') {\r\n\t\t\t  this.procurementPdfRendered = isRendered;\r\n\t\t  }\r\n\t\t  \r\n\t\t  if (isRendered) {\r\n\t\t\t  console.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t\t  }\r\n\t  },\r\n\t  \r\n    // 跳转到二次报价\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query: query });\r\n    },\r\n    // 跳转到询标\r\n    bidInquiry() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      this.$router.push({ path: \"/bidInquiry\", query: query });\r\n    },\r\n    // 获取因素对应页码\r\n    getFactorsPage() {\r\n      this.factorsPage = JSON.parse(localStorage.getItem(\"entDocResponsePage\"));\r\n    },\r\n    downloadFile(item){\r\n      this.$download.zip(item.filePath,item.fileName);\r\n    },\r\n    \r\n    /**\r\n     * 获取评分项的最大分值\r\n     * @param {Object} item - 评分项对象\r\n     * @returns {number} 最大分值\r\n     */\r\n    getMaxScore(item) {\r\n      if (!item) return 0;\r\n      \r\n      // 如果有分数挡位，使用挡位中的最大值\r\n      if (item.scoreLevel && item.scoreLevel.length > 0 && item.scoreLevel !== null && item.scoreLevel !== undefined) {\r\n        const scoreLevels = item.scoreLevel.split(',').map(level => parseFloat(level.trim())).filter(level => !isNaN(level));\r\n        if (scoreLevels.length > 0) {\r\n          return Math.max(...scoreLevels);\r\n        }\r\n      }\r\n      \r\n      // 否则使用配置的最大分值\r\n      return parseFloat(item.score) || 0;\r\n    },\r\n\t  \r\n\t  // ========== 悬停相关 ==========\r\n\t  /**\r\n\t   * 显示评分项悬浮框\r\n\t   * @param {Object} factorItem 评分项对象\r\n\t   */\r\n\t  showFactorTooltip(factorItem) {\r\n\t\t  if (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\t\t  \r\n\t\t  // 清除之前的定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t  this.tooltipTimer = setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = factorItem;\r\n\t\t  }, 300); // 300ms延迟\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 隐藏评分项悬浮框\r\n\t   */\r\n\t  hideFactorTooltip() {\r\n\t\t  // 清除定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t  setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = null;\r\n\t\t  }, 100);\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t   */\r\n\t  clearTooltipTimer() {\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t  }\r\n  },\r\n  mounted() {\r\n    this.init();\r\n    this.getFactorsPage();\r\n  },\r\n\t\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.business-review-container {\r\n  min-height: 57vh;\r\n  display: flex;\r\n}\r\n.business-review-main {\r\n  min-height: 57vh;\r\n  width: 79%;\r\n}\r\n.business-review-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.business-review-title-group {\r\n  display: flex;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333333;\r\n}\r\n.business-review-title {\r\n  /* 保持原样 */\r\n}\r\n.business-review-step-group {\r\n  display: grid;\r\n  justify-items: center;\r\n  position: relative;\r\n  bottom: -30px;\r\n}\r\n.business-review-step-text {\r\n  font-size: 12px;\r\n}\r\n.business-review-step-img {\r\n  width: 80px;\r\n  height: 30px;\r\n  margin-right: 20px;\r\n}\r\n.business-review-header-btns {\r\n  text-align: right;\r\n}\r\n.business-review-header-btns-group {\r\n  margin-top: 20px;\r\n}\r\n.business-review-header-btns-group .item-button {\r\n  background-color: #176ADB;\r\n  color: #FFFFFF;\r\n  border: 1px solid #176ADB;\r\n}\r\n.business-review-pdf-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  height: 82%;\r\n}\r\n.business-review-pdf {\r\n  width: 49%;\r\n}\r\n.business-review-pdf-border-right {\r\n  border-right: 1px solid #176ADB;\r\n}\r\n.business-review-pdf-border-left {\r\n  border-left: 1px solid #176ADB;\r\n}\r\n.business-review-divider {\r\n  min-height: 57vh;\r\n  width: 1%;\r\n  background-color: #F5F5F5;\r\n}\r\n.business-review-side {\r\n  min-height: 57vh;\r\n  width: 20%;\r\n}\r\n.business-review-select-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.business-review-select {\r\n  width: 100%;\r\n}\r\n.business-review-side-content {\r\n  padding: 15px 20px;\r\n}\r\n.business-review-factor-item {\r\n  margin-bottom: 10px;\r\n}\r\n.business-review-factor-title-group {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  text-align: left;\r\n  width: 98%;\r\n}\r\n.business-review-factor-title {\r\n  cursor: pointer;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 500;\r\n  font-size: 16px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n}\r\n.business-review-factor-score-group {\r\n  display: flex;\r\n  width: 100%;\r\n  justify-content: flex-end;\r\n  padding: 10px;\r\n}\r\n.business-review-factor-score {\r\n  color: green;\r\n  font-size: 16px;\r\n}\r\n.business-review-submit-group {\r\n  display: flex;\r\n  margin: 32px 0;\r\n  justify-content: space-evenly;\r\n}\r\n.business-review-submit-btn {\r\n  background-color: #176ADB;\r\n}\r\n.business-review-content {\r\n  text-align: left;\r\n  font-size: 14px;\r\n}\r\n.business-review-content-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 15px;\r\n  color: #176ADB;\r\n  letter-spacing: 0;\r\n}\r\n.business-review-content-remark {\r\n  padding: 6px 30px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n\t  transition: all 0.3s ease;\r\n\t  padding: 4px 8px;\r\n\t  border-radius: 4px;\r\n\t  \r\n\t  &:hover {\r\n\t\t  background-color: #f0f8ff;\r\n\t\t  color: #176ADB;\r\n\t\t  transform: translateX(2px);\r\n\t  }\r\n  }\r\n}\r\n.item-button {\r\n  border: 1px solid #979797;\r\n  width: 150px;\r\n  height: 36px;\r\n  margin: 0 10px;\r\n  font-weight: 700;\r\n  font-size: 17px;\r\n  border-radius: 6px;\r\n  color: #333333;\r\n  &:hover {\r\n    color: #333333;\r\n  }\r\n}\r\n.business-blue-btn {\r\n  background-color: #176ADB !important;\r\n  color: #fff !important;\r\n  border: 1px solid #176ADB !important;\r\n}\r\n.business-blue-btn-active {\r\n  background-color: #FF6B35 !important;\r\n  color: #fff !important;\r\n  border: 1px solid #FF6B35 !important;\r\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.item-button-little {\r\n  width: 124px;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #fff;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n  .fileItem {\r\n    transition: all 0.3s ease;\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    }\r\n\r\n    ::v-deep .el-card__body {\r\n      padding: 0;\r\n    }\r\n  }\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\t\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\t\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\t\t\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\t\t\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\t\t\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuLA,IAAAA,OAAA,GAAAC,OAAA;AAOA,IAAAC,OAAA,GAAAD,OAAA;AAEA,IAAAE,aAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,aAAA;MACAC,UAAA;MACAC,QAAA;MACAC,cAAA;MACAC,UAAA;MACAC,kBAAA;MACAC,IAAA;MACAC,YAAA;MACAC,eAAA;MACAC,MAAA;MAEAC,kBAAA;MACAC,WAAA;MACAC,YAAA;MACAC,WAAA;MACAC,cAAA;MACAC,eAAA;MAEA;MACAC,YAAA;MAAA;;MAEA;MACAC,qBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,eAAA;MAAA;;MAEA;MACAC,mBAAA;MAAA;MACAC,sBAAA;MAAA;;MAEAC,OAAA;MAEA;MACAC,iBAAA;MAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA;IACA;AACA;AACA;AACA;IACAC,UAAA,WAAAA,WAAAC,KAAA;MACA;MACA,IAAAC,QAAA,GAAAD,KAAA,CAAAE,KAAA,IAAAF,KAAA,CAAAG,OAAA;;MAEA;MACA,IACAF,QAAA,UAAAA,QAAA;MAAA;MACAA,QAAA;MAAA;MACAA,QAAA;MAAA;MACAA,QAAA;MAAA;MACAA,QAAA;MAAA;MACAA,QAAA,UAAAA,QAAA;MAAA,EACA;QACA;MACA;;MAEA;MACAD,KAAA,CAAAI,cAAA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC,gBAAA,WAAAA,iBAAAC,MAAA,EAAAC,KAAA;MACA;MACA,IAAAC,UAAA,GAAAD,KAAA,CAAAE,OAAA;;MAEA;MACA,IAAAC,KAAA,GAAAF,UAAA,CAAAG,KAAA;MACA,IAAAD,KAAA,CAAAE,MAAA;QACAJ,UAAA,GAAAE,KAAA,YAAAA,KAAA,CAAAG,KAAA,IAAAC,IAAA;MACA;;MAEA;MACA,IAAAJ,KAAA,CAAAE,MAAA,UAAAF,KAAA,IAAAE,MAAA;QACAJ,UAAA,GAAAE,KAAA,YAAAA,KAAA,IAAAK,SAAA;MACA;;MAEA;MACA,KAAArC,kBAAA,CAAA4B,MAAA,EAAAU,KAAA,GAAAR,UAAA;;MAEA;MACA,KAAAS,aAAA,CAAAX,MAAA,EAAAE,UAAA;IACA;IAEAU,IAAA,WAAAA,KAAA;MAAA,IAAAC,KAAA;MACA,IAAA1C,UAAA,GAAA2C,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACA,KAAAxC,kBAAA,GAAAqC,IAAA,CAAAC,KAAA,CACAC,YAAA,CAAAC,OAAA,sBACA;MACA;MACA,KAAAjC,qBAAA,GAAA8B,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACA,IAAAC,oBAAA;QAAAC,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QAAAG,cAAA;MAAA,GAAAC,IAAA,CACA,UAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAZ,KAAA,CAAA/C,OAAA,GAAA0D,QAAA,CAAAE,IAAA,CAAAC,MAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAN,cAAA;UAAA;QACA;UACAT,KAAA,CAAAgB,QAAA,CAAAC,OAAA,CAAAN,QAAA,CAAAO,GAAA;QACA;MACA,CACA;MACA,IAAAC,uBAAA,OAAAZ,MAAA,CAAAC,KAAA,CAAAF,SAAA,EAAAhD,UAAA,CAAA8D,QAAA,EAAAV,IAAA,CACA,UAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACA;UACAZ,KAAA,CAAA3B,eAAA,GAAAsC,QAAA,CAAA3D,IAAA,CAAAqE,gBAAA,CAAAC,WAAA,CAAAR,MAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAQ,QAAA;UAAA;UAEAvB,KAAA,CAAA9C,aAAA,GACAyD,QAAA,CAAA3D,IAAA,CAAAwE,kBAAA,CAAAC,kBAAA,CAAAC,IAAA,CACA,UAAAX,IAAA;YACA,OACAA,IAAA,CAAAY,mBAAA,IACA3B,KAAA,CAAAO,MAAA,CAAAC,KAAA,CAAAmB,mBAAA;UAEA,CACA;UACAxB,YAAA,CAAAyB,OAAA,CACA,gCACA3B,IAAA,CAAA4B,SAAA,CAAA7B,KAAA,CAAA9C,aAAA,CAAA4E,4BAAA,CACA;UAEA9B,KAAA,CAAAzC,kBAAA,GAAAyC,KAAA,CAAA9C,aAAA,CAAA6E,MAAA,CAAAC,MAAA,CACA,UAAAC,GAAA,EAAAC,CAAA,EAAAC,KAAA;YACAF,GAAA,CAAAjC,KAAA,CAAA9C,aAAA,CAAA6E,MAAA,CAAAI,KAAA,EAAAC,eAAA;cACAvC,KAAA;cACAwC,MAAA;YACA;YACA,OAAAJ,GAAA;UACA,GACA,EACA;UACAK,OAAA,CAAAC,GAAA,6BAAAvC,KAAA,CAAA9C,aAAA,CAAA6E,MAAA;QACA;UACA/B,KAAA,CAAAwC,QAAA,CAAAvB,OAAA,CAAAN,QAAA,CAAAO,GAAA;QACA;MACA,CACA;MACA,IAAAuB,iBAAA,OAAAlC,MAAA,CAAAC,KAAA,CAAAF,SAAA,EAAAI,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAZ,KAAA,CAAAxC,IAAA,GAAAmD,QAAA,CAAA3D,IAAA;UACA,IAAAgD,KAAA,CAAAxC,IAAA,CAAAkF,oBAAA,IAAAC,SAAA;YACA3C,KAAA,CAAAhC,cAAA,GAAAgC,KAAA,CAAAxC,IAAA,CAAAkF,oBAAA;UACA;UACA;UACA;UACA;QACA;UACA1C,KAAA,CAAAgB,QAAA,CAAAC,OAAA,CAAAN,QAAA,CAAAO,GAAA;QACA;MACA;MACA;MACA,KAAA0B,cAAA;IACA;IAEA;AACA;AACA;IACAA,cAAA,WAAAA,eAAA;MACA;QACA,IAAAC,UAAA,GAAA1C,YAAA,CAAAC,OAAA;QACA,IAAAyC,UAAA;UACA,KAAAvF,UAAA,GAAA2C,IAAA,CAAAC,KAAA,CAAA2C,UAAA;UACAP,OAAA,CAAAC,GAAA,kBAAAjF,UAAA;QACA;UACAgF,OAAA,CAAAQ,IAAA;QACA;MACA,SAAAC,KAAA;QACAT,OAAA,CAAAS,KAAA,eAAAA,KAAA;MACA;IACA;IAEAC,YAAA,WAAAA,aAAA5D,KAAA;MAAA,IAAA6D,MAAA;MACA,IAAAC,MAAA,CAAAC,IAAA,MAAA9F,cAAA,EAAAoC,MAAA;QACA,KAAA2D,OAAA;MACA;MACA,KAAA/F,cAAA,QAAAJ,OAAA,CAAAyE,IAAA,WAAAX,IAAA;QACA,OAAAA,IAAA,CAAAsC,UAAA,IAAAjE,KAAA;MACA;;MAEA;MACA,KAAAtB,YAAA,QAAAD,WAAA,MAAAR,cAAA,CAAAiG,QAAA;MAEA,IAAAtG,IAAA;QACAuG,cAAA,OAAAjG,UAAA,CAAA8D,QAAA;QACAd,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QACAqB,mBAAA,OAAApB,MAAA,CAAAC,KAAA,CAAAmB;MACA;MACA,IAAA6B,uBAAA,EAAAxG,IAAA,EAAA0D,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAqC,MAAA,CAAAQ,UAAA,GAAA9C,QAAA,CAAA3D,IAAA;UACA,IAAA0G,MAAA,GAAAT,MAAA,CAAAQ,UAAA,CAAA/B,IAAA,WAAAX,IAAA;YACA,OAAAA,IAAA,CAAAsC,UAAA,IAAAjE,KAAA;UACA,GAAAuE,2BAAA;UACA,IAAAD,MAAA;YACAA,MAAA,CAAAE,GAAA,WAAA7C,IAAA;cACAkC,MAAA,CAAA1F,kBAAA,CAAAwD,IAAA,CAAA8C,oBAAA,EAAAxB,MAAA,GACAtB,IAAA,CAAA+C,gBAAA;cACAb,MAAA,CAAA1F,kBAAA,CAAAwD,IAAA,CAAA8C,oBAAA,EAAAhE,KAAA,GACAkB,IAAA,CAAAgD,gBAAA;YACA;UACA;YACAb,MAAA,CAAAC,IAAA,CAAAF,MAAA,CAAA1F,kBAAA,EAAAyG,OAAA,WAAAC,GAAA;cACAhB,MAAA,CAAA1F,kBAAA,CAAA0G,GAAA,EAAApE,KAAA;cACAoD,MAAA,CAAA1F,kBAAA,CAAA0G,GAAA,EAAA5B,MAAA;YACA;UACA;QACA;UACAY,MAAA,CAAAjC,QAAA,CAAAC,OAAA,CAAAN,QAAA,CAAAO,GAAA;QACA;MACA;MACA,KAAAgD,gBAAA;IACA;IACApE,aAAA,WAAAA,cAAAX,MAAA,EAAAN,KAAA;MACA,IAAAsF,UAAA,GAAAC,UAAA,CAAAvF,KAAA;MACAyD,OAAA,CAAAC,GAAA,eAAA4B,UAAA;;MAEA;MACA,IAAAE,WAAA,QAAAnH,aAAA,CAAA6E,MAAA,CAAAL,IAAA,WAAAX,IAAA;QAAA,OAAAA,IAAA,CAAAqB,eAAA,KAAAjD,MAAA;MAAA;MACA,IAAAmF,QAAA;MAEA,IAAAD,WAAA;QACA;QACA,IAAAA,WAAA,CAAAE,UAAA,IAAAF,WAAA,CAAAE,UAAA,CAAA9E,MAAA;UACA,IAAA+E,WAAA,GAAAH,WAAA,CAAAE,UAAA,CAAA/E,KAAA,MAAAoE,GAAA,WAAA7C,IAAA;YAAA,OAAAqD,UAAA,CAAArD,IAAA,CAAA0D,IAAA;UAAA,GAAA3D,MAAA,WAAAC,IAAA;YAAA,QAAA2D,KAAA,CAAA3D,IAAA;UAAA;UACA,IAAAyD,WAAA,CAAA/E,MAAA;YACA6E,QAAA,GAAAK,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAC,OAAA,EAAAP,WAAA;UACA;QACA;UACA;UACAF,QAAA,GAAAF,UAAA,CAAAC,WAAA,CAAAW,KAAA;QACA;MACA;MAEA1C,OAAA,CAAAC,GAAA,aAAA+B,QAAA;MAEA,KAAAI,KAAA,CAAAP,UAAA,KAAAG,QAAA;QACA,IAAAH,UAAA,GAAAG,QAAA;UACA,KAAAtD,QAAA,CAAAC,OAAA,oDAAAgE,MAAA,CAAAX,QAAA;UACA;UACA,KAAA/G,kBAAA,CAAA4B,MAAA,EAAAU,KAAA;QACA,WAAAsE,UAAA;UACA,KAAAnD,QAAA,CAAAC,OAAA;UACA,KAAA1D,kBAAA,CAAA4B,MAAA,EAAAU,KAAA;QACA;MACA;IACA;IACAqE,gBAAA,WAAAA,iBAAA;MACA,IAAAhB,MAAA,CAAAC,IAAA,MAAA9F,cAAA,EAAAoC,MAAA;QACA,KAAAuB,QAAA,CAAAC,OAAA;MACA;QACA,KAAA/C,YAAA;QACA,KAAAP,MAAA;QACA,KAAAD,eAAA;QACA,KAAAD,YAAA;QACA,KAAAM,WAAA,QAAAP,IAAA,CAAAA,IAAA,MAAAH,cAAA,CAAAiG,QAAA;MACA;IACA;IACA4B,YAAA,WAAAA,aAAA;MACA,IAAAhC,MAAA,CAAAC,IAAA,MAAA9F,cAAA,EAAAoC,MAAA;QACA,KAAAuB,QAAA,CAAAC,OAAA;MACA;QACA,KAAA/C,YAAA;QACA,KAAAP,MAAA;QACA,KAAAD,eAAA;QACA,KAAAD,YAAA;QACA,KAAAM,WAAA,QAAAP,IAAA,CAAAA,IAAA,MAAAH,cAAA,CAAAiG,QAAA;MACA;IACA;IACA6B,QAAA,WAAAA,SAAApE,IAAA;MACA;MACA,UAAAqE,aAAA;QACA,KAAApE,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAA9D,UAAA,GAAA4D,IAAA;;MAEA;MACA,SAAArD,eAAA,UAAAD,YAAA;QACA,UAAAc,sBAAA;UACA,KAAAyC,QAAA,CAAAC,OAAA;UACA;QACA;QAEA,IAAAF,IAAA,CAAAsE,UAAA;UACA,KAAAC,KAAA,CAAAC,WAAA,CAAAC,QAAA,CAAAzE,IAAA,CAAAsE,UAAA;QACA,gBAAAlH,qBAAA,SAAAA,qBAAA,CAAA4C,IAAA,CAAA0E,QAAA;UACA,KAAAH,KAAA,CAAAC,WAAA,CAAAC,QAAA,MAAArH,qBAAA,CAAA4C,IAAA,CAAA0E,QAAA;QACA;QACA;MACA;;MAEA;MACA,IAAAvC,MAAA,CAAAC,IAAA,MAAArF,YAAA,EAAA2B,MAAA;QACA;QACA,SAAAhC,YAAA,SAAA6H,KAAA,CAAA3E,QAAA;UACA,UAAArC,mBAAA;YACA,KAAA0C,QAAA,CAAAC,OAAA;YACA;UACA;UAEA,KAAAqE,KAAA,CAAA3E,QAAA,CAAA6E,QAAA,CACA,KAAA1H,YAAA,MAAAX,UAAA,CAAAsI,QAAA,CACA;QACA;;QAEA;QACA,SAAA/H,eAAA,SAAA4H,KAAA,CAAAC,WAAA;UACA,UAAAhH,sBAAA;YACA,KAAAyC,QAAA,CAAAC,OAAA;YACA;UACA;;UAEA;UACA,SAAA9C,qBAAA,SAAAA,qBAAA,CAAA4C,IAAA,CAAA0E,QAAA;YACA,KAAAH,KAAA,CAAAC,WAAA,CAAAC,QAAA,MAAArH,qBAAA,CAAA4C,IAAA,CAAA0E,QAAA;UACA;YACA;YACA;UAAA;QAEA;MACA;QACA,KAAAzE,QAAA,CAAAC,OAAA;MACA;IACA;IACAyE,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACAzC,MAAA,CAAAC,IAAA,MAAA5F,kBAAA,EAAAyG,OAAA,WAAAC,GAAA;QACA0B,MAAA,CAAApI,kBAAA,CAAA0G,GAAA,EAAApE,KAAA;QACA8F,MAAA,CAAApI,kBAAA,CAAA0G,GAAA,EAAA5B,MAAA;MACA;IACA;IACA;AACA;AACA;AACA;IACAuD,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAf,OAAA,EACA,KAAA7H,aAAA,CAAA6E,MAAA;QAAAgE,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAAnF,IAAA,GAAAgF,KAAA,CAAA3G,KAAA;UACA,IAAAS,KAAA,QAAAtC,kBAAA,CAAAwD,IAAA,CAAAqB,eAAA,EAAAvC,KAAA;;UAEA;UACA,IAAAA,KAAA,aAAAA,KAAA,WAAAA,KAAA,KAAA8C,SAAA;YACA;YACA;UACA;;UAEA;UACA,KAAA5B,IAAA,CAAAwD,UAAA,IAAAxD,IAAA,CAAAwD,UAAA,CAAA9E,MAAA,UAAAsB,IAAA,CAAAwD,UAAA,aAAAxD,IAAA,CAAAwD,UAAA,KAAA5B,SAAA;YACA,IAAAqC,KAAA,GAAAZ,UAAA,CAAAvE,KAAA;YACA,IAAA6E,KAAA,CAAAM,KAAA,KAAAA,KAAA;cACA,KAAAhE,QAAA,CAAAC,OAAA,IAAAgE,MAAA,CAAAlE,IAAA,CAAA0E,QAAA;cACA;YACA;YACA;YACA,IAAAnB,QAAA,QAAA6B,WAAA,CAAApF,IAAA;YACA,IAAAiE,KAAA,GAAAV,QAAA;cACA,KAAAtD,QAAA,CAAAC,OAAA,IAAAgE,MAAA,CAAAlE,IAAA,CAAA0E,QAAA,gDAAAR,MAAA,CAAAX,QAAA;cACA;YACA;UACA;YACA;YACA,IAAAE,WAAA,GAAAzD,IAAA,CAAAwD,UAAA,CAAA/E,KAAA,MAAAoE,GAAA,WAAAwC,KAAA;cAAA,OAAAA,KAAA,CAAA3B,IAAA;YAAA;YACA,KAAAD,WAAA,CAAA6B,QAAA,CAAAxG,KAAA,CAAAyG,QAAA;cACA,KAAAtF,QAAA,CAAAC,OAAA,IAAAgE,MAAA,CAAAlE,IAAA,CAAA0E,QAAA,oFAAAR,MAAA,CAAAlE,IAAA,CAAAwD,UAAA;cACA;YACA;UACA;QACA;MAAA,SAAAgC,GAAA;QAAAV,SAAA,CAAAW,CAAA,CAAAD,GAAA;MAAA;QAAAV,SAAA,CAAAY,CAAA;MAAA;MACA;IACA;IAEArD,OAAA,WAAAA,QAAA;MAAA,IAAAsD,MAAA;MACApE,OAAA,CAAAC,GAAA;;MAEA;MACA,UAAAqD,kBAAA;QACA,OAAAe,OAAA,CAAAC,OAAA;UAAAhG,IAAA;UAAAiG,OAAA;QAAA;MACA;MAEA,IAAAC,WAAA,GAAA7G,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAA4B,SAAA,MAAAtE,kBAAA;MAEA,IAAAP,IAAA;MACA,SAAAmF,KAAA,MAAAA,KAAA,QAAAjF,aAAA,CAAA6E,MAAA,CAAAtC,MAAA,EAAA0C,KAAA;QACA,IAAApB,IAAA,QAAA7D,aAAA,CAAA6E,MAAA,CAAAI,KAAA;QACA,IAAAhD,MAAA,GAAA4B,IAAA,CAAAqB,eAAA;QACA;QACA,IAAA2B,gBAAA,GAAA+C,WAAA,CAAA3H,MAAA,EAAAU,KAAA;QACA,IAAAkE,gBAAA,aAAAA,gBAAA;UACA;UACAzB,OAAA,CAAAC,GAAA;UACA;QACA;QACA;QACA,IAAAuB,gBAAA,GAAAgD,WAAA,CAAA3H,MAAA,EAAAkD,MAAA;QACArF,IAAA,CAAA+J,IAAA;UACAlD,oBAAA,EAAA1E,MAAA;UACAoE,cAAA,OAAAjG,UAAA,CAAA8D,QAAA;UACA4F,KAAA,OAAA3J,cAAA,CAAAiG,QAAA;UACAS,gBAAA,EAAAA,gBAAA;UACAD,gBAAA,EAAAA;QACA;MACA;MACA,IAAA9G,IAAA,CAAAyC,MAAA;QACA6C,OAAA,CAAAC,GAAA;QACA,WAAA0E,sBAAA,EAAAjK,IAAA,EAAA0D,IAAA,WAAAC,QAAA;UACA2B,OAAA,CAAAC,GAAA,CAAA5B,QAAA,CAAAO,GAAA;UACA,IAAAP,QAAA,CAAAC,IAAA;YACA8F,MAAA,CAAA1F,QAAA,CAAA6F,OAAA;YACA;cAAAjG,IAAA;cAAAiG,OAAA;YAAA;UACA;YACAH,MAAA,CAAA1F,QAAA,CAAAC,OAAA,CAAAN,QAAA,CAAAO,GAAA;YACA;cAAAN,IAAA,EAAAD,QAAA,CAAAC,IAAA;cAAAiG,OAAA;YAAA;UACA;QACA,GAAAK,KAAA,WAAAnE,KAAA;UACA2D,MAAA,CAAA1F,QAAA,CAAA+B,KAAA;UACA;YAAAnC,IAAA;YAAAiG,OAAA;UAAA;QACA;MACA;QACA,OAAAF,OAAA,CAAAC,OAAA;UAAAhG,IAAA;UAAAiG,OAAA;QAAA;MACA;IACA;IACAM,IAAA,WAAAA,KAAA;MAAA,IAAAC,MAAA;MACA,SAAAhK,QAAA;QACA,KAAA4D,QAAA,CAAAC,OAAA;MACA;QACA;QACA,IAAAjE,IAAA;QACA,SAAAmF,KAAA,MAAAA,KAAA,QAAAjF,aAAA,CAAA6E,MAAA,CAAAtC,MAAA,EAAA0C,KAAA;UACA,IAAApB,IAAA,QAAA7D,aAAA,CAAA6E,MAAA,CAAAI,KAAA;UACA,IAAAhD,MAAA,GAAA4B,IAAA,CAAAqB,eAAA;UACA;UACA,IAAA2B,gBAAA,QAAAxG,kBAAA,CAAA4B,MAAA,EAAAU,KAAA;UACAyC,OAAA,CAAAC,GAAA,CAAAwB,gBAAA;UACA,IAAAA,gBAAA,aAAAA,gBAAA;YACA;YACA,KAAA/C,QAAA,CAAAC,OAAA,8CAAAgE,MAAA,CAAAlE,IAAA,CAAA0E,QAAA;YACA;UACA;UACA;UACA,IAAA3B,gBAAA,QAAAvG,kBAAA,CAAA4B,MAAA,EAAAkD,MAAA;UACArF,IAAA,CAAA+J,IAAA;YACAlD,oBAAA,EAAA1E,MAAA;YACAoE,cAAA,OAAAjG,UAAA,CAAA8D,QAAA;YACA4F,KAAA,OAAA3J,cAAA,CAAAiG,QAAA;YACAS,gBAAA,EAAAA,gBAAA;YACAD,gBAAA,EAAAA;UACA;QACA;QACA,IAAAmD,sBAAA,EAAAjK,IAAA,EAAA0D,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA;YACAwG,MAAA,CAAApG,QAAA,CAAA6F,OAAA,CAAAlG,QAAA,CAAAO,GAAA;UACA;YACAkG,MAAA,CAAApG,QAAA,CAAAC,OAAA,CAAAN,QAAA,CAAAO,GAAA;UACA;QACA;MACA;IACA;IACA;IACAmG,mBAAA,WAAAA,oBAAA;MACA,IAAArK,IAAA;MACA,SAAAmF,KAAA,MAAAA,KAAA,QAAAjF,aAAA,CAAA6E,MAAA,CAAAtC,MAAA,EAAA0C,KAAA;QACAnF,IAAA,CAAA+J,IAAA;UACAlD,oBAAA,EACA,KAAA3G,aAAA,CAAA6E,MAAA,CAAAI,KAAA,EAAAC,eAAA;UACAmB,cAAA,OAAAjG,UAAA,CAAA8D,QAAA;UACA4F,KAAA,OAAA3J,cAAA,CAAAiG,QAAA;UACAS,gBAAA,EACA,KAAAxG,kBAAA,CACA,KAAAL,aAAA,CAAA6E,MAAA,CAAAI,KAAA,EAAAC,eAAA,CACA,CAAAvC,KAAA;UACAiE,gBAAA,EACA,KAAAvG,kBAAA,CACA,KAAAL,aAAA,CAAA6E,MAAA,CAAAI,KAAA,EAAAC,eAAA,CACA,CAAAC;QACA;MACA;MACA,OAAArF,IAAA;IACA;IACAsK,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,KAAAnE,OAAA,GAAA1C,IAAA,WAAA8G,UAAA;QACA;QACA,KAAAA,UAAA,IAAAA,UAAA,CAAAX,OAAA;UACA;QACA;QAEA,IAAA7J,IAAA;UACAsD,SAAA,EAAAiH,MAAA,CAAAhH,MAAA,CAAAC,KAAA,CAAAF,SAAA;UACAiD,cAAA,EAAAgE,MAAA,CAAAjK,UAAA,CAAA8D,QAAA;UACAO,mBAAA,EAAA4F,MAAA,CAAAhH,MAAA,CAAAC,KAAA,CAAAmB;QACA;QACA,IAAA8F,0BAAA,EAAAzK,IAAA,EAAA0D,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA;YACA;YACA,IAAA8G,MAAA;cACAC,qBAAA,EAAA1H,IAAA,CAAAC,KAAA,CACAC,YAAA,CAAAC,OAAA,uBACA,EAAAuH,qBAAA;cACAC,SAAA;YACA;YACA,IAAAC,qCAAA,EAAAH,MAAA,EAAAhH,IAAA,WAAAoH,GAAA;cACA,IAAAA,GAAA,CAAAlH,IAAA;gBACA2G,MAAA,CAAAvG,QAAA,CAAA6F,OAAA;cACA;YACA;YACAU,MAAA,CAAAQ,KAAA;UACA;YACAR,MAAA,CAAAvG,QAAA,CAAAC,OAAA,CAAAN,QAAA,CAAAO,GAAA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACA8G,cAAA,WAAAA,eAAA;MACA,KAAA9J,YAAA;MACA,KAAAP,MAAA;MACA,KAAAF,YAAA;MACA,KAAAC,eAAA;;MAEA;MACA,IAAAuK,kBAAA;MACA,SAAAlH,IAAA,SAAA5C,qBAAA;QACA8J,kBAAA,CAAAlB,IAAA;UACAtB,QAAA,EAAA1E,IAAA;UACAsE,UAAA,OAAAlH,qBAAA,CAAA4C,IAAA;QACA;MACA;MAEAuB,OAAA,CAAAC,GAAA,MAAArF,aAAA,CAAA6E,MAAA;MACAO,OAAA,CAAAC,GAAA,CAAA0F,kBAAA;MACA,KAAA7J,eAAA;MACA,SAAA8J,CAAA,MAAAA,CAAA,QAAAhL,aAAA,CAAA6E,MAAA,CAAAtC,MAAA,EAAAyI,CAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,kBAAA,CAAAxI,MAAA,EAAA0I,CAAA;UACA,SAAAjL,aAAA,CAAA6E,MAAA,CAAAmG,CAAA,EAAAzC,QAAA,IAAAwC,kBAAA,CAAAE,CAAA,EAAA1C,QAAA;YACA,KAAArH,eAAA,CAAA2I,IAAA,KAAAqB,cAAA,CAAArD,OAAA,MAAAqD,cAAA,CAAArD,OAAA,WAAA7H,aAAA,CAAA6E,MAAA,CAAAmG,CAAA,IAAAD,kBAAA,CAAAE,CAAA;UACA;QACA;MACA;MACA7F,OAAA,CAAAC,GAAA,MAAAnE,eAAA;IACA;IAEA;AACA;AACA;AACA;IACAiK,qBAAA,WAAAA,sBAAAtH,IAAA;MACA,IAAAA,IAAA,CAAAsE,UAAA,SAAAC,KAAA,CAAAC,WAAA;QACA,KAAAD,KAAA,CAAAC,WAAA,CAAAC,QAAA,CAAAzE,IAAA,CAAAsE,UAAA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAD,aAAA,WAAAA,cAAA;MACA;MACA,SAAA1H,eAAA,UAAAD,YAAA;QACA,YAAAc,sBAAA;MACA;MACA;MACA,SAAAd,YAAA,UAAAC,eAAA;QACA,YAAAY,mBAAA;MACA;MACA;MACA,SAAAb,YAAA,SAAAC,eAAA;QACA,YAAAY,mBAAA,SAAAC,sBAAA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACA+J,2BAAA,WAAAA,4BAAAC,UAAA,EAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAAlK,mBAAA,GAAAiK,UAAA;MACA,WAAAC,OAAA;QACA,KAAAjK,sBAAA,GAAAgK,UAAA;MACA;MAEA,IAAAA,UAAA;QACAjG,OAAA,CAAAC,GAAA,IAAA0C,MAAA,CAAAuD,OAAA;MACA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,IAAAjI,KAAA;QACAF,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QACAoI,IAAA,OAAAnI,MAAA,CAAAC,KAAA,CAAAkI,IAAA;QACA/G,mBAAA,EAAA1B,IAAA,CAAAC,KAAA,CACAC,YAAA,CAAAC,OAAA,iCACA;MACA;MACA,KAAAuI,OAAA,CAAA5B,IAAA;QAAA6B,IAAA;QAAApI,KAAA,EAAAA;MAAA;IACA;IACA;IACAqI,UAAA,WAAAA,WAAA;MACA,IAAArI,KAAA;QACAF,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QACAoI,IAAA,OAAAnI,MAAA,CAAAC,KAAA,CAAAkI,IAAA;QACA/G,mBAAA,EAAA1B,IAAA,CAAAC,KAAA,CACAC,YAAA,CAAAC,OAAA,iCACA;MACA;MACA,KAAAuI,OAAA,CAAA5B,IAAA;QAAA6B,IAAA;QAAApI,KAAA,EAAAA;MAAA;IACA;IACA;IACAsI,cAAA,WAAAA,eAAA;MACA,KAAAjL,WAAA,GAAAoC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;IACA;IACA2I,YAAA,WAAAA,aAAAhI,IAAA;MACA,KAAAiI,SAAA,CAAAC,GAAA,CAAAlI,IAAA,CAAAmI,QAAA,EAAAnI,IAAA,CAAAoI,QAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAhD,WAAA,WAAAA,YAAApF,IAAA;MACA,KAAAA,IAAA;;MAEA;MACA,IAAAA,IAAA,CAAAwD,UAAA,IAAAxD,IAAA,CAAAwD,UAAA,CAAA9E,MAAA,QAAAsB,IAAA,CAAAwD,UAAA,aAAAxD,IAAA,CAAAwD,UAAA,KAAA5B,SAAA;QACA,IAAA6B,WAAA,GAAAzD,IAAA,CAAAwD,UAAA,CAAA/E,KAAA,MAAAoE,GAAA,WAAAwC,KAAA;UAAA,OAAAhC,UAAA,CAAAgC,KAAA,CAAA3B,IAAA;QAAA,GAAA3D,MAAA,WAAAsF,KAAA;UAAA,QAAA1B,KAAA,CAAA0B,KAAA;QAAA;QACA,IAAA5B,WAAA,CAAA/E,MAAA;UACA,OAAAkF,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAC,OAAA,EAAAP,WAAA;QACA;MACA;;MAEA;MACA,OAAAJ,UAAA,CAAArD,IAAA,CAAAiE,KAAA;IACA;IAEA;IACA;AACA;AACA;AACA;IACAoE,iBAAA,WAAAA,kBAAAC,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAD,UAAA,CAAAE,UAAA;;MAEA;MACA,SAAA7K,YAAA;QACA8K,YAAA,MAAA9K,YAAA;MACA;;MAEA;MACA,KAAAA,YAAA,GAAA+K,UAAA;QACAH,MAAA,CAAA7K,iBAAA,GAAA4K,UAAA;MACA;IACA;IAEA;AACA;AACA;IACAK,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAAjL,YAAA;QACA8K,YAAA,MAAA9K,YAAA;QACA,KAAAA,YAAA;MACA;;MAEA;MACA+K,UAAA;QACAE,MAAA,CAAAlL,iBAAA;MACA;IACA;IAEA;AACA;AACA;IACAmL,iBAAA,WAAAA,kBAAA;MACA,SAAAlL,YAAA;QACA8K,YAAA,MAAA9K,YAAA;QACA,KAAAA,YAAA;MACA;IACA;EACA;EACAmL,OAAA,WAAAA,QAAA;IACA,KAAA9J,IAAA;IACA,KAAA+I,cAAA;EACA;EAEAgB,aAAA,WAAAA,cAAA;IACA;IACA,SAAApL,YAAA;MACA8K,YAAA,MAAA9K,YAAA;MACA,KAAAA,YAAA;IACA;EACA;AACA", "ignoreList": []}]}