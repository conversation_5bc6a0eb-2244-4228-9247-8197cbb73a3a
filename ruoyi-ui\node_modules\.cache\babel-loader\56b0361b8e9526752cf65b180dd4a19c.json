{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\compliance\\one.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\compliance\\one.vue", "mtime": 1753922915380}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\babel.config.js", "mtime": 1750995480034}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_review", "require", "_detail", "_expertStatus", "_entInfo", "PASS", "FAIL", "CHECK_PASS", "CHECK_FAIL", "_default", "exports", "default", "data", "supplierOptions", "<PERSON><PERSON><PERSON><PERSON>", "selectedFactorNode", "selectedSupplierName", "selectedSupplier", "expertInfo", "ratingStateMap", "projectFiles", "isShowResponse", "isShowProcurement", "isDoubleView", "factorDetailList", "entDocResponsePage", "factorsPageMap", "supplierFactorPage", "responsePdfUrl", "procurementPdfUrl", "activeButton", "entDocProcurementPage", "pageProcurement", "attachmentsList", "responsePdfRendered", "procurementPdfRendered", "helpImgList", "factorCodeMap", "checkResult", "checkResultNameMap", "localExpertInfo", "localEntDocResponsePage", "localFactorsPageMap", "hoveredFactorNode", "tooltipTimer", "methods", "validateRatings", "_iterator", "_createForOfIteratorHelper2", "uitems", "_step", "s", "n", "done", "item", "value", "state", "entMethodItemId", "reason", "trim", "$message", "warning", "concat", "itemName", "err", "e", "f", "getCheckResultState", "factorName", "Object", "keys", "length", "code", "check", "resetRatingStateMap", "_i", "_Object$keys", "key", "saveTempRating", "_this", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "response", "_t", "w", "_context", "a", "map", "itemId", "scoringMethodUitemId", "expertResultId", "resultId", "entId", "bidderId", "evaluationResult", "evaluationRemark", "filter", "d", "p", "scoringFactors", "v", "success", "msg", "error", "handleSupplierChange", "supplierName", "_this2", "_callee2", "_yield$Promise$allSet", "_yield$Promise$allSet2", "detailResult", "detailRes", "_this2$factorDetailLi", "factor", "_iterator2", "_step2", "checkRes", "_t2", "_context2", "find", "bidderName", "Promise", "allSettled", "getDetailByPsxx", "projectId", "$route", "query", "scoringMethodItemId", "resDocReviewFactorsDecision", "_slicedToArray2", "status", "evalExpertEvaluationDetails", "console", "showResponseFile", "file", "showFileContrast", "handleShowFactorInfo", "factorItem", "canJumpToPage", "jumpToPage", "$refs", "procurement", "skipPage", "handlePdfRenderStatusChange", "isRendered", "pdfType", "log", "submit", "_this3", "_callee3", "saveResult", "res", "_t3", "_context3", "checkReviewSummary", "evalExpertScoreInfoId", "JSON", "parse", "localStorage", "getItem", "evalState", "editEvalExpertScoreInfo", "$emit", "viewPurchasing", "pageProcurementArr", "push", "i", "j", "_objectSpread2", "secondOffer", "zjhm", "$router", "path", "bidInquiry", "getFactorsPage", "initLocalData", "initExpertInfo", "expertInfoStr", "initPage", "_this4", "_callee4", "_yield$Promise$all", "_yield$Promise$all2", "supplierRes", "approvalRes", "filesRes", "_t4", "_context4", "all", "supplierInfo", "approvalProcess", "filesById", "rows", "isAbandonedBid", "busiTenderNotice", "attachments", "fileType", "scoringMethodUinfo", "scoringMethodItems", "setItem", "stringify", "evalProjectEvaluationProcess", "reduce", "acc", "tenderNoticeFilePath", "downloadFile", "$download", "zip", "filePath", "fileName", "showFactorTooltip", "_this5", "itemRemark", "clearTimeout", "setTimeout", "hideFactorTooltip", "_this6", "clearTooltipTimer", "mounted", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/expertReview/compliance/one.vue"], "sourcesContent": ["<template>\r\n  <!-- 页面主容器，flex布局，分为左中右三部分 -->\r\n  <div class=\"compliance-main\">\r\n    <!-- 左侧内容区，包含标题、操作按钮、PDF预览区 -->\r\n    <div class=\"compliance-left\">\r\n      <!-- 顶部标题和操作按钮区 -->\r\n      <div class=\"compliance-header\">\r\n        <!-- 标题及操作步骤图片 -->\r\n        <div class=\"compliance-title-group\">\r\n          <div class=\"compliance-title\">符合性评审</div> <!-- 页面主标题 -->\r\n          <div class=\"compliance-step-img-group\">\r\n            <div class=\"compliance-step-text\">该页面操作说明</div> <!-- 操作步骤说明文字 -->\r\n            <el-image class=\"compliance-step-img\" :src=\"helpImgList[0]\" :preview-src-list=\"helpImgList\">\r\n            </el-image> <!-- 操作步骤图片，可点击放大 -->\r\n          </div>\r\n        </div>\r\n\t      \r\n\t      <!-- 文件列表 -->\r\n\t      <div class=\"fileList\" style=\"width: 200px; border-right: 1px solid #e6e6e6; border-left: 1px solid #e6e6e6; padding: 10px; overflow-y: auto;\">\r\n\t\t      <div style=\"font-weight: bold; margin-bottom: 10px; color: #333;\">响应文件附件下载</div>\r\n\t\t      <el-card\r\n\t\t\t      v-for=\"(item, index) in attachmentsList\"\r\n\t\t\t      :key=\"index\"\r\n\t\t\t      class=\"fileItem\"\r\n\t\t\t      shadow=\"hover\"\r\n\t\t\t      @click.native=\"downloadFile(item)\"\r\n\t\t\t      style=\"margin-bottom: 8px; cursor: pointer;\"\r\n\t\t      >\r\n\t\t\t      <div style=\"display: flex; align-items: center; padding: 5px;\">\r\n\t\t\t\t      <i class=\"el-icon-document\" style=\"margin-right: 8px; color: #409EFF;\"></i>\r\n\t\t\t\t      <span style=\"font-size: 12px; flex: 1; word-break: break-all;\">{{ item.fileName }}</span>\r\n\t\t\t\t      <i class=\"el-icon-download\" style=\"margin-left: 8px; color: #999;\"></i>\r\n\t\t\t      </div>\r\n\t\t      </el-card>\r\n\t      </div>\r\n\t      \r\n        <!-- 右侧操作按钮区 -->\r\n        <div class=\"compliance-header-btns\">\r\n          <el-button class=\"item-button\" @click=\"bidInquiry\">询标</el-button> <!-- 跳转到询标页面 -->\r\n          <!-- <el-button class=\"item-button\" @click=\"secondOffer\">发起二次报价</el-button> -->\r\n          <div class=\"compliance-header-btns-bottom\">\r\n\t          <el-button\r\n\t\t          :class=\"['item-button', activeButton === 'procurement' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n\t\t          @click=\"viewPurchasing\">采购文件</el-button> <!-- 显示采购文件PDF -->\r\n\t          \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'response' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n              @click=\"showResponseFile\">响应文件</el-button> <!-- 显示响应文件PDF -->\r\n           \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'contrast' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n              @click=\"showFileContrast\">对比</el-button> <!-- 响应文件与采购文件对比 -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- PDF文件预览区，支持单文件和双文件对比 -->\r\n      <div style=\"height:82%\">\r\n        <!-- PDF预览区域 - 保持原始尺寸 -->\r\n        <div class=\"compliance-pdf-group\">\r\n\t        <!-- 采购文件PDF预览 -->\r\n\t        <div v-show=\"isShowProcurement\" :class=\"['compliance-pdf', isDoubleView ? 'compliance-pdf-border-left' : '']\">\r\n\t\t        <!--            <pdfView ref=\"procurement\" :pdfurl=\"procurementPdfUrl\" :uni_key=\"'procurement'\"></pdfView>-->\r\n\t\t        \r\n\t\t        <PdfViewImproved ref=\"procurement\"  :pdfurl=\"procurementPdfUrl\"  :page-height=\"800\"\r\n\t\t                         :buffer-size=\"2\" @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"/>\r\n\t        \r\n\t        </div>\r\n\t        \r\n          <!-- 响应文件PDF预览 -->\r\n          <div v-show=\"isShowResponse\" :class=\"['compliance-pdf', isDoubleView ? 'compliance-pdf-border-right' : '']\">\r\n<!--            <pdfView ref=\"response\" :pdfurl=\"responsePdfUrl\" :uni_key=\"'response'\"></pdfView>-->\r\n\t          \r\n\t          <PdfViewImproved ref=\"response\"  :pdfurl=\"responsePdfUrl\"  :page-height=\"800\" :buffer-size=\"2\"\r\n\t                           @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"/>\r\n\t          \r\n          </div>\r\n          \r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 中间分割线 -->\r\n    <div class=\"compliance-divider\"></div>\r\n    <!-- 右侧评分区 -->\r\n    <div class=\"compliance-right\">\r\n      <!-- 供应商选择下拉框 -->\r\n      <div class=\"compliance-select-group\">\r\n        <el-select class=\"compliance-select\" v-model=\"selectedSupplierName\" placeholder=\"请选择供应商\" @change=\"handleSupplierChange\">\r\n          <el-option v-for=\"item in supplierOptions\" :key=\"item.bidderName\" :label=\"item.bidderName\" :value=\"item.bidderName\">\r\n          </el-option>\r\n        </el-select>\r\n      </div>\r\n      <!-- 评分因子列表及操作区 -->\r\n      <div class=\"compliance-factors-group\" v-if=\"isShowResponse\">\r\n\t      <!-- PDF渲染状态提示 -->\r\n\t      <div v-if=\"!responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t      <i class=\"el-icon-loading\"></i>\r\n\t\t      <span>响应文件正在渲染中，请稍候...</span>\r\n\t      </div>\r\n\t      <div v-else class=\"render-status-tip success\">\r\n\t\t      <i class=\"el-icon-success\"></i>\r\n\t\t      <span>响应文件渲染完成，可以点击跳转</span>\r\n\t      </div>\r\n\t      \r\n        <!-- 判空后再渲染评分因子项列表，防止scoringMethod为null时报错 -->\r\n        <template v-if=\"scoringMethod && scoringMethod.uitems\">\r\n          <div v-for=\"(item, index) in scoringMethod.uitems\" :key=\"index\"\r\n               class=\"factor-item\" style=\"margin-bottom:10px\"\r\n               @mouseenter=\"showFactorTooltip(item)\"\r\n               @mouseleave=\"hideFactorTooltip\" >\r\n\t          <!-- 悬浮框 -->\r\n\t          <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t               class=\"factor-tooltip\"\r\n\t               @mouseenter=\"clearTooltipTimer\"\r\n\t               @mouseleave=\"hideFactorTooltip\">\r\n\t\t          <div class=\"tooltip-header\">\r\n\t\t\t          <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t          <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t          </div>\r\n\t\t          <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t          </div>\r\n\t          \r\n            <div>\r\n              <div class=\"factors\">\r\n                <!-- 评分因子名称，点击可跳转PDF对应页码 -->\r\n                <div class=\"compliance-factor-title-group factor-title\" :class=\"{ 'disabled': !canJumpToPage() }\">\r\n                  <div class=\"compliance-factor-title\" @click=\"handleShowFactorInfo(item)\">\r\n                    {{ item.itemName }}\r\n\t                  <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n                  </div>\r\n                </div>\r\n                <!-- 评分单选按钮（通过/不通过） -->\r\n                <div class=\"compliance-factor-radio-group\">\r\n                  <div>\r\n                    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n                    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- 不通过时填写原因 -->\r\n              <el-input v-if=\"ratingStateMap[item.entMethodItemId].state == 0\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n              </el-input>\r\n              <!-- 系统初验结果展示，绿色为通过，红色为未通过 -->\r\n              <span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n                <i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n                <i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n                {{checkResultNameMap[item.itemName]}}</span>\r\n              <div class=\"compliance-factor-divider\"></div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <!-- 提交按钮区 -->\r\n        <div class=\"compliance-submit-group\">\r\n          <!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"show\">保存</el-button></div> -->\r\n          <div><el-button class=\"item-button-little compliance-submit-btn\" @click=\"submit\">提交</el-button></div>\r\n        </div>\r\n        <!-- 当前选中评分因子的详细说明 -->\r\n        <div class=\"compliance-review-content\">\r\n          <div class=\"compliance-review-title\">评审内容：</div>\r\n          <div class=\"compliance-review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n        </div>\r\n      </div>\r\n\r\n\t    <div class=\"compliance-factors-group\" v-else>\r\n\t\t    <!-- PDF渲染状态提示 -->\r\n\t\t    <div v-if=\"!procurementPdfRendered\" class=\"render-status-tip\">\r\n\t\t\t    <i class=\"el-icon-loading\"></i>\r\n\t\t\t    <span>采购文件正在渲染中，请稍候...</span>\r\n\t\t    </div>\r\n\t\t    <div v-else class=\"render-status-tip success\">\r\n\t\t\t    <i class=\"el-icon-success\"></i>\r\n\t\t\t    <span>采购文件渲染完成，可以点击跳转</span>\r\n\t\t    </div>\r\n\t\t    \r\n\t\t    <!-- 判空后再渲染评分因子项列表，防止scoringMethod为null时报错 -->\r\n\t\t    <template v-if=\"pageProcurement\">\r\n\t\t\t    <div v-for=\"(item, index) in pageProcurement\" :key=\"index\" class=\"factor-item\" style=\"margin-bottom:10px\"\r\n\t\t\t         @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t         @mouseleave=\"hideFactorTooltip\" >\r\n\t\t\t\t    <!-- 悬浮框 -->\r\n\t\t\t\t    <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t\t         class=\"factor-tooltip\"\r\n\t\t\t\t         @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t\t         @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t    <div class=\"tooltip-header\">\r\n\t\t\t\t\t\t    <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t\t    <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t\t    </div>\r\n\t\t\t\t\t    <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t\t    </div>\r\n\t\t\t\t    <div>\r\n\t\t\t\t\t    <div class=\"factors\">\r\n\t\t\t\t\t\t    <!-- 评分因子名称，点击可跳转PDF对应页码 -->\r\n\t\t\t\t\t\t    <div class=\"compliance-factor-title-group factor-title\" :class=\"{ 'disabled': !canJumpToPage() }\">\r\n\t\t\t\t\t\t\t    <div class=\"compliance-factor-title\" @click=\"handleShowFactorInfo(item)\">\r\n\t\t\t\t\t\t\t\t    {{ item.itemName }}\r\n\t\t\t\t\t\t\t\t    <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    <!-- 评分单选按钮（通过/不通过） -->\r\n\t\t\t\t\t\t    <div class=\"compliance-factor-radio-group\">\r\n\t\t\t\t\t\t\t    <div>\r\n\t\t\t\t\t\t\t\t    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n\t\t\t\t\t\t\t\t    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n\t\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t    </div>\r\n\t\t\t\t\t    <!-- 不通过时填写原因 -->\r\n\t\t\t\t\t    <el-input v-if=\"ratingStateMap[item.entMethodItemId].state == 0\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n\t\t\t\t\t    </el-input>\r\n\t\t\t\t\t    <!-- 系统初验结果展示，绿色为通过，红色为未通过 -->\r\n\t\t\t\t\t    <span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n                <i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n                <i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n                {{checkResultNameMap[item.itemName]}}</span>\r\n\t\t\t\t\t    <div class=\"compliance-factor-divider\"></div>\r\n\t\t\t\t    </div>\r\n\t\t\t    </div>\r\n\t\t    </template>\r\n\t\t    <!-- 提交按钮区 -->\r\n\t\t    <div class=\"compliance-submit-group\">\r\n\t\t\t    <!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"show\">保存</el-button></div> -->\r\n\t\t\t    <div><el-button class=\"item-button-little compliance-submit-btn\" @click=\"submit\">提交</el-button></div>\r\n\t\t    </div>\r\n\t\t    <!-- 当前选中评分因子的详细说明 -->\r\n\t\t    <div class=\"compliance-review-content\">\r\n\t\t\t    <div class=\"compliance-review-title\">评审内容：</div>\r\n\t\t\t    <div class=\"compliance-review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n\t\t    </div>\r\n\t    </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 常量定义\r\nconst PASS = '1'; // 通过\r\nconst FAIL = '0'; // 不通过\r\nconst CHECK_PASS = '系统初验通过'; // 系统初验通过文本\r\nconst CHECK_FAIL = '系统初验未通过'; // 系统初验未通过文本\r\n\r\nimport {\r\n  supplierInfo, // 获取供应商信息API\r\n  approvalProcess, // 获取评分方法API\r\n  scoringFactors, // 提交评分因子API\r\n  checkReviewSummary, // 检查评审汇总API\r\n  filesById, // 获取项目相关文件API\r\n} from \"@/api/expert/review\"; // 导入专家评审相关API\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\"; // 获取评分详情API\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\"; // 编辑专家评分状态API\r\nimport { resDocReviewFactorsDecision } from \"@/api/docResponse/entInfo\"; // 获取响应文件评审因子决策API\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      supplierOptions: [], // 供应商下拉选项列表\r\n      scoringMethod: null, // 当前评分方法对象\r\n      selectedFactorNode: {}, // 当前选中的评分因子节点\r\n      selectedSupplierName: '', // 当前选中的供应商名称\r\n      selectedSupplier: {}, // 当前选中的供应商对象\r\n      expertInfo: {}, // 当前专家信息\r\n      ratingStateMap: {}, // 评分项状态映射（key为评分项ID，value为{state, reason}）\r\n      projectFiles: {}, // 项目相关文件对象\r\n      isShowResponse: false, // 是否显示响应文件\r\n      isShowProcurement: false, // 是否显示采购文件\r\n      isDoubleView: false, // 是否双文件对比模式\r\n      factorDetailList: [], // 评分因子详细列表\r\n      entDocResponsePage: null, // 企业响应文件页码信息\r\n      factorsPageMap: null, // 供应商因子页码映射\r\n      supplierFactorPage: null, // 当前供应商因子页码\r\n      responsePdfUrl: null, // 响应文件PDF地址\r\n      procurementPdfUrl: null, // 采购文件PDF地址\r\n\r\n      // 按钮状态管理\r\n      activeButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n\t    entDocProcurementPage: {}, // 采购文件页码信息\r\n      pageProcurement:[], // 采购文件的评分项\r\n      attachmentsList:[], // 文件列表\r\n\t    \r\n\t    // PDF渲染状态管理\r\n\t    responsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n\t    procurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n\t    helpImgList: [\"/evalution/help.jpg\"], // 操作帮助图片列表\r\n      // 评分项名称与后端字段映射\r\n      factorCodeMap: {\r\n        \"特定资格要求\": \"zgzs\",\r\n        \"响应内容\": \"jsplb\",\r\n        \"采购需求\": \"jsplb\",\r\n        \"供货期限\": \"ghqx\",\r\n        \"投标报价\": \"tbbj\"\r\n      },\r\n      checkResult: {}, // 系统初验结果对象\r\n      // 系统初验结果名称映射\r\n      checkResultNameMap: {\r\n        \"符合《中华人民共和国政府采购法》第二十二条规定\": CHECK_PASS,\r\n        \"特定资格要求\": CHECK_PASS,\r\n        \"信用查询\": CHECK_PASS,\r\n        \"响应人名称\": CHECK_PASS,\r\n        \"响应内容\": CHECK_PASS,\r\n        \"采购需求\": CHECK_PASS,\r\n        \"供货期限\": CHECK_PASS,\r\n        \"投标报价\": CHECK_PASS\r\n      },\r\n      // 本地缓存数据\r\n      localExpertInfo: null, // 本地专家信息\r\n      localEntDocResponsePage: null, // 本地响应文件页码\r\n      localFactorsPageMap: null, // 本地因子页码映射\r\n\t    \r\n\t    // 悬停状态管理\r\n\t    hoveredFactorNode: null, // 悬停时的评分项\r\n\t    tooltipTimer: null, // 悬浮框显示定时器\r\n    };\r\n  },\r\n  methods: {\r\n    /**\r\n     * 校验所有评分项是否填写完整\r\n     * @returns {boolean} 是否全部填写\r\n     */\r\n    validateRatings() {\r\n      for (const item of this.scoringMethod.uitems) { // 遍历所有评分项\r\n        const state = this.ratingStateMap[item.entMethodItemId].state; // 获取评分状态\r\n        const reason = this.ratingStateMap[item.entMethodItemId].reason; // 获取评分原因\r\n        // 评分结果未填写\r\n        if (state === null || state === '') {\r\n          // this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`); // 提示未填写\r\n          return true;\r\n        }\r\n        // 不通过但未填写原因\r\n        if (state === FAIL && (!reason || reason.trim() === '')) {\r\n          this.$message.warning(`${item.itemName}评审不通过但未填写备注，不进行保存`); // 提示未填写原因\r\n          return false;\r\n        }\r\n      }\r\n      return true; // 全部填写返回true\r\n    },\r\n    /**\r\n     * 获取系统初验结果（通过/未通过）\r\n     * @param {string} factorName 评分项名称\r\n     * @returns {string} 1-通过 0-未通过\r\n     */\r\n    getCheckResultState(factorName) {\r\n      if (!this.checkResult || Object.keys(this.checkResult).length === 0) return ''; // 没有初验结果直接返回空\r\n      let code = this.factorCodeMap[factorName]; // 获取评分项对应的后端字段\r\n      let check = PASS; // 默认通过\r\n      if (code) {\r\n        check = this.checkResult[code]; // 获取初验结果\r\n        // 投标报价特殊处理\r\n        if (factorName === \"投标报价\" && check === PASS) {\r\n          check = this.checkResult['mxbjb']; // 明细报价表\r\n        }\r\n      }\r\n      // 设置初验结果名称\r\n      if (check === FAIL) {\r\n        this.checkResultNameMap[factorName] = CHECK_FAIL; // 未通过\r\n      } else {\r\n        check = PASS;\r\n        this.checkResultNameMap[factorName] = CHECK_PASS; // 通过\r\n      }\r\n      return check; // 返回初验结果\r\n    },\r\n    /**\r\n     * 重置所有评分项的状态\r\n     */\r\n    resetRatingStateMap() {\r\n      if (!this.scoringMethod) return; // 没有评分方法直接返回\r\n      for (const key of Object.keys(this.ratingStateMap)) { // 遍历所有评分项\r\n        this.ratingStateMap[key].state = null; // 重置状态\r\n        this.ratingStateMap[key].reason = ''; // 重置原因\r\n      }\r\n    },\r\n    /**\r\n     * 临时保存评分结果到后端\r\n     * 校验通过后才会保存\r\n     * @returns {boolean} 保存是否成功\r\n     */\r\n    async saveTempRating() {\r\n      if (!this.validateRatings()) return false; // 校验不通过不保存，返回false\r\n      // 构造提交数据\r\n      const data = this.scoringMethod.uitems.map(item => {\r\n        const itemId = item.entMethodItemId; // 获取评分项ID\r\n        return {\r\n          scoringMethodUitemId: itemId, // 评分项ID\r\n          expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n          entId: this.selectedSupplier.bidderId, // 供应商ID\r\n          evaluationResult: this.ratingStateMap[itemId].state, // 评分结果\r\n          evaluationRemark: this.ratingStateMap[itemId].reason || '' // 评分原因\r\n        };\r\n      }).filter(d => d.evaluationResult !== null && d.evaluationResult !== ''); // 过滤未填写的项\r\n      if (data.length > 0) {\r\n        try {\r\n          const response = await scoringFactors(data); // 提交评分因子\r\n          if (response.code === 200) {\r\n            this.$message.success(\"保存成功\"); // 保存成功提示\r\n            return true; // 保存成功返回true\r\n          } else {\r\n            this.$message.warning(response.msg); // 保存失败提示\r\n            return false; // 保存失败返回false\r\n          }\r\n        } catch (e) {\r\n          this.$message.error(\"保存失败\"); // 异常提示\r\n          return false; // 异常返回false\r\n        }\r\n      }\r\n      return true; // 没有数据需要保存时也返回true\r\n    },\r\n    /**\r\n     * 供应商切换事件，切换时自动保存上一个供应商评分，并并发获取新供应商的评分详情和系统初验\r\n     * @param {string} supplierName 供应商名称\r\n     */\r\n    async handleSupplierChange(supplierName) {\r\n      // 切换前保存上一个供应商评分\r\n      if (Object.keys(this.selectedSupplier).length !== 0) {\r\n        await this.saveTempRating(); // 保存评分\r\n      }\r\n      // 查找当前选中的供应商对象\r\n      this.selectedSupplier = this.supplierOptions.find(item => item.bidderName === supplierName); // 查找供应商\r\n      // 获取当前供应商因子页码\r\n      this.supplierFactorPage = this.factorsPageMap[this.selectedSupplier.bidderId]; // 获取页码\r\n      // 并发获取评分详情和系统初验\r\n      // 使用 Promise.allSettled 让两个请求独立执行，互不影响\r\n      try {\r\n        const [detailResult, checkResult] = await Promise.allSettled([\r\n          getDetailByPsxx({\r\n            expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n            projectId: this.$route.query.projectId, // 项目ID\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项ID\r\n          }),\r\n          resDocReviewFactorsDecision({\r\n            projectId: this.$route.query.projectId, // 项目ID\r\n            entId: this.selectedSupplier.bidderId, // 供应商ID\r\n          })\r\n        ]);\r\n\r\n        // 处理评分详情请求结果\r\n        if (detailResult.status === 'fulfilled') {\r\n          const detailRes = detailResult.value;\r\n          if (detailRes.code === 200) {\r\n            this.factorDetailList = detailRes.data; // 评分详情列表\r\n            const factor = this.factorDetailList.find(item => item.bidderName === supplierName)?.evalExpertEvaluationDetails; // 当前供应商评分详情\r\n            this.resetRatingStateMap(); // 重置评分状态\r\n            if (factor) {\r\n              for (const item of factor) {\r\n                this.ratingStateMap[item.scoringMethodUitemId].reason = item.evaluationRemark; // 设置评分原因\r\n                this.ratingStateMap[item.scoringMethodUitemId].state = item.evaluationResult; // 设置评分结果\r\n              }\r\n            }\r\n          } else {\r\n            this.$message.warning(detailRes.msg); // 评分详情获取失败\r\n          }\r\n        } else {\r\n          console.error(\"获取评分详情失败:\", detailResult.reason);\r\n          this.$message.error(\"获取评分详情失败\"); // 评分详情请求异常\r\n        }\r\n\r\n        // 处理系统初验请求结果\r\n        if (checkResult.status === 'fulfilled') {\r\n          const checkRes = checkResult.value;\r\n          if (checkRes.code === 200) {\r\n            this.checkResult = checkRes.data; // 设置初验结果\r\n          } else {\r\n            console.error(\"获取系统初验结果失败:\", checkRes.msg);\r\n            this.$message.warning(\"获取系统初验结果失败\"); // 系统初验获取失败\r\n          }\r\n        } else {\r\n          console.error(\"系统初验请求失败:\", checkResult.reason);\r\n          this.$message.error(\"系统初验请求失败\"); // 系统初验请求异常\r\n        }\r\n      } catch (e) {\r\n        console.error(\"请求处理异常:\", e);\r\n        this.$message.error(\"获取供应商详情失败\"); // 异常提示\r\n      }\r\n      // 默认显示响应文件\r\n      this.showResponseFile();\r\n    },\r\n    /**\r\n     * 显示响应文件PDF\r\n     */\r\n    showResponseFile() {\r\n      if (!this.selectedSupplier || Object.keys(this.selectedSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n      this.activeButton = 'response'; // 设置当前激活按钮\r\n      this.isDoubleView = false; // 单文件模式\r\n      this.isShowProcurement = false; // 不显示采购文件\r\n      this.isShowResponse = true; // 显示响应文件\r\n      this.responsePdfUrl = this.projectFiles.file[this.selectedSupplier.bidderId]; // 设置响应文件PDF地址\r\n    },\r\n    /**\r\n     * 文件对比（双文件模式）\r\n     */\r\n    showFileContrast() {\r\n      if (!this.selectedSupplier || Object.keys(this.selectedSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n      this.activeButton = 'contrast'; // 设置当前激活按钮\r\n      this.isDoubleView = true; // 双文件模式\r\n      this.isShowProcurement = true; // 显示采购文件\r\n      this.isShowResponse = true; // 显示响应文件\r\n      this.responsePdfUrl = this.projectFiles.file[this.selectedSupplier.bidderId]; // 设置响应文件PDF地址\r\n    },\r\n    /**\r\n     * 点击评分项名称，跳转到对应PDF页码\r\n     * @param {Object} factorItem 当前评分因子项\r\n     */\r\n    handleShowFactorInfo(factorItem) {\r\n\t    // 检查PDF是否渲染完成\r\n\t    if (!this.canJumpToPage()) {\r\n\t\t    this.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t    return;\r\n\t    }\r\n\t\t\t\r\n      this.selectedFactorNode = factorItem; // 设置当前选中因子\r\n\r\n      // 如果只显示采购文件，使用采购文件页码信息\r\n      if (this.isShowProcurement && !this.isShowResponse) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        if (factorItem.jumpToPage) {\r\n          this.$refs.procurement.skipPage(factorItem.jumpToPage); // 采购文件跳页\r\n        } else if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 如果显示响应文件或对比模式，需要选择供应商\r\n      if (!this.supplierFactorPage || Object.keys(this.supplierFactorPage).length === 0) {\r\n        this.$message.warning(\"请先选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n\r\n      // 跳转到响应文件对应页码\r\n      if (this.isShowResponse && this.$refs.response) {\r\n\t      if (!this.responsePdfRendered) {\r\n\t\t      this.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n        this.$refs.response.skipPage(this.supplierFactorPage[this.selectedFactorNode.itemName]); // 响应文件跳页\r\n      }\r\n\r\n      // 跳转到采购文件对应页码\r\n      if (this.isShowProcurement && this.$refs.procurement) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        // 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n        if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n        } else {\r\n          // 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n          // 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n        }\r\n      }\r\n    },\r\n\t  \r\n\t  /**\r\n\t   * 检查是否可以跳转页面\r\n\t   * @returns {boolean} 是否可以跳转\r\n\t   */\r\n\t  canJumpToPage() {\r\n\t\t  // 如果只显示采购文件\r\n\t\t  if (this.isShowProcurement && !this.isShowResponse) {\r\n\t\t\t  return this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  // 如果只显示响应文件\r\n\t\t  if (this.isShowResponse && !this.isShowProcurement) {\r\n\t\t\t  return this.responsePdfRendered;\r\n\t\t  }\r\n\t\t  // 如果对比模式（两个都显示）\r\n\t\t  if (this.isShowResponse && this.isShowProcurement) {\r\n\t\t\t  return this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  return false;\r\n\t  },\r\n\t  /**\r\n\t   * 处理PDF渲染状态变化\r\n\t   * @param {boolean} isRendered 是否渲染完成\r\n\t   * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t   */\r\n\t  handlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t\t  if (pdfType === 'response') {\r\n\t\t\t  this.responsePdfRendered = isRendered;\r\n\t\t  } else if (pdfType === 'procurement') {\r\n\t\t\t  this.procurementPdfRendered = isRendered;\r\n\t\t  }\r\n\t\t  \r\n\t\t  if (isRendered) {\r\n\t\t\t  console.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t\t  }\r\n\t  },\r\n\t  \r\n    /**\r\n     * 提交评分并修改专家进度\r\n     */\r\n    async submit() {\r\n      // 先保存评分，如果保存失败则不继续提交\r\n      const saveResult = await this.saveTempRating();\r\n      if (!saveResult) {\r\n        // 保存失败，不继续提交流程\r\n        return;\r\n      }\r\n\r\n      const data = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项ID\r\n      };\r\n      try {\r\n        const response = await checkReviewSummary(data); // 检查评审汇总\r\n\r\n        if (response.code === 200) {\r\n          // 修改专家进度\r\n          const status = {\r\n            evalExpertScoreInfoId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\")).evalExpertScoreInfoId, // 专家评分信息ID\r\n            evalState: 1, // 进度状态\r\n          };\r\n          const res = await editEvalExpertScoreInfo(status); // 编辑专家评分状态\r\n          if (res.code === 200) {\r\n            this.$message.success(\"提交成功\"); // 提交成功提示\r\n          }\r\n          this.$emit(\"send\", \"two\"); // 发送事件\r\n        } else {\r\n          this.$message.warning(response.msg); // 提交失败提示\r\n        }\r\n      } catch (e) {\r\n        this.$message.error(\"提交失败\"); // 异常提示\r\n      }\r\n    },\r\n    /**\r\n     * 显示采购文件PDF\r\n     */\r\n    viewPurchasing() {\r\n      this.activeButton = 'procurement'; // 设置当前激活按钮\r\n      this.isDoubleView = false; // 单文件模式\r\n      this.isShowResponse = false; // 不显示响应文件\r\n      this.isShowProcurement = true; // 显示采购文件\r\n      // 右侧评分项显示为采购文件的评分项\r\n      let pageProcurementArr = []; // 采购文件评分项数组\r\n\r\n      for (let item in this.entDocProcurementPage){\r\n        pageProcurementArr.push({\r\n          itemName: item,\r\n          jumpToPage: this.entDocProcurementPage[item]\r\n        })\r\n      }\r\n\r\n      console.log(this.scoringMethod.uitems);\r\n      console.log(pageProcurementArr)\r\n      this.pageProcurement = [];\r\n      for (let i = 0; i < this.scoringMethod.uitems.length;i++){\r\n        for (let j = 0; j < pageProcurementArr.length;j++){\r\n          if (this.scoringMethod.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n            this.pageProcurement.push({...this.scoringMethod.uitems[i],...pageProcurementArr[j]});\r\n          }\r\n        }\r\n      }\r\n      console.log(this.pageProcurement)\r\n    },\r\n    /**\r\n     * 跳转到二次报价页面\r\n     */\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        zjhm: this.$route.query.zjhm, // 专家证件号码\r\n        scoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")), // 二次报价评分方法项ID\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query }); // 跳转页面\r\n    },\r\n    /**\r\n     * 跳转到询标页面\r\n     */\r\n    bidInquiry() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        zjhm: this.$route.query.zjhm, // 专家证件号码\r\n        scoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")), // 询标评分方法项ID\r\n      };\r\n      this.$router.push({ path: \"/bidInquiry\", query }); // 跳转页面\r\n    },\r\n    /**\r\n     * 获取因素对应页码（从本地缓存）\r\n     */\r\n    getFactorsPage() {\r\n      this.factorsPageMap = JSON.parse(localStorage.getItem(\"entDocResponsePage\")); // 获取因子页码映射\r\n    },\r\n    /**\r\n     * 初始化专家和本地数据，只在mounted时调用一次\r\n     */\r\n    initLocalData() {\r\n      try {\r\n        this.localExpertInfo = JSON.parse(localStorage.getItem(\"expertInfo\") || \"{}\"); // 获取本地专家信息\r\n        this.localEntDocResponsePage = JSON.parse(localStorage.getItem(\"entDocResponsePage\") || \"{}\"); // 获取本地响应文件页码\r\n        this.localFactorsPageMap = JSON.parse(localStorage.getItem(\"entDocResponsePage\") || \"{}\"); // 获取本地因子页码映射\r\n        this.expertInfo = this.localExpertInfo; // 设置专家信息\r\n        this.entDocResponsePage = this.localEntDocResponsePage; // 设置响应文件页码\r\n        this.factorsPageMap = this.localFactorsPageMap; // 设置因子页码映射\r\n        console.log(\"本地数据已初始化\", { expertInfo: this.expertInfo });\r\n      } catch (error) {\r\n        console.error(\"初始化本地数据失败:\", error);\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 初始化专家信息（用于响应专家信息更新）\r\n     */\r\n    initExpertInfo() {\r\n      try {\r\n        const expertInfoStr = localStorage.getItem(\"expertInfo\");\r\n        if (expertInfoStr) {\r\n          this.localExpertInfo = JSON.parse(expertInfoStr);\r\n          this.expertInfo = this.localExpertInfo;\r\n          console.log(\"专家信息已刷新\", this.expertInfo);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"刷新专家信息失败:\", error);\r\n      }\r\n    },\r\n    /**\r\n     * 页面初始化，加载供应商、评分方法、文件等（并发请求）\r\n     */\r\n    async initPage() {\r\n      this.initLocalData(); // 初始化本地数据\r\n      try {\r\n        // 并发获取供应商、评分方法、文件\r\n        const [supplierRes, approvalRes, filesRes] = await Promise.all([\r\n          supplierInfo({ projectId: this.$route.query.projectId }), // 获取供应商\r\n          approvalProcess(this.$route.query.projectId, this.expertInfo.resultId), // 获取评分方法\r\n          filesById(this.$route.query.projectId) // 获取项目文件\r\n        ]);\r\n        // 处理供应商\r\n        if (supplierRes.code === 200) {\r\n          this.supplierOptions = supplierRes.rows.filter(item => item.isAbandonedBid == 0); // 过滤未弃标供应商\r\n        } else {\r\n          this.$message.warning(supplierRes.msg); // 获取失败提示\r\n        }\r\n        // 处理评分方法\r\n        if (approvalRes.code === 200) {\r\n\t        this.attachmentsList = approvalRes.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n          this.scoringMethod = approvalRes.data.scoringMethodUinfo.scoringMethodItems.find(\r\n            item => item.scoringMethodItemId == this.$route.query.scoringMethodItemId\r\n          ); // 获取当前评分方法\r\n          localStorage.setItem(\"evalProjectEvaluationProcess\", JSON.stringify(this.scoringMethod.evalProjectEvaluationProcess)); // 缓存评分流程\r\n          this.ratingStateMap = this.scoringMethod.uitems.reduce((acc, item) => {\r\n            acc[item.entMethodItemId] = { state: null, reason: '' }; // 初始化评分状态\r\n            return acc;\r\n          }, {});\r\n        } else {\r\n          this.$message.warning(approvalRes.msg); // 获取失败提示\r\n        }\r\n        // 处理文件\r\n        if (filesRes.code === 200) {\r\n          this.projectFiles = filesRes.data; // 设置项目文件\r\n          if (this.projectFiles.tenderNoticeFilePath) {\r\n            this.procurementPdfUrl = this.projectFiles.tenderNoticeFilePath; // 设置采购文件PDF\r\n          }\r\n          // if (this.projectFiles.file) {\r\n          //   this.responsePdfUrl = this.projectFiles.file[0]; // 设置响应文件PDF\r\n          // }\r\n        } else {\r\n          this.$message.warning(filesRes.msg); // 获取失败提示\r\n        }\r\n      } catch (e) {\r\n        this.$message.error(\"页面初始化失败\"); // 异常提示\r\n      }\r\n    },\r\n\t  \r\n\t  downloadFile(item){\r\n\t\t  this.$download.zip(item.filePath,item.fileName);\r\n\t  },\r\n\t  \r\n\t  \r\n\t  // ========== 悬停相关 ==========\r\n\t  /**\r\n\t   * 显示评分项悬浮框\r\n\t   * @param {Object} factorItem 评分项对象\r\n\t   */\r\n\t  showFactorTooltip(factorItem) {\r\n\t\t  if (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\t\t  \r\n\t\t  // 清除之前的定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t  this.tooltipTimer = setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = factorItem;\r\n\t\t  }, 300); // 300ms延迟\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 隐藏评分项悬浮框\r\n\t   */\r\n\t  hideFactorTooltip() {\r\n\t\t  // 清除定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t  setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = null;\r\n\t\t  }, 100);\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t   */\r\n\t  clearTooltipTimer() {\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t  }\r\n  },\r\n  mounted() {\r\n\t  this.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\")); // 初始化采购文件页码信息\r\n    this.initPage(); // 页面挂载时初始化数据\r\n    this.getFactorsPage(); // 获取因子页码\r\n  },\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.compliance-main {\r\n  min-height: 57vh;\r\n  display: flex;\r\n}\r\n.compliance-left {\r\n  min-height: 57vh;\r\n  width: 79%;\r\n}\r\n.compliance-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.compliance-title-group {\r\n  display: flex;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333;\r\n}\r\n.compliance-title {\r\n  // nothing extra\r\n}\r\n.compliance-step-img-group {\r\n  display: grid;\r\n  justify-items: center;\r\n  position: relative;\r\n  bottom: -30px;\r\n}\r\n.compliance-step-text {\r\n  font-size: 12px;\r\n}\r\n.compliance-step-img {\r\n  width: 80px;\r\n  height: 30px;\r\n  margin-right: 20px;\r\n}\r\n.compliance-header-btns {\r\n  text-align: right;\r\n}\r\n.compliance-header-btns-bottom {\r\n  margin-top: 20px;\r\n}\r\n.compliance-blue-btn {\r\n  background-color: #176ADB !important;\r\n  color: #fff !important;\r\n  border: 1px solid #176ADB !important;\r\n}\r\n.compliance-blue-btn-active {\r\n  background-color: #FF6B35 !important;\r\n  color: #fff !important;\r\n  border: 1px solid #FF6B35 !important;\r\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.compliance-pdf-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  height: 82%;\r\n}\r\n.compliance-pdf {\r\n  width: 49%;\r\n}\r\n.compliance-pdf-border-right {\r\n  border-right: 1px solid #176ADB;\r\n}\r\n.compliance-pdf-border-left {\r\n  border-left: 1px solid #176ADB;\r\n}\r\n.compliance-divider {\r\n  min-height: 57vh;\r\n  width: 1%;\r\n  background-color: #F5F5F5;\r\n}\r\n.compliance-right {\r\n  min-height: 57vh;\r\n  width: 20%;\r\n}\r\n.compliance-select-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.compliance-select {\r\n  width: 100%;\r\n}\r\n.compliance-factors-group {\r\n  padding: 15px 20px;\r\n}\r\n.compliance-factor-title-group {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  text-align: left;\r\n  width: 98%;\r\n}\r\n.compliance-factor-title {\r\n  cursor: pointer;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 16px;\r\n  color: #333;\r\n  letter-spacing: 0;\r\n}\r\n.compliance-factor-radio-group {\r\n  display: flex;\r\n  width: 100%;\r\n  justify-content: flex-end;\r\n}\r\n.compliance-factor-divider {\r\n  height: 1px;\r\n  background-color: #DCDFE6;\r\n  margin-top: 10px;\r\n}\r\n.compliance-submit-group {\r\n  display: flex;\r\n  margin: 32px 0;\r\n  justify-content: space-evenly;\r\n}\r\n.compliance-submit-btn {\r\n  background-color: #176ADB;\r\n}\r\n.compliance-review-content {\r\n  text-align: left;\r\n  font-size: 14px;\r\n}\r\n.compliance-review-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 15px;\r\n  color: #176ADB;\r\n  letter-spacing: 0;\r\n}\r\n.compliance-review-html {\r\n  padding: 6px 30px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n\t  transition: all 0.3s ease;\r\n\t  padding: 4px 8px;\r\n\t  border-radius: 4px;\r\n\t  \r\n\t  &:hover {\r\n\t\t  background-color: #f0f8ff;\r\n\t\t  color: #176ADB;\r\n\t\t  transform: translateX(2px);\r\n\t  }\r\n  }\r\n}\r\n.item-button {\r\n  border: 1px solid #979797;\r\n  width: 150px;\r\n  height: 36px;\r\n  margin: 0 10px;\r\n  font-weight: 700;\r\n  font-size: 17px;\r\n  border-radius: 6px;\r\n  color: #333;\r\n  &:hover {\r\n    color: #333;\r\n  }\r\n}\r\n.item-button-little {\r\n  width: 124px;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #fff;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n\t.fileItem {\r\n\t\ttransition: all 0.3s ease;\r\n\t\t&:hover {\r\n\t\t\ttransform: translateY(-2px);\r\n\t\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\t\t}\r\n\t\t\r\n\t\t::v-deep .el-card__body {\r\n\t\t\tpadding: 0;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\t\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\t\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\t\t\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\t\t\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\t\t\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiPA,IAAAA,OAAA,GAAAC,OAAA;AAOA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,aAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAfA;AACA,IAAAI,IAAA;AACA,IAAAC,IAAA;AACA,IAAAC,UAAA;AACA,IAAAC,UAAA;;AAQA;AACA;AACA;AACA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,eAAA;MAAA;MACAC,aAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,UAAA;MAAA;MACAC,cAAA;MAAA;MACAC,YAAA;MAAA;MACAC,cAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,YAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,cAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,cAAA;MAAA;MACAC,iBAAA;MAAA;;MAEA;MACAC,YAAA;MAAA;;MAEAC,qBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,eAAA;MAAA;;MAEA;MACAC,mBAAA;MAAA;MACAC,sBAAA;MAAA;;MAEAC,WAAA;MAAA;MACA;MACAC,aAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACAC,WAAA;MAAA;MACA;MACAC,kBAAA;QACA,2BAAAhC,UAAA;QACA,UAAAA,UAAA;QACA,QAAAA,UAAA;QACA,SAAAA,UAAA;QACA,QAAAA,UAAA;QACA,QAAAA,UAAA;QACA,QAAAA,UAAA;QACA,QAAAA;MACA;MACA;MACAiC,eAAA;MAAA;MACAC,uBAAA;MAAA;MACAC,mBAAA;MAAA;;MAEA;MACAC,iBAAA;MAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA;IACA;AACA;AACA;AACA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAArC,OAAA,EACA,KAAAG,aAAA,CAAAmC,MAAA;QAAAC,KAAA;MAAA;QAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAK,CAAA,IAAAC,IAAA;UAAA,IAAAC,IAAA,GAAAJ,KAAA,CAAAK,KAAA;UAAA;UACA,IAAAC,KAAA,QAAArC,cAAA,CAAAmC,IAAA,CAAAG,eAAA,EAAAD,KAAA;UACA,IAAAE,MAAA,QAAAvC,cAAA,CAAAmC,IAAA,CAAAG,eAAA,EAAAC,MAAA;UACA;UACA,IAAAF,KAAA,aAAAA,KAAA;YACA;YACA;UACA;UACA;UACA,IAAAA,KAAA,KAAAlD,IAAA,MAAAoD,MAAA,IAAAA,MAAA,CAAAC,IAAA;YACA,KAAAC,QAAA,CAAAC,OAAA,IAAAC,MAAA,CAAAR,IAAA,CAAAS,QAAA;YACA;UACA;QACA;MAAA,SAAAC,GAAA;QAAAjB,SAAA,CAAAkB,CAAA,CAAAD,GAAA;MAAA;QAAAjB,SAAA,CAAAmB,CAAA;MAAA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAC,mBAAA,WAAAA,oBAAAC,UAAA;MACA,UAAA9B,WAAA,IAAA+B,MAAA,CAAAC,IAAA,MAAAhC,WAAA,EAAAiC,MAAA;MACA,IAAAC,IAAA,QAAAnC,aAAA,CAAA+B,UAAA;MACA,IAAAK,KAAA,GAAApE,IAAA;MACA,IAAAmE,IAAA;QACAC,KAAA,QAAAnC,WAAA,CAAAkC,IAAA;QACA;QACA,IAAAJ,UAAA,eAAAK,KAAA,KAAApE,IAAA;UACAoE,KAAA,QAAAnC,WAAA;QACA;MACA;MACA;MACA,IAAAmC,KAAA,KAAAnE,IAAA;QACA,KAAAiC,kBAAA,CAAA6B,UAAA,IAAA5D,UAAA;MACA;QACAiE,KAAA,GAAApE,IAAA;QACA,KAAAkC,kBAAA,CAAA6B,UAAA,IAAA7D,UAAA;MACA;MACA,OAAAkE,KAAA;IACA;IACA;AACA;AACA;IACAC,mBAAA,WAAAA,oBAAA;MACA,UAAA5D,aAAA;MACA,SAAA6D,EAAA,MAAAC,YAAA,GAAAP,MAAA,CAAAC,IAAA,MAAAnD,cAAA,GAAAwD,EAAA,GAAAC,YAAA,CAAAL,MAAA,EAAAI,EAAA;QAAA,IAAAE,GAAA,GAAAD,YAAA,CAAAD,EAAA;QAAA;QACA,KAAAxD,cAAA,CAAA0D,GAAA,EAAArB,KAAA;QACA,KAAArC,cAAA,CAAA0D,GAAA,EAAAnB,MAAA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAoB,cAAA,WAAAA,eAAA;MAAA,IAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAArE,OAAA,mBAAAsE,aAAA,CAAAtE,OAAA,IAAAuE,CAAA,UAAAC,QAAA;QAAA,IAAAvE,IAAA,EAAAwE,QAAA,EAAAC,EAAA;QAAA,WAAAJ,aAAA,CAAAtE,OAAA,IAAA2E,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAnC,CAAA;YAAA;cAAA,IACA2B,KAAA,CAAAjC,eAAA;gBAAAyC,QAAA,CAAAnC,CAAA;gBAAA;cAAA;cAAA,OAAAmC,QAAA,CAAAC,CAAA;YAAA;cAAA;cACA;cACA5E,IAAA,GAAAmE,KAAA,CAAAjE,aAAA,CAAAmC,MAAA,CAAAwC,GAAA,WAAAnC,IAAA;gBACA,IAAAoC,MAAA,GAAApC,IAAA,CAAAG,eAAA;gBACA;kBACAkC,oBAAA,EAAAD,MAAA;kBAAA;kBACAE,cAAA,EAAAb,KAAA,CAAA7D,UAAA,CAAA2E,QAAA;kBAAA;kBACAC,KAAA,EAAAf,KAAA,CAAA9D,gBAAA,CAAA8E,QAAA;kBAAA;kBACAC,gBAAA,EAAAjB,KAAA,CAAA5D,cAAA,CAAAuE,MAAA,EAAAlC,KAAA;kBAAA;kBACAyC,gBAAA,EAAAlB,KAAA,CAAA5D,cAAA,CAAAuE,MAAA,EAAAhC,MAAA;gBACA;cACA,GAAAwC,MAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAAH,gBAAA,aAAAG,CAAA,CAAAH,gBAAA;cAAA;cAAA,MACApF,IAAA,CAAA2D,MAAA;gBAAAgB,QAAA,CAAAnC,CAAA;gBAAA;cAAA;cAAAmC,QAAA,CAAAa,CAAA;cAAAb,QAAA,CAAAnC,CAAA;cAAA,OAEA,IAAAiD,sBAAA,EAAAzF,IAAA;YAAA;cAAAwE,QAAA,GAAAG,QAAA,CAAAe,CAAA;cAAA,MACAlB,QAAA,CAAAZ,IAAA;gBAAAe,QAAA,CAAAnC,CAAA;gBAAA;cAAA;cACA2B,KAAA,CAAAnB,QAAA,CAAA2C,OAAA;cAAA,OAAAhB,QAAA,CAAAC,CAAA,IACA;YAAA;cAEAT,KAAA,CAAAnB,QAAA,CAAAC,OAAA,CAAAuB,QAAA,CAAAoB,GAAA;cAAA,OAAAjB,QAAA,CAAAC,CAAA,IACA;YAAA;cAAAD,QAAA,CAAAnC,CAAA;cAAA;YAAA;cAAAmC,QAAA,CAAAa,CAAA;cAAAf,EAAA,GAAAE,QAAA,CAAAe,CAAA;cAGAvB,KAAA,CAAAnB,QAAA,CAAA6C,KAAA;cAAA,OAAAlB,QAAA,CAAAC,CAAA,IACA;YAAA;cAAA,OAAAD,QAAA,CAAAC,CAAA,IAGA;UAAA;QAAA,GAAAL,OAAA;MAAA;IACA;IACA;AACA;AACA;AACA;IACAuB,oBAAA,WAAAA,qBAAAC,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAA5B,kBAAA,CAAArE,OAAA,mBAAAsE,aAAA,CAAAtE,OAAA,IAAAuE,CAAA,UAAA2B,SAAA;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,YAAA,EAAA1E,WAAA,EAAA2E,SAAA,EAAAC,qBAAA,EAAAC,MAAA,EAAAC,UAAA,EAAAC,MAAA,EAAA/D,IAAA,EAAAgE,QAAA,EAAAC,GAAA;QAAA,WAAAtC,aAAA,CAAAtE,OAAA,IAAA2E,CAAA,WAAAkC,SAAA;UAAA,kBAAAA,SAAA,CAAApE,CAAA;YAAA;cAAA,MAEAiB,MAAA,CAAAC,IAAA,CAAAsC,MAAA,CAAA3F,gBAAA,EAAAsD,MAAA;gBAAAiD,SAAA,CAAApE,CAAA;gBAAA;cAAA;cAAAoE,SAAA,CAAApE,CAAA;cAAA,OACAwD,MAAA,CAAA9B,cAAA;YAAA;cAEA;cACA8B,MAAA,CAAA3F,gBAAA,GAAA2F,MAAA,CAAA/F,eAAA,CAAA4G,IAAA,WAAAnE,IAAA;gBAAA,OAAAA,IAAA,CAAAoE,UAAA,KAAAf,YAAA;cAAA;cACA;cACAC,MAAA,CAAAjF,kBAAA,GAAAiF,MAAA,CAAAlF,cAAA,CAAAkF,MAAA,CAAA3F,gBAAA,CAAA8E,QAAA;cACA;cACA;cAAAyB,SAAA,CAAApB,CAAA;cAAAoB,SAAA,CAAApE,CAAA;cAAA,OAEAuE,OAAA,CAAAC,UAAA,EACA,IAAAC,uBAAA;gBACAjC,cAAA,EAAAgB,MAAA,CAAA1F,UAAA,CAAA2E,QAAA;gBAAA;gBACAiC,SAAA,EAAAlB,MAAA,CAAAmB,MAAA,CAAAC,KAAA,CAAAF,SAAA;gBAAA;gBACAG,mBAAA,EAAArB,MAAA,CAAAmB,MAAA,CAAAC,KAAA,CAAAC,mBAAA;cACA,IACA,IAAAC,oCAAA;gBACAJ,SAAA,EAAAlB,MAAA,CAAAmB,MAAA,CAAAC,KAAA,CAAAF,SAAA;gBAAA;gBACAhC,KAAA,EAAAc,MAAA,CAAA3F,gBAAA,CAAA8E,QAAA;cACA,GACA;YAAA;cAAAe,qBAAA,GAAAU,SAAA,CAAAlB,CAAA;cAAAS,sBAAA,OAAAoB,eAAA,CAAAxH,OAAA,EAAAmG,qBAAA;cAVAE,YAAA,GAAAD,sBAAA;cAAAzE,WAAA,GAAAyE,sBAAA;cAYA;cACA,IAAAC,YAAA,CAAAoB,MAAA;gBACAnB,SAAA,GAAAD,YAAA,CAAAzD,KAAA;gBACA,IAAA0D,SAAA,CAAAzC,IAAA;kBACAoC,MAAA,CAAApF,gBAAA,GAAAyF,SAAA,CAAArG,IAAA;kBACAuG,MAAA,IAAAD,qBAAA,GAAAN,MAAA,CAAApF,gBAAA,CAAAiG,IAAA,WAAAnE,IAAA;oBAAA,OAAAA,IAAA,CAAAoE,UAAA,KAAAf,YAAA;kBAAA,gBAAAO,qBAAA,uBAAAA,qBAAA,CAAAmB,2BAAA;kBACAzB,MAAA,CAAAlC,mBAAA;kBACA,IAAAyC,MAAA;oBAAAC,UAAA,OAAApE,2BAAA,CAAArC,OAAA,EACAwG,MAAA;oBAAA;sBAAA,KAAAC,UAAA,CAAAjE,CAAA,MAAAkE,MAAA,GAAAD,UAAA,CAAAhE,CAAA,IAAAC,IAAA;wBAAAC,IAAA,GAAA+D,MAAA,CAAA9D,KAAA;wBACAqD,MAAA,CAAAzF,cAAA,CAAAmC,IAAA,CAAAqC,oBAAA,EAAAjC,MAAA,GAAAJ,IAAA,CAAA2C,gBAAA;wBACAW,MAAA,CAAAzF,cAAA,CAAAmC,IAAA,CAAAqC,oBAAA,EAAAnC,KAAA,GAAAF,IAAA,CAAA0C,gBAAA;sBACA;oBAAA,SAAAhC,GAAA;sBAAAoD,UAAA,CAAAnD,CAAA,CAAAD,GAAA;oBAAA;sBAAAoD,UAAA,CAAAlD,CAAA;oBAAA;kBACA;gBACA;kBACA0C,MAAA,CAAAhD,QAAA,CAAAC,OAAA,CAAAoD,SAAA,CAAAT,GAAA;gBACA;cACA;gBACA8B,OAAA,CAAA7B,KAAA,cAAAO,YAAA,CAAAtD,MAAA;gBACAkD,MAAA,CAAAhD,QAAA,CAAA6C,KAAA;cACA;;cAEA;cACA,IAAAnE,WAAA,CAAA8F,MAAA;gBACAd,QAAA,GAAAhF,WAAA,CAAAiB,KAAA;gBACA,IAAA+D,QAAA,CAAA9C,IAAA;kBACAoC,MAAA,CAAAtE,WAAA,GAAAgF,QAAA,CAAA1G,IAAA;gBACA;kBACA0H,OAAA,CAAA7B,KAAA,gBAAAa,QAAA,CAAAd,GAAA;kBACAI,MAAA,CAAAhD,QAAA,CAAAC,OAAA;gBACA;cACA;gBACAyE,OAAA,CAAA7B,KAAA,cAAAnE,WAAA,CAAAoB,MAAA;gBACAkD,MAAA,CAAAhD,QAAA,CAAA6C,KAAA;cACA;cAAAe,SAAA,CAAApE,CAAA;cAAA;YAAA;cAAAoE,SAAA,CAAApB,CAAA;cAAAmB,GAAA,GAAAC,SAAA,CAAAlB,CAAA;cAEAgC,OAAA,CAAA7B,KAAA,YAAAc,GAAA;cACAX,MAAA,CAAAhD,QAAA,CAAA6C,KAAA;YAAA;cAEA;cACAG,MAAA,CAAA2B,gBAAA;YAAA;cAAA,OAAAf,SAAA,CAAAhC,CAAA;UAAA;QAAA,GAAAqB,QAAA;MAAA;IACA;IACA;AACA;AACA;IACA0B,gBAAA,WAAAA,iBAAA;MACA,UAAAtH,gBAAA,IAAAoD,MAAA,CAAAC,IAAA,MAAArD,gBAAA,EAAAsD,MAAA;QACA,KAAAX,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAA/B,YAAA;MACA,KAAAP,YAAA;MACA,KAAAD,iBAAA;MACA,KAAAD,cAAA;MACA,KAAAO,cAAA,QAAAR,YAAA,CAAAoH,IAAA,MAAAvH,gBAAA,CAAA8E,QAAA;IACA;IACA;AACA;AACA;IACA0C,gBAAA,WAAAA,iBAAA;MACA,UAAAxH,gBAAA,IAAAoD,MAAA,CAAAC,IAAA,MAAArD,gBAAA,EAAAsD,MAAA;QACA,KAAAX,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAA/B,YAAA;MACA,KAAAP,YAAA;MACA,KAAAD,iBAAA;MACA,KAAAD,cAAA;MACA,KAAAO,cAAA,QAAAR,YAAA,CAAAoH,IAAA,MAAAvH,gBAAA,CAAA8E,QAAA;IACA;IACA;AACA;AACA;AACA;IACA2C,oBAAA,WAAAA,qBAAAC,UAAA;MACA;MACA,UAAAC,aAAA;QACA,KAAAhF,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAA9C,kBAAA,GAAA4H,UAAA;;MAEA;MACA,SAAArH,iBAAA,UAAAD,cAAA;QACA,UAAAc,sBAAA;UACA,KAAAyB,QAAA,CAAAC,OAAA;UACA;QACA;QAEA,IAAA8E,UAAA,CAAAE,UAAA;UACA,KAAAC,KAAA,CAAAC,WAAA,CAAAC,QAAA,CAAAL,UAAA,CAAAE,UAAA;QACA,gBAAA9G,qBAAA,SAAAA,qBAAA,CAAA4G,UAAA,CAAA5E,QAAA;UACA,KAAA+E,KAAA,CAAAC,WAAA,CAAAC,QAAA,MAAAjH,qBAAA,CAAA4G,UAAA,CAAA5E,QAAA;QACA;QACA;MACA;;MAEA;MACA,UAAApC,kBAAA,IAAA0C,MAAA,CAAAC,IAAA,MAAA3C,kBAAA,EAAA4C,MAAA;QACA,KAAAX,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,SAAAxC,cAAA,SAAAyH,KAAA,CAAA1D,QAAA;QACA,UAAAlD,mBAAA;UACA,KAAA0B,QAAA,CAAAC,OAAA;UACA;QACA;QACA,KAAAiF,KAAA,CAAA1D,QAAA,CAAA4D,QAAA,MAAArH,kBAAA,MAAAZ,kBAAA,CAAAgD,QAAA;MACA;;MAEA;MACA,SAAAzC,iBAAA,SAAAwH,KAAA,CAAAC,WAAA;QACA,UAAA5G,sBAAA;UACA,KAAAyB,QAAA,CAAAC,OAAA;UACA;QACA;;QAEA;QACA,SAAA9B,qBAAA,SAAAA,qBAAA,CAAA4G,UAAA,CAAA5E,QAAA;UACA,KAAA+E,KAAA,CAAAC,WAAA,CAAAC,QAAA,MAAAjH,qBAAA,CAAA4G,UAAA,CAAA5E,QAAA;QACA;UACA;UACA;QAAA;MAEA;IACA;IAEA;AACA;AACA;AACA;IACA6E,aAAA,WAAAA,cAAA;MACA;MACA,SAAAtH,iBAAA,UAAAD,cAAA;QACA,YAAAc,sBAAA;MACA;MACA;MACA,SAAAd,cAAA,UAAAC,iBAAA;QACA,YAAAY,mBAAA;MACA;MACA;MACA,SAAAb,cAAA,SAAAC,iBAAA;QACA,YAAAY,mBAAA,SAAAC,sBAAA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACA8G,2BAAA,WAAAA,4BAAAC,UAAA,EAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAAjH,mBAAA,GAAAgH,UAAA;MACA,WAAAC,OAAA;QACA,KAAAhH,sBAAA,GAAA+G,UAAA;MACA;MAEA,IAAAA,UAAA;QACAZ,OAAA,CAAAc,GAAA,IAAAtF,MAAA,CAAAqF,OAAA;MACA;IACA;IAEA;AACA;AACA;IACAE,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MAAA,WAAAtE,kBAAA,CAAArE,OAAA,mBAAAsE,aAAA,CAAAtE,OAAA,IAAAuE,CAAA,UAAAqE,SAAA;QAAA,IAAAC,UAAA,EAAA5I,IAAA,EAAAwE,QAAA,EAAAgD,MAAA,EAAAqB,GAAA,EAAAC,GAAA;QAAA,WAAAzE,aAAA,CAAAtE,OAAA,IAAA2E,CAAA,WAAAqE,SAAA;UAAA,kBAAAA,SAAA,CAAAvG,CAAA;YAAA;cAAAuG,SAAA,CAAAvG,CAAA;cAAA,OAEAkG,MAAA,CAAAxE,cAAA;YAAA;cAAA0E,UAAA,GAAAG,SAAA,CAAArD,CAAA;cAAA,IACAkD,UAAA;gBAAAG,SAAA,CAAAvG,CAAA;gBAAA;cAAA;cAAA,OAAAuG,SAAA,CAAAnE,CAAA;YAAA;cAKA5E,IAAA;gBACAkH,SAAA,EAAAwB,MAAA,CAAAvB,MAAA,CAAAC,KAAA,CAAAF,SAAA;gBAAA;gBACAlC,cAAA,EAAA0D,MAAA,CAAApI,UAAA,CAAA2E,QAAA;gBAAA;gBACAoC,mBAAA,EAAAqB,MAAA,CAAAvB,MAAA,CAAAC,KAAA,CAAAC,mBAAA;cACA;cAAA0B,SAAA,CAAAvD,CAAA;cAAAuD,SAAA,CAAAvG,CAAA;cAAA,OAEA,IAAAwG,0BAAA,EAAAhJ,IAAA;YAAA;cAAAwE,QAAA,GAAAuE,SAAA,CAAArD,CAAA;cAAA,MAEAlB,QAAA,CAAAZ,IAAA;gBAAAmF,SAAA,CAAAvG,CAAA;gBAAA;cAAA;cACA;cACAgF,MAAA;gBACAyB,qBAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA,yBAAAJ,qBAAA;gBAAA;gBACAK,SAAA;cACA;cAAAP,SAAA,CAAAvG,CAAA;cAAA,OACA,IAAA+G,qCAAA,EAAA/B,MAAA;YAAA;cAAAqB,GAAA,GAAAE,SAAA,CAAArD,CAAA;cAAA;cACA,IAAAmD,GAAA,CAAAjF,IAAA;gBACA8E,MAAA,CAAA1F,QAAA,CAAA2C,OAAA;cACA;cACA+C,MAAA,CAAAc,KAAA;cAAAT,SAAA,CAAAvG,CAAA;cAAA;YAAA;cAEAkG,MAAA,CAAA1F,QAAA,CAAAC,OAAA,CAAAuB,QAAA,CAAAoB,GAAA;YAAA;cAAAmD,SAAA,CAAAvG,CAAA;cAAA;YAAA;cAAAuG,SAAA,CAAAvD,CAAA;cAAAsD,GAAA,GAAAC,SAAA,CAAArD,CAAA;cAGAgD,MAAA,CAAA1F,QAAA,CAAA6C,KAAA;YAAA;cAAA,OAAAkD,SAAA,CAAAnE,CAAA;UAAA;QAAA,GAAA+D,QAAA;MAAA;IAEA;IACA;AACA;AACA;IACAc,cAAA,WAAAA,eAAA;MACA,KAAAvI,YAAA;MACA,KAAAP,YAAA;MACA,KAAAF,cAAA;MACA,KAAAC,iBAAA;MACA;MACA,IAAAgJ,kBAAA;;MAEA,SAAAhH,IAAA,SAAAvB,qBAAA;QACAuI,kBAAA,CAAAC,IAAA;UACAxG,QAAA,EAAAT,IAAA;UACAuF,UAAA,OAAA9G,qBAAA,CAAAuB,IAAA;QACA;MACA;MAEAgF,OAAA,CAAAc,GAAA,MAAAtI,aAAA,CAAAmC,MAAA;MACAqF,OAAA,CAAAc,GAAA,CAAAkB,kBAAA;MACA,KAAAtI,eAAA;MACA,SAAAwI,CAAA,MAAAA,CAAA,QAAA1J,aAAA,CAAAmC,MAAA,CAAAsB,MAAA,EAAAiG,CAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,GAAAH,kBAAA,CAAA/F,MAAA,EAAAkG,CAAA;UACA,SAAA3J,aAAA,CAAAmC,MAAA,CAAAuH,CAAA,EAAAzG,QAAA,IAAAuG,kBAAA,CAAAG,CAAA,EAAA1G,QAAA;YACA,KAAA/B,eAAA,CAAAuI,IAAA,KAAAG,cAAA,CAAA/J,OAAA,MAAA+J,cAAA,CAAA/J,OAAA,WAAAG,aAAA,CAAAmC,MAAA,CAAAuH,CAAA,IAAAF,kBAAA,CAAAG,CAAA;UACA;QACA;MACA;MACAnC,OAAA,CAAAc,GAAA,MAAApH,eAAA;IACA;IACA;AACA;AACA;IACA2I,WAAA,WAAAA,YAAA;MACA,IAAA3C,KAAA;QACAF,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QAAA;QACA8C,IAAA,OAAA7C,MAAA,CAAAC,KAAA,CAAA4C,IAAA;QAAA;QACA3C,mBAAA,EAAA6B,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACA;MACA,KAAAY,OAAA,CAAAN,IAAA;QAAAO,IAAA;QAAA9C,KAAA,EAAAA;MAAA;IACA;IACA;AACA;AACA;IACA+C,UAAA,WAAAA,WAAA;MACA,IAAA/C,KAAA;QACAF,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QAAA;QACA8C,IAAA,OAAA7C,MAAA,CAAAC,KAAA,CAAA4C,IAAA;QAAA;QACA3C,mBAAA,EAAA6B,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACA;MACA,KAAAY,OAAA,CAAAN,IAAA;QAAAO,IAAA;QAAA9C,KAAA,EAAAA;MAAA;IACA;IACA;AACA;AACA;IACAgD,cAAA,WAAAA,eAAA;MACA,KAAAtJ,cAAA,GAAAoI,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;IACA;IACA;AACA;AACA;IACAgB,aAAA,WAAAA,cAAA;MACA;QACA,KAAAzI,eAAA,GAAAsH,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;QACA,KAAAxH,uBAAA,GAAAqH,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;QACA,KAAAvH,mBAAA,GAAAoH,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;QACA,KAAA/I,UAAA,QAAAsB,eAAA;QACA,KAAAf,kBAAA,QAAAgB,uBAAA;QACA,KAAAf,cAAA,QAAAgB,mBAAA;QACA4F,OAAA,CAAAc,GAAA;UAAAlI,UAAA,OAAAA;QAAA;MACA,SAAAuF,KAAA;QACA6B,OAAA,CAAA7B,KAAA,eAAAA,KAAA;MACA;IACA;IAEA;AACA;AACA;IACAyE,cAAA,WAAAA,eAAA;MACA;QACA,IAAAC,aAAA,GAAAnB,YAAA,CAAAC,OAAA;QACA,IAAAkB,aAAA;UACA,KAAA3I,eAAA,GAAAsH,IAAA,CAAAC,KAAA,CAAAoB,aAAA;UACA,KAAAjK,UAAA,QAAAsB,eAAA;UACA8F,OAAA,CAAAc,GAAA,iBAAAlI,UAAA;QACA;MACA,SAAAuF,KAAA;QACA6B,OAAA,CAAA7B,KAAA,cAAAA,KAAA;MACA;IACA;IACA;AACA;AACA;IACA2E,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MAAA,WAAArG,kBAAA,CAAArE,OAAA,mBAAAsE,aAAA,CAAAtE,OAAA,IAAAuE,CAAA,UAAAoG,SAAA;QAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,QAAA,EAAAC,GAAA;QAAA,WAAA3G,aAAA,CAAAtE,OAAA,IAAA2E,CAAA,WAAAuG,SAAA;UAAA,kBAAAA,SAAA,CAAAzI,CAAA;YAAA;cACAiI,MAAA,CAAAJ,aAAA;cAAAY,SAAA,CAAAzF,CAAA;cAAAyF,SAAA,CAAAzI,CAAA;cAAA,OAGAuE,OAAA,CAAAmE,GAAA,EACA,IAAAC,oBAAA;gBAAAjE,SAAA,EAAAuD,MAAA,CAAAtD,MAAA,CAAAC,KAAA,CAAAF;cAAA;cAAA;cACA,IAAAkE,uBAAA,EAAAX,MAAA,CAAAtD,MAAA,CAAAC,KAAA,CAAAF,SAAA,EAAAuD,MAAA,CAAAnK,UAAA,CAAA2E,QAAA;cAAA;cACA,IAAAoG,iBAAA,EAAAZ,MAAA,CAAAtD,MAAA,CAAAC,KAAA,CAAAF,SAAA;cAAA,CACA;YAAA;cAAAyD,kBAAA,GAAAM,SAAA,CAAAvF,CAAA;cAAAkF,mBAAA,OAAArD,eAAA,CAAAxH,OAAA,EAAA4K,kBAAA;cAJAE,WAAA,GAAAD,mBAAA;cAAAE,WAAA,GAAAF,mBAAA;cAAAG,QAAA,GAAAH,mBAAA;cAKA;cACA,IAAAC,WAAA,CAAAjH,IAAA;gBACA6G,MAAA,CAAAxK,eAAA,GAAA4K,WAAA,CAAAS,IAAA,CAAAhG,MAAA,WAAA5C,IAAA;kBAAA,OAAAA,IAAA,CAAA6I,cAAA;gBAAA;cACA;gBACAd,MAAA,CAAAzH,QAAA,CAAAC,OAAA,CAAA4H,WAAA,CAAAjF,GAAA;cACA;cACA;cACA,IAAAkF,WAAA,CAAAlH,IAAA;gBACA6G,MAAA,CAAApJ,eAAA,GAAAyJ,WAAA,CAAA9K,IAAA,CAAAwL,gBAAA,CAAAC,WAAA,CAAAnG,MAAA,WAAA5C,IAAA;kBAAA,OAAAA,IAAA,CAAAgJ,QAAA;gBAAA;gBACAjB,MAAA,CAAAvK,aAAA,GAAA4K,WAAA,CAAA9K,IAAA,CAAA2L,kBAAA,CAAAC,kBAAA,CAAA/E,IAAA,CACA,UAAAnE,IAAA;kBAAA,OAAAA,IAAA,CAAA2E,mBAAA,IAAAoD,MAAA,CAAAtD,MAAA,CAAAC,KAAA,CAAAC,mBAAA;gBAAA,CACA;gBACA+B,YAAA,CAAAyC,OAAA,iCAAA3C,IAAA,CAAA4C,SAAA,CAAArB,MAAA,CAAAvK,aAAA,CAAA6L,4BAAA;gBACAtB,MAAA,CAAAlK,cAAA,GAAAkK,MAAA,CAAAvK,aAAA,CAAAmC,MAAA,CAAA2J,MAAA,WAAAC,GAAA,EAAAvJ,IAAA;kBACAuJ,GAAA,CAAAvJ,IAAA,CAAAG,eAAA;oBAAAD,KAAA;oBAAAE,MAAA;kBAAA;kBACA,OAAAmJ,GAAA;gBACA;cACA;gBACAxB,MAAA,CAAAzH,QAAA,CAAAC,OAAA,CAAA6H,WAAA,CAAAlF,GAAA;cACA;cACA;cACA,IAAAmF,QAAA,CAAAnH,IAAA;gBACA6G,MAAA,CAAAjK,YAAA,GAAAuK,QAAA,CAAA/K,IAAA;gBACA,IAAAyK,MAAA,CAAAjK,YAAA,CAAA0L,oBAAA;kBACAzB,MAAA,CAAAxJ,iBAAA,GAAAwJ,MAAA,CAAAjK,YAAA,CAAA0L,oBAAA;gBACA;gBACA;gBACA;gBACA;cACA;gBACAzB,MAAA,CAAAzH,QAAA,CAAAC,OAAA,CAAA8H,QAAA,CAAAnF,GAAA;cACA;cAAAqF,SAAA,CAAAzI,CAAA;cAAA;YAAA;cAAAyI,SAAA,CAAAzF,CAAA;cAAAwF,GAAA,GAAAC,SAAA,CAAAvF,CAAA;cAEA+E,MAAA,CAAAzH,QAAA,CAAA6C,KAAA;YAAA;cAAA,OAAAoF,SAAA,CAAArG,CAAA;UAAA;QAAA,GAAA8F,QAAA;MAAA;IAEA;IAEAyB,YAAA,WAAAA,aAAAzJ,IAAA;MACA,KAAA0J,SAAA,CAAAC,GAAA,CAAA3J,IAAA,CAAA4J,QAAA,EAAA5J,IAAA,CAAA6J,QAAA;IACA;IAGA;IACA;AACA;AACA;AACA;IACAC,iBAAA,WAAAA,kBAAAzE,UAAA;MAAA,IAAA0E,MAAA;MACA,KAAA1E,UAAA,CAAA2E,UAAA;;MAEA;MACA,SAAA1K,YAAA;QACA2K,YAAA,MAAA3K,YAAA;MACA;;MAEA;MACA,KAAAA,YAAA,GAAA4K,UAAA;QACAH,MAAA,CAAA1K,iBAAA,GAAAgG,UAAA;MACA;IACA;IAEA;AACA;AACA;IACA8E,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAA9K,YAAA;QACA2K,YAAA,MAAA3K,YAAA;QACA,KAAAA,YAAA;MACA;;MAEA;MACA4K,UAAA;QACAE,MAAA,CAAA/K,iBAAA;MACA;IACA;IAEA;AACA;AACA;IACAgL,iBAAA,WAAAA,kBAAA;MACA,SAAA/K,YAAA;QACA2K,YAAA,MAAA3K,YAAA;QACA,KAAAA,YAAA;MACA;IACA;EACA;EACAgL,OAAA,WAAAA,QAAA;IACA,KAAA7L,qBAAA,GAAA+H,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;IACA,KAAAmB,QAAA;IACA,KAAAJ,cAAA;EACA;EACA6C,aAAA,WAAAA,cAAA;IACA;IACA,SAAAjL,YAAA;MACA2K,YAAA,MAAA3K,YAAA;MACA,KAAAA,YAAA;IACA;EACA;AACA", "ignoreList": []}]}