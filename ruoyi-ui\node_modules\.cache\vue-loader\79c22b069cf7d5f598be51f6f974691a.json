{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\compliance\\one.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\compliance\\one.vue", "mtime": 1753922915380}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQovLyDluLjph4/lrprkuYkNCmNvbnN0IFBBU1MgPSAnMSc7IC8vIOmAmui/hw0KY29uc3QgRkFJTCA9ICcwJzsgLy8g5LiN6YCa6L+HDQpjb25zdCBDSEVDS19QQVNTID0gJ+ezu+e7n+WInemqjOmAmui/hyc7IC8vIOezu+e7n+WInemqjOmAmui/h+aWh+acrA0KY29uc3QgQ0hFQ0tfRkFJTCA9ICfns7vnu5/liJ3pqozmnKrpgJrov4cnOyAvLyDns7vnu5/liJ3pqozmnKrpgJrov4fmlofmnKwNCg0KaW1wb3J0IHsNCiAgc3VwcGxpZXJJbmZvLCAvLyDojrflj5bkvpvlupTllYbkv6Hmga9BUEkNCiAgYXBwcm92YWxQcm9jZXNzLCAvLyDojrflj5bor4TliIbmlrnms5VBUEkNCiAgc2NvcmluZ0ZhY3RvcnMsIC8vIOaPkOS6pOivhOWIhuWboOWtkEFQSQ0KICBjaGVja1Jldmlld1N1bW1hcnksIC8vIOajgOafpeivhOWuoeaxh+aAu0FQSQ0KICBmaWxlc0J5SWQsIC8vIOiOt+WPlumhueebruebuOWFs+aWh+S7tkFQSQ0KfSBmcm9tICJAL2FwaS9leHBlcnQvcmV2aWV3IjsgLy8g5a+85YWl5LiT5a626K+E5a6h55u45YWzQVBJDQppbXBvcnQgeyBnZXREZXRhaWxCeVBzeHggfSBmcm9tICJAL2FwaS9ldmFsdWF0aW9uL2RldGFpbC8iOyAvLyDojrflj5bor4TliIbor6bmg4VBUEkNCmltcG9ydCB7IGVkaXRFdmFsRXhwZXJ0U2NvcmVJbmZvIH0gZnJvbSAiQC9hcGkvZXZhbHVhdGlvbi9leHBlcnRTdGF0dXMiOyAvLyDnvJbovpHkuJPlrrbor4TliIbnirbmgIFBUEkNCmltcG9ydCB7IHJlc0RvY1Jldmlld0ZhY3RvcnNEZWNpc2lvbiB9IGZyb20gIkAvYXBpL2RvY1Jlc3BvbnNlL2VudEluZm8iOyAvLyDojrflj5blk43lupTmlofku7bor4TlrqHlm6DlrZDlhrPnrZZBUEkNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBzdXBwbGllck9wdGlvbnM6IFtdLCAvLyDkvpvlupTllYbkuIvmi4npgInpobnliJfooagNCiAgICAgIHNjb3JpbmdNZXRob2Q6IG51bGwsIC8vIOW9k+WJjeivhOWIhuaWueazleWvueixoQ0KICAgICAgc2VsZWN0ZWRGYWN0b3JOb2RlOiB7fSwgLy8g5b2T5YmN6YCJ5Lit55qE6K+E5YiG5Zug5a2Q6IqC54K5DQogICAgICBzZWxlY3RlZFN1cHBsaWVyTmFtZTogJycsIC8vIOW9k+WJjemAieS4reeahOS+m+W6lOWVhuWQjeensA0KICAgICAgc2VsZWN0ZWRTdXBwbGllcjoge30sIC8vIOW9k+WJjemAieS4reeahOS+m+W6lOWVhuWvueixoQ0KICAgICAgZXhwZXJ0SW5mbzoge30sIC8vIOW9k+WJjeS4k+WutuS/oeaBrw0KICAgICAgcmF0aW5nU3RhdGVNYXA6IHt9LCAvLyDor4TliIbpobnnirbmgIHmmKDlsITvvIhrZXnkuLror4TliIbpoblJRO+8jHZhbHVl5Li6e3N0YXRlLCByZWFzb25977yJDQogICAgICBwcm9qZWN0RmlsZXM6IHt9LCAvLyDpobnnm67nm7jlhbPmlofku7blr7nosaENCiAgICAgIGlzU2hvd1Jlc3BvbnNlOiBmYWxzZSwgLy8g5piv5ZCm5pi+56S65ZON5bqU5paH5Lu2DQogICAgICBpc1Nob3dQcm9jdXJlbWVudDogZmFsc2UsIC8vIOaYr+WQpuaYvuekuumHh+i0reaWh+S7tg0KICAgICAgaXNEb3VibGVWaWV3OiBmYWxzZSwgLy8g5piv5ZCm5Y+M5paH5Lu25a+55q+U5qih5byPDQogICAgICBmYWN0b3JEZXRhaWxMaXN0OiBbXSwgLy8g6K+E5YiG5Zug5a2Q6K+m57uG5YiX6KGoDQogICAgICBlbnREb2NSZXNwb25zZVBhZ2U6IG51bGwsIC8vIOS8geS4muWTjeW6lOaWh+S7tumhteeggeS/oeaBrw0KICAgICAgZmFjdG9yc1BhZ2VNYXA6IG51bGwsIC8vIOS+m+W6lOWVhuWboOWtkOmhteeggeaYoOWwhA0KICAgICAgc3VwcGxpZXJGYWN0b3JQYWdlOiBudWxsLCAvLyDlvZPliY3kvpvlupTllYblm6DlrZDpobXnoIENCiAgICAgIHJlc3BvbnNlUGRmVXJsOiBudWxsLCAvLyDlk43lupTmlofku7ZQREblnLDlnYANCiAgICAgIHByb2N1cmVtZW50UGRmVXJsOiBudWxsLCAvLyDph4fotK3mlofku7ZQREblnLDlnYANCg0KICAgICAgLy8g5oyJ6ZKu54q25oCB566h55CGDQogICAgICBhY3RpdmVCdXR0b246ICdyZXNwb25zZScsIC8vIOW9k+WJjea/gOa0u+eahOaMiemSru+8midyZXNwb25zZSfjgIEncHJvY3VyZW1lbnQn44CBJ2NvbnRyYXN0Jw0KDQoJICAgIGVudERvY1Byb2N1cmVtZW50UGFnZToge30sIC8vIOmHh+i0reaWh+S7tumhteeggeS/oeaBrw0KICAgICAgcGFnZVByb2N1cmVtZW50OltdLCAvLyDph4fotK3mlofku7bnmoTor4TliIbpobkNCiAgICAgIGF0dGFjaG1lbnRzTGlzdDpbXSwgLy8g5paH5Lu25YiX6KGoDQoJICAgIA0KCSAgICAvLyBQREbmuLLmn5PnirbmgIHnrqHnkIYNCgkgICAgcmVzcG9uc2VQZGZSZW5kZXJlZDogZmFsc2UsIC8vIOWTjeW6lOaWh+S7tlBERuaYr+WQpua4suafk+WujOaIkA0KCSAgICBwcm9jdXJlbWVudFBkZlJlbmRlcmVkOiBmYWxzZSwgLy8g6YeH6LSt5paH5Lu2UERG5piv5ZCm5riy5p+T5a6M5oiQDQoNCgkgICAgaGVscEltZ0xpc3Q6IFsiL2V2YWx1dGlvbi9oZWxwLmpwZyJdLCAvLyDmk43kvZzluK7liqnlm77niYfliJfooagNCiAgICAgIC8vIOivhOWIhumhueWQjeensOS4juWQjuerr+Wtl+auteaYoOWwhA0KICAgICAgZmFjdG9yQ29kZU1hcDogew0KICAgICAgICAi54m55a6a6LWE5qC86KaB5rGCIjogInpnenMiLA0KICAgICAgICAi5ZON5bqU5YaF5a65IjogImpzcGxiIiwNCiAgICAgICAgIumHh+i0remcgOaxgiI6ICJqc3BsYiIsDQogICAgICAgICLkvpvotKfmnJ/pmZAiOiAiZ2hxeCIsDQogICAgICAgICLmipXmoIfmiqXku7ciOiAidGJiaiINCiAgICAgIH0sDQogICAgICBjaGVja1Jlc3VsdDoge30sIC8vIOezu+e7n+WInemqjOe7k+aenOWvueixoQ0KICAgICAgLy8g57O757uf5Yid6aqM57uT5p6c5ZCN56ew5pig5bCEDQogICAgICBjaGVja1Jlc3VsdE5hbWVNYXA6IHsNCiAgICAgICAgIuespuWQiOOAiuS4reWNjuS6uuawkeWFseWSjOWbveaUv+W6nOmHh+i0reazleOAi+esrOS6jOWNgeS6jOadoeinhOWumiI6IENIRUNLX1BBU1MsDQogICAgICAgICLnibnlrprotYTmoLzopoHmsYIiOiBDSEVDS19QQVNTLA0KICAgICAgICAi5L+h55So5p+l6K+iIjogQ0hFQ0tfUEFTUywNCiAgICAgICAgIuWTjeW6lOS6uuWQjeensCI6IENIRUNLX1BBU1MsDQogICAgICAgICLlk43lupTlhoXlrrkiOiBDSEVDS19QQVNTLA0KICAgICAgICAi6YeH6LSt6ZyA5rGCIjogQ0hFQ0tfUEFTUywNCiAgICAgICAgIuS+m+i0p+acn+mZkCI6IENIRUNLX1BBU1MsDQogICAgICAgICLmipXmoIfmiqXku7ciOiBDSEVDS19QQVNTDQogICAgICB9LA0KICAgICAgLy8g5pys5Zyw57yT5a2Y5pWw5o2uDQogICAgICBsb2NhbEV4cGVydEluZm86IG51bGwsIC8vIOacrOWcsOS4k+WutuS/oeaBrw0KICAgICAgbG9jYWxFbnREb2NSZXNwb25zZVBhZ2U6IG51bGwsIC8vIOacrOWcsOWTjeW6lOaWh+S7tumhteeggQ0KICAgICAgbG9jYWxGYWN0b3JzUGFnZU1hcDogbnVsbCwgLy8g5pys5Zyw5Zug5a2Q6aG156CB5pig5bCEDQoJICAgIA0KCSAgICAvLyDmgqzlgZznirbmgIHnrqHnkIYNCgkgICAgaG92ZXJlZEZhY3Rvck5vZGU6IG51bGwsIC8vIOaCrOWBnOaXtueahOivhOWIhumhuQ0KCSAgICB0b29sdGlwVGltZXI6IG51bGwsIC8vIOaCrOa1ruahhuaYvuekuuWumuaXtuWZqA0KICAgIH07DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKioNCiAgICAgKiDmoKHpqozmiYDmnInor4TliIbpobnmmK/lkKbloavlhpnlrozmlbQNCiAgICAgKiBAcmV0dXJucyB7Ym9vbGVhbn0g5piv5ZCm5YWo6YOo5aGr5YaZDQogICAgICovDQogICAgdmFsaWRhdGVSYXRpbmdzKCkgew0KICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHRoaXMuc2NvcmluZ01ldGhvZC51aXRlbXMpIHsgLy8g6YGN5Y6G5omA5pyJ6K+E5YiG6aG5DQogICAgICAgIGNvbnN0IHN0YXRlID0gdGhpcy5yYXRpbmdTdGF0ZU1hcFtpdGVtLmVudE1ldGhvZEl0ZW1JZF0uc3RhdGU7IC8vIOiOt+WPluivhOWIhueKtuaAgQ0KICAgICAgICBjb25zdCByZWFzb24gPSB0aGlzLnJhdGluZ1N0YXRlTWFwW2l0ZW0uZW50TWV0aG9kSXRlbUlkXS5yZWFzb247IC8vIOiOt+WPluivhOWIhuWOn+WboA0KICAgICAgICAvLyDor4TliIbnu5PmnpzmnKrloavlhpkNCiAgICAgICAgaWYgKHN0YXRlID09PSBudWxsIHx8IHN0YXRlID09PSAnJykgew0KICAgICAgICAgIC8vIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhg6K+35aGr5YaZ6K+E5YiG6aG577yaJHtpdGVtLml0ZW1OYW1lfSDnmoTor4TliIbnu5PmnpxgKTsgLy8g5o+Q56S65pyq5aGr5YaZDQogICAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICAgIH0NCiAgICAgICAgLy8g5LiN6YCa6L+H5L2G5pyq5aGr5YaZ5Y6f5ZugDQogICAgICAgIGlmIChzdGF0ZSA9PT0gRkFJTCAmJiAoIXJlYXNvbiB8fCByZWFzb24udHJpbSgpID09PSAnJykpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoYCR7aXRlbS5pdGVtTmFtZX3or4TlrqHkuI3pgJrov4fkvYbmnKrloavlhpnlpIfms6jvvIzkuI3ov5vooYzkv53lrZhgKTsgLy8g5o+Q56S65pyq5aGr5YaZ5Y6f5ZugDQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gdHJ1ZTsgLy8g5YWo6YOo5aGr5YaZ6L+U5ZuedHJ1ZQ0KICAgIH0sDQogICAgLyoqDQogICAgICog6I635Y+W57O757uf5Yid6aqM57uT5p6c77yI6YCa6L+HL+acqumAmui/h++8iQ0KICAgICAqIEBwYXJhbSB7c3RyaW5nfSBmYWN0b3JOYW1lIOivhOWIhumhueWQjeensA0KICAgICAqIEByZXR1cm5zIHtzdHJpbmd9IDEt6YCa6L+HIDAt5pyq6YCa6L+HDQogICAgICovDQogICAgZ2V0Q2hlY2tSZXN1bHRTdGF0ZShmYWN0b3JOYW1lKSB7DQogICAgICBpZiAoIXRoaXMuY2hlY2tSZXN1bHQgfHwgT2JqZWN0LmtleXModGhpcy5jaGVja1Jlc3VsdCkubGVuZ3RoID09PSAwKSByZXR1cm4gJyc7IC8vIOayoeacieWInemqjOe7k+aenOebtOaOpei/lOWbnuepug0KICAgICAgbGV0IGNvZGUgPSB0aGlzLmZhY3RvckNvZGVNYXBbZmFjdG9yTmFtZV07IC8vIOiOt+WPluivhOWIhumhueWvueW6lOeahOWQjuerr+Wtl+autQ0KICAgICAgbGV0IGNoZWNrID0gUEFTUzsgLy8g6buY6K6k6YCa6L+HDQogICAgICBpZiAoY29kZSkgew0KICAgICAgICBjaGVjayA9IHRoaXMuY2hlY2tSZXN1bHRbY29kZV07IC8vIOiOt+WPluWInemqjOe7k+aenA0KICAgICAgICAvLyDmipXmoIfmiqXku7fnibnmrorlpITnkIYNCiAgICAgICAgaWYgKGZhY3Rvck5hbWUgPT09ICLmipXmoIfmiqXku7ciICYmIGNoZWNrID09PSBQQVNTKSB7DQogICAgICAgICAgY2hlY2sgPSB0aGlzLmNoZWNrUmVzdWx0WydteGJqYiddOyAvLyDmmI7nu4bmiqXku7fooagNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgLy8g6K6+572u5Yid6aqM57uT5p6c5ZCN56ewDQogICAgICBpZiAoY2hlY2sgPT09IEZBSUwpIHsNCiAgICAgICAgdGhpcy5jaGVja1Jlc3VsdE5hbWVNYXBbZmFjdG9yTmFtZV0gPSBDSEVDS19GQUlMOyAvLyDmnKrpgJrov4cNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNoZWNrID0gUEFTUzsNCiAgICAgICAgdGhpcy5jaGVja1Jlc3VsdE5hbWVNYXBbZmFjdG9yTmFtZV0gPSBDSEVDS19QQVNTOyAvLyDpgJrov4cNCiAgICAgIH0NCiAgICAgIHJldHVybiBjaGVjazsgLy8g6L+U5Zue5Yid6aqM57uT5p6cDQogICAgfSwNCiAgICAvKioNCiAgICAgKiDph43nva7miYDmnInor4TliIbpobnnmoTnirbmgIENCiAgICAgKi8NCiAgICByZXNldFJhdGluZ1N0YXRlTWFwKCkgew0KICAgICAgaWYgKCF0aGlzLnNjb3JpbmdNZXRob2QpIHJldHVybjsgLy8g5rKh5pyJ6K+E5YiG5pa55rOV55u05o6l6L+U5ZueDQogICAgICBmb3IgKGNvbnN0IGtleSBvZiBPYmplY3Qua2V5cyh0aGlzLnJhdGluZ1N0YXRlTWFwKSkgeyAvLyDpgY3ljobmiYDmnInor4TliIbpobkNCiAgICAgICAgdGhpcy5yYXRpbmdTdGF0ZU1hcFtrZXldLnN0YXRlID0gbnVsbDsgLy8g6YeN572u54q25oCBDQogICAgICAgIHRoaXMucmF0aW5nU3RhdGVNYXBba2V5XS5yZWFzb24gPSAnJzsgLy8g6YeN572u5Y6f5ZugDQogICAgICB9DQogICAgfSwNCiAgICAvKioNCiAgICAgKiDkuLTml7bkv53lrZjor4TliIbnu5PmnpzliLDlkI7nq68NCiAgICAgKiDmoKHpqozpgJrov4flkI7miY3kvJrkv53lrZgNCiAgICAgKiBAcmV0dXJucyB7Ym9vbGVhbn0g5L+d5a2Y5piv5ZCm5oiQ5YqfDQogICAgICovDQogICAgYXN5bmMgc2F2ZVRlbXBSYXRpbmcoKSB7DQogICAgICBpZiAoIXRoaXMudmFsaWRhdGVSYXRpbmdzKCkpIHJldHVybiBmYWxzZTsgLy8g5qCh6aqM5LiN6YCa6L+H5LiN5L+d5a2Y77yM6L+U5ZueZmFsc2UNCiAgICAgIC8vIOaehOmAoOaPkOS6pOaVsOaNrg0KICAgICAgY29uc3QgZGF0YSA9IHRoaXMuc2NvcmluZ01ldGhvZC51aXRlbXMubWFwKGl0ZW0gPT4gew0KICAgICAgICBjb25zdCBpdGVtSWQgPSBpdGVtLmVudE1ldGhvZEl0ZW1JZDsgLy8g6I635Y+W6K+E5YiG6aG5SUQNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICBzY29yaW5nTWV0aG9kVWl0ZW1JZDogaXRlbUlkLCAvLyDor4TliIbpoblJRA0KICAgICAgICAgIGV4cGVydFJlc3VsdElkOiB0aGlzLmV4cGVydEluZm8ucmVzdWx0SWQsIC8vIOS4k+Wutue7k+aenElEDQogICAgICAgICAgZW50SWQ6IHRoaXMuc2VsZWN0ZWRTdXBwbGllci5iaWRkZXJJZCwgLy8g5L6b5bqU5ZWGSUQNCiAgICAgICAgICBldmFsdWF0aW9uUmVzdWx0OiB0aGlzLnJhdGluZ1N0YXRlTWFwW2l0ZW1JZF0uc3RhdGUsIC8vIOivhOWIhue7k+aenA0KICAgICAgICAgIGV2YWx1YXRpb25SZW1hcms6IHRoaXMucmF0aW5nU3RhdGVNYXBbaXRlbUlkXS5yZWFzb24gfHwgJycgLy8g6K+E5YiG5Y6f5ZugDQogICAgICAgIH07DQogICAgICB9KS5maWx0ZXIoZCA9PiBkLmV2YWx1YXRpb25SZXN1bHQgIT09IG51bGwgJiYgZC5ldmFsdWF0aW9uUmVzdWx0ICE9PSAnJyk7IC8vIOi/h+a7pOacquWhq+WGmeeahOmhuQ0KICAgICAgaWYgKGRhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgc2NvcmluZ0ZhY3RvcnMoZGF0YSk7IC8vIOaPkOS6pOivhOWIhuWboOWtkA0KICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5L+d5a2Y5oiQ5YqfIik7IC8vIOS/neWtmOaIkOWKn+aPkOekug0KICAgICAgICAgICAgcmV0dXJuIHRydWU7IC8vIOS/neWtmOaIkOWKn+i/lOWbnnRydWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKHJlc3BvbnNlLm1zZyk7IC8vIOS/neWtmOWksei0peaPkOekug0KICAgICAgICAgICAgcmV0dXJuIGZhbHNlOyAvLyDkv53lrZjlpLHotKXov5Tlm55mYWxzZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuS/neWtmOWksei0pSIpOyAvLyDlvILluLjmj5DnpLoNCiAgICAgICAgICByZXR1cm4gZmFsc2U7IC8vIOW8guW4uOi/lOWbnmZhbHNlDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiB0cnVlOyAvLyDmsqHmnInmlbDmja7pnIDopoHkv53lrZjml7bkuZ/ov5Tlm550cnVlDQogICAgfSwNCiAgICAvKioNCiAgICAgKiDkvpvlupTllYbliIfmjaLkuovku7bvvIzliIfmjaLml7boh6rliqjkv53lrZjkuIrkuIDkuKrkvpvlupTllYbor4TliIbvvIzlubblubblj5Hojrflj5bmlrDkvpvlupTllYbnmoTor4TliIbor6bmg4Xlkozns7vnu5/liJ3pqowNCiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc3VwcGxpZXJOYW1lIOS+m+W6lOWVhuWQjeensA0KICAgICAqLw0KICAgIGFzeW5jIGhhbmRsZVN1cHBsaWVyQ2hhbmdlKHN1cHBsaWVyTmFtZSkgew0KICAgICAgLy8g5YiH5o2i5YmN5L+d5a2Y5LiK5LiA5Liq5L6b5bqU5ZWG6K+E5YiGDQogICAgICBpZiAoT2JqZWN0LmtleXModGhpcy5zZWxlY3RlZFN1cHBsaWVyKS5sZW5ndGggIT09IDApIHsNCiAgICAgICAgYXdhaXQgdGhpcy5zYXZlVGVtcFJhdGluZygpOyAvLyDkv53lrZjor4TliIYNCiAgICAgIH0NCiAgICAgIC8vIOafpeaJvuW9k+WJjemAieS4reeahOS+m+W6lOWVhuWvueixoQ0KICAgICAgdGhpcy5zZWxlY3RlZFN1cHBsaWVyID0gdGhpcy5zdXBwbGllck9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0uYmlkZGVyTmFtZSA9PT0gc3VwcGxpZXJOYW1lKTsgLy8g5p+l5om+5L6b5bqU5ZWGDQogICAgICAvLyDojrflj5blvZPliY3kvpvlupTllYblm6DlrZDpobXnoIENCiAgICAgIHRoaXMuc3VwcGxpZXJGYWN0b3JQYWdlID0gdGhpcy5mYWN0b3JzUGFnZU1hcFt0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVySWRdOyAvLyDojrflj5bpobXnoIENCiAgICAgIC8vIOW5tuWPkeiOt+WPluivhOWIhuivpuaDheWSjOezu+e7n+WInemqjA0KICAgICAgLy8g5L2/55SoIFByb21pc2UuYWxsU2V0dGxlZCDorqnkuKTkuKror7fmsYLni6znq4vmiafooYzvvIzkupLkuI3lvbHlk40NCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IFtkZXRhaWxSZXN1bHQsIGNoZWNrUmVzdWx0XSA9IGF3YWl0IFByb21pc2UuYWxsU2V0dGxlZChbDQogICAgICAgICAgZ2V0RGV0YWlsQnlQc3h4KHsNCiAgICAgICAgICAgIGV4cGVydFJlc3VsdElkOiB0aGlzLmV4cGVydEluZm8ucmVzdWx0SWQsIC8vIOS4k+Wutue7k+aenElEDQogICAgICAgICAgICBwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwgLy8g6aG555uuSUQNCiAgICAgICAgICAgIHNjb3JpbmdNZXRob2RJdGVtSWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnNjb3JpbmdNZXRob2RJdGVtSWQsIC8vIOivhOWIhuaWueazlemhuUlEDQogICAgICAgICAgfSksDQogICAgICAgICAgcmVzRG9jUmV2aWV3RmFjdG9yc0RlY2lzaW9uKHsNCiAgICAgICAgICAgIHByb2plY3RJZDogdGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkLCAvLyDpobnnm65JRA0KICAgICAgICAgICAgZW50SWQ6IHRoaXMuc2VsZWN0ZWRTdXBwbGllci5iaWRkZXJJZCwgLy8g5L6b5bqU5ZWGSUQNCiAgICAgICAgICB9KQ0KICAgICAgICBdKTsNCg0KICAgICAgICAvLyDlpITnkIbor4TliIbor6bmg4Xor7fmsYLnu5PmnpwNCiAgICAgICAgaWYgKGRldGFpbFJlc3VsdC5zdGF0dXMgPT09ICdmdWxmaWxsZWQnKSB7DQogICAgICAgICAgY29uc3QgZGV0YWlsUmVzID0gZGV0YWlsUmVzdWx0LnZhbHVlOw0KICAgICAgICAgIGlmIChkZXRhaWxSZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICB0aGlzLmZhY3RvckRldGFpbExpc3QgPSBkZXRhaWxSZXMuZGF0YTsgLy8g6K+E5YiG6K+m5oOF5YiX6KGoDQogICAgICAgICAgICBjb25zdCBmYWN0b3IgPSB0aGlzLmZhY3RvckRldGFpbExpc3QuZmluZChpdGVtID0+IGl0ZW0uYmlkZGVyTmFtZSA9PT0gc3VwcGxpZXJOYW1lKT8uZXZhbEV4cGVydEV2YWx1YXRpb25EZXRhaWxzOyAvLyDlvZPliY3kvpvlupTllYbor4TliIbor6bmg4UNCiAgICAgICAgICAgIHRoaXMucmVzZXRSYXRpbmdTdGF0ZU1hcCgpOyAvLyDph43nva7or4TliIbnirbmgIENCiAgICAgICAgICAgIGlmIChmYWN0b3IpIHsNCiAgICAgICAgICAgICAgZm9yIChjb25zdCBpdGVtIG9mIGZhY3Rvcikgew0KICAgICAgICAgICAgICAgIHRoaXMucmF0aW5nU3RhdGVNYXBbaXRlbS5zY29yaW5nTWV0aG9kVWl0ZW1JZF0ucmVhc29uID0gaXRlbS5ldmFsdWF0aW9uUmVtYXJrOyAvLyDorr7nva7or4TliIbljp/lm6ANCiAgICAgICAgICAgICAgICB0aGlzLnJhdGluZ1N0YXRlTWFwW2l0ZW0uc2NvcmluZ01ldGhvZFVpdGVtSWRdLnN0YXRlID0gaXRlbS5ldmFsdWF0aW9uUmVzdWx0OyAvLyDorr7nva7or4TliIbnu5PmnpwNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoZGV0YWlsUmVzLm1zZyk7IC8vIOivhOWIhuivpuaDheiOt+WPluWksei0pQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5bor4TliIbor6bmg4XlpLHotKU6IiwgZGV0YWlsUmVzdWx0LnJlYXNvbik7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6I635Y+W6K+E5YiG6K+m5oOF5aSx6LSlIik7IC8vIOivhOWIhuivpuaDheivt+axguW8guW4uA0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aSE55CG57O757uf5Yid6aqM6K+35rGC57uT5p6cDQogICAgICAgIGlmIChjaGVja1Jlc3VsdC5zdGF0dXMgPT09ICdmdWxmaWxsZWQnKSB7DQogICAgICAgICAgY29uc3QgY2hlY2tSZXMgPSBjaGVja1Jlc3VsdC52YWx1ZTsNCiAgICAgICAgICBpZiAoY2hlY2tSZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICB0aGlzLmNoZWNrUmVzdWx0ID0gY2hlY2tSZXMuZGF0YTsgLy8g6K6+572u5Yid6aqM57uT5p6cDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluezu+e7n+WInemqjOe7k+aenOWksei0pToiLCBjaGVja1Jlcy5tc2cpOw0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLojrflj5bns7vnu5/liJ3pqoznu5PmnpzlpLHotKUiKTsgLy8g57O757uf5Yid6aqM6I635Y+W5aSx6LSlDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuezu+e7n+WInemqjOivt+axguWksei0pToiLCBjaGVja1Jlc3VsdC5yZWFzb24pOw0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuezu+e7n+WInemqjOivt+axguWksei0pSIpOyAvLyDns7vnu5/liJ3pqozor7fmsYLlvILluLgNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLor7fmsYLlpITnkIblvILluLg6IiwgZSk7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuiOt+WPluS+m+W6lOWVhuivpuaDheWksei0pSIpOyAvLyDlvILluLjmj5DnpLoNCiAgICAgIH0NCiAgICAgIC8vIOm7mOiupOaYvuekuuWTjeW6lOaWh+S7tg0KICAgICAgdGhpcy5zaG93UmVzcG9uc2VGaWxlKCk7DQogICAgfSwNCiAgICAvKioNCiAgICAgKiDmmL7npLrlk43lupTmlofku7ZQREYNCiAgICAgKi8NCiAgICBzaG93UmVzcG9uc2VGaWxlKCkgew0KICAgICAgaWYgKCF0aGlzLnNlbGVjdGVkU3VwcGxpZXIgfHwgT2JqZWN0LmtleXModGhpcy5zZWxlY3RlZFN1cHBsaWVyKS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nkvpvlupTllYYiKTsgLy8g5pyq6YCJ5L6b5bqU5ZWG5o+Q56S6DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRoaXMuYWN0aXZlQnV0dG9uID0gJ3Jlc3BvbnNlJzsgLy8g6K6+572u5b2T5YmN5r+A5rS75oyJ6ZKuDQogICAgICB0aGlzLmlzRG91YmxlVmlldyA9IGZhbHNlOyAvLyDljZXmlofku7bmqKHlvI8NCiAgICAgIHRoaXMuaXNTaG93UHJvY3VyZW1lbnQgPSBmYWxzZTsgLy8g5LiN5pi+56S66YeH6LSt5paH5Lu2DQogICAgICB0aGlzLmlzU2hvd1Jlc3BvbnNlID0gdHJ1ZTsgLy8g5pi+56S65ZON5bqU5paH5Lu2DQogICAgICB0aGlzLnJlc3BvbnNlUGRmVXJsID0gdGhpcy5wcm9qZWN0RmlsZXMuZmlsZVt0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVySWRdOyAvLyDorr7nva7lk43lupTmlofku7ZQREblnLDlnYANCiAgICB9LA0KICAgIC8qKg0KICAgICAqIOaWh+S7tuWvueavlO+8iOWPjOaWh+S7tuaooeW8j++8iQ0KICAgICAqLw0KICAgIHNob3dGaWxlQ29udHJhc3QoKSB7DQogICAgICBpZiAoIXRoaXMuc2VsZWN0ZWRTdXBwbGllciB8fCBPYmplY3Qua2V5cyh0aGlzLnNlbGVjdGVkU3VwcGxpZXIpLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeS+m+W6lOWVhiIpOyAvLyDmnKrpgInkvpvlupTllYbmj5DnpLoNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy5hY3RpdmVCdXR0b24gPSAnY29udHJhc3QnOyAvLyDorr7nva7lvZPliY3mv4DmtLvmjInpkq4NCiAgICAgIHRoaXMuaXNEb3VibGVWaWV3ID0gdHJ1ZTsgLy8g5Y+M5paH5Lu25qih5byPDQogICAgICB0aGlzLmlzU2hvd1Byb2N1cmVtZW50ID0gdHJ1ZTsgLy8g5pi+56S66YeH6LSt5paH5Lu2DQogICAgICB0aGlzLmlzU2hvd1Jlc3BvbnNlID0gdHJ1ZTsgLy8g5pi+56S65ZON5bqU5paH5Lu2DQogICAgICB0aGlzLnJlc3BvbnNlUGRmVXJsID0gdGhpcy5wcm9qZWN0RmlsZXMuZmlsZVt0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVySWRdOyAvLyDorr7nva7lk43lupTmlofku7ZQREblnLDlnYANCiAgICB9LA0KICAgIC8qKg0KICAgICAqIOeCueWHu+ivhOWIhumhueWQjeensO+8jOi3s+i9rOWIsOWvueW6lFBERumhteeggQ0KICAgICAqIEBwYXJhbSB7T2JqZWN0fSBmYWN0b3JJdGVtIOW9k+WJjeivhOWIhuWboOWtkOmhuQ0KICAgICAqLw0KICAgIGhhbmRsZVNob3dGYWN0b3JJbmZvKGZhY3Rvckl0ZW0pIHsNCgkgICAgLy8g5qOA5p+lUERG5piv5ZCm5riy5p+T5a6M5oiQDQoJICAgIGlmICghdGhpcy5jYW5KdW1wVG9QYWdlKCkpIHsNCgkJICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygiUERG6aG16Z2i5q2j5Zyo5riy5p+T5Lit77yM6K+356iN5YCZ5YaN6K+VIik7DQoJCSAgICByZXR1cm47DQoJICAgIH0NCgkJCQ0KICAgICAgdGhpcy5zZWxlY3RlZEZhY3Rvck5vZGUgPSBmYWN0b3JJdGVtOyAvLyDorr7nva7lvZPliY3pgInkuK3lm6DlrZANCg0KICAgICAgLy8g5aaC5p6c5Y+q5pi+56S66YeH6LSt5paH5Lu277yM5L2/55So6YeH6LSt5paH5Lu26aG156CB5L+h5oGvDQogICAgICBpZiAodGhpcy5pc1Nob3dQcm9jdXJlbWVudCAmJiAhdGhpcy5pc1Nob3dSZXNwb25zZSkgew0KCSAgICAgIGlmICghdGhpcy5wcm9jdXJlbWVudFBkZlJlbmRlcmVkKSB7DQoJCSAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6YeH6LSt5paH5Lu25q2j5Zyo5riy5p+T5Lit77yM6K+356iN5YCZ5YaN6K+VIik7DQoJCSAgICAgIHJldHVybjsNCgkgICAgICB9DQoJCQkJDQogICAgICAgIGlmIChmYWN0b3JJdGVtLmp1bXBUb1BhZ2UpIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLnByb2N1cmVtZW50LnNraXBQYWdlKGZhY3Rvckl0ZW0uanVtcFRvUGFnZSk7IC8vIOmHh+i0reaWh+S7tui3s+mhtQ0KICAgICAgICB9IGVsc2UgaWYgKHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlICYmIHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlW2ZhY3Rvckl0ZW0uaXRlbU5hbWVdKSB7DQogICAgICAgICAgdGhpcy4kcmVmcy5wcm9jdXJlbWVudC5za2lwUGFnZSh0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZVtmYWN0b3JJdGVtLml0ZW1OYW1lXSk7IC8vIOmHh+i0reaWh+S7tui3s+mhtQ0KICAgICAgICB9DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5pi+56S65ZON5bqU5paH5Lu25oiW5a+55q+U5qih5byP77yM6ZyA6KaB6YCJ5oup5L6b5bqU5ZWGDQogICAgICBpZiAoIXRoaXMuc3VwcGxpZXJGYWN0b3JQYWdlIHx8IE9iamVjdC5rZXlzKHRoaXMuc3VwcGxpZXJGYWN0b3JQYWdlKS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7flhYjpgInmi6nkvpvlupTllYYiKTsgLy8g5pyq6YCJ5L6b5bqU5ZWG5o+Q56S6DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g6Lez6L2s5Yiw5ZON5bqU5paH5Lu25a+55bqU6aG156CBDQogICAgICBpZiAodGhpcy5pc1Nob3dSZXNwb25zZSAmJiB0aGlzLiRyZWZzLnJlc3BvbnNlKSB7DQoJICAgICAgaWYgKCF0aGlzLnJlc3BvbnNlUGRmUmVuZGVyZWQpIHsNCgkJICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLlk43lupTmlofku7bmraPlnKjmuLLmn5PkuK3vvIzor7fnqI3lgJnlho3or5UiKTsNCgkJICAgICAgcmV0dXJuOw0KCSAgICAgIH0NCiAgICAgICAgdGhpcy4kcmVmcy5yZXNwb25zZS5za2lwUGFnZSh0aGlzLnN1cHBsaWVyRmFjdG9yUGFnZVt0aGlzLnNlbGVjdGVkRmFjdG9yTm9kZS5pdGVtTmFtZV0pOyAvLyDlk43lupTmlofku7bot7PpobUNCiAgICAgIH0NCg0KICAgICAgLy8g6Lez6L2s5Yiw6YeH6LSt5paH5Lu25a+55bqU6aG156CBDQogICAgICBpZiAodGhpcy5pc1Nob3dQcm9jdXJlbWVudCAmJiB0aGlzLiRyZWZzLnByb2N1cmVtZW50KSB7DQoJICAgICAgaWYgKCF0aGlzLnByb2N1cmVtZW50UGRmUmVuZGVyZWQpIHsNCgkJICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLph4fotK3mlofku7bmraPlnKjmuLLmn5PkuK3vvIzor7fnqI3lgJnlho3or5UiKTsNCgkJICAgICAgcmV0dXJuOw0KCSAgICAgIH0NCgkJCQkNCiAgICAgICAgLy8g5Zyo5a+55q+U5qih5byP5LiL77yM6YeH6LSt5paH5Lu25bqU6K+l6Lez6L2s5Yiw6YeH6LSt5paH5Lu255qE5a+55bqU6aG156CB77yM6ICM5LiN5piv5L6b5bqU5ZWG55qE6aG156CBDQogICAgICAgIGlmICh0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZSAmJiB0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZVtmYWN0b3JJdGVtLml0ZW1OYW1lXSkgew0KICAgICAgICAgIHRoaXMuJHJlZnMucHJvY3VyZW1lbnQuc2tpcFBhZ2UodGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2VbZmFjdG9ySXRlbS5pdGVtTmFtZV0pOyAvLyDph4fotK3mlofku7bot7PpobUNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlnKjlr7nmr5TmqKHlvI/kuIvvvIzlpoLmnpzmsqHmnInph4fotK3mlofku7bpobXnoIHkv6Hmga/vvIzliJnlj6rot7Povazlk43lupTmlofku7bnmoTpobXnoIHvvIzkuI3ot7Povazph4fotK3mlofku7YNCiAgICAgICAgICAvLyDov5nmoLflj6/ku6Xpgb/lhY3ph4fotK3mlofku7blkozlk43lupTmlofku7bmmL7npLrkuI3lkIznmoTlhoXlrrnpgKDmiJDmt7fmt4YNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQoJICANCgkgIC8qKg0KCSAgICog5qOA5p+l5piv5ZCm5Y+v5Lul6Lez6L2s6aG16Z2iDQoJICAgKiBAcmV0dXJucyB7Ym9vbGVhbn0g5piv5ZCm5Y+v5Lul6Lez6L2sDQoJICAgKi8NCgkgIGNhbkp1bXBUb1BhZ2UoKSB7DQoJCSAgLy8g5aaC5p6c5Y+q5pi+56S66YeH6LSt5paH5Lu2DQoJCSAgaWYgKHRoaXMuaXNTaG93UHJvY3VyZW1lbnQgJiYgIXRoaXMuaXNTaG93UmVzcG9uc2UpIHsNCgkJCSAgcmV0dXJuIHRoaXMucHJvY3VyZW1lbnRQZGZSZW5kZXJlZDsNCgkJICB9DQoJCSAgLy8g5aaC5p6c5Y+q5pi+56S65ZON5bqU5paH5Lu2DQoJCSAgaWYgKHRoaXMuaXNTaG93UmVzcG9uc2UgJiYgIXRoaXMuaXNTaG93UHJvY3VyZW1lbnQpIHsNCgkJCSAgcmV0dXJuIHRoaXMucmVzcG9uc2VQZGZSZW5kZXJlZDsNCgkJICB9DQoJCSAgLy8g5aaC5p6c5a+55q+U5qih5byP77yI5Lik5Liq6YO95pi+56S677yJDQoJCSAgaWYgKHRoaXMuaXNTaG93UmVzcG9uc2UgJiYgdGhpcy5pc1Nob3dQcm9jdXJlbWVudCkgew0KCQkJICByZXR1cm4gdGhpcy5yZXNwb25zZVBkZlJlbmRlcmVkICYmIHRoaXMucHJvY3VyZW1lbnRQZGZSZW5kZXJlZDsNCgkJICB9DQoJCSAgcmV0dXJuIGZhbHNlOw0KCSAgfSwNCgkgIC8qKg0KCSAgICog5aSE55CGUERG5riy5p+T54q25oCB5Y+Y5YyWDQoJICAgKiBAcGFyYW0ge2Jvb2xlYW59IGlzUmVuZGVyZWQg5piv5ZCm5riy5p+T5a6M5oiQDQoJICAgKiBAcGFyYW0ge3N0cmluZ30gcGRmVHlwZSBQREbnsbvlnovvvJoncmVzcG9uc2UnIOaIliAncHJvY3VyZW1lbnQnDQoJICAgKi8NCgkgIGhhbmRsZVBkZlJlbmRlclN0YXR1c0NoYW5nZShpc1JlbmRlcmVkLCBwZGZUeXBlKSB7DQoJCSAgaWYgKHBkZlR5cGUgPT09ICdyZXNwb25zZScpIHsNCgkJCSAgdGhpcy5yZXNwb25zZVBkZlJlbmRlcmVkID0gaXNSZW5kZXJlZDsNCgkJICB9IGVsc2UgaWYgKHBkZlR5cGUgPT09ICdwcm9jdXJlbWVudCcpIHsNCgkJCSAgdGhpcy5wcm9jdXJlbWVudFBkZlJlbmRlcmVkID0gaXNSZW5kZXJlZDsNCgkJICB9DQoJCSAgDQoJCSAgaWYgKGlzUmVuZGVyZWQpIHsNCgkJCSAgY29uc29sZS5sb2coYCR7cGRmVHlwZSA9PT0gJ3Jlc3BvbnNlJyA/ICflk43lupQnIDogJ+mHh+i0rSd95paH5Lu25riy5p+T5a6M5oiQ77yM5Y+v5Lul6L+b6KGM6aG16Z2i6Lez6L2sYCk7DQoJCSAgfQ0KCSAgfSwNCgkgIA0KICAgIC8qKg0KICAgICAqIOaPkOS6pOivhOWIhuW5tuS/ruaUueS4k+Wutui/m+W6pg0KICAgICAqLw0KICAgIGFzeW5jIHN1Ym1pdCgpIHsNCiAgICAgIC8vIOWFiOS/neWtmOivhOWIhu+8jOWmguaenOS/neWtmOWksei0peWImeS4jee7p+e7reaPkOS6pA0KICAgICAgY29uc3Qgc2F2ZVJlc3VsdCA9IGF3YWl0IHRoaXMuc2F2ZVRlbXBSYXRpbmcoKTsNCiAgICAgIGlmICghc2F2ZVJlc3VsdCkgew0KICAgICAgICAvLyDkv53lrZjlpLHotKXvvIzkuI3nu6fnu63mj5DkuqTmtYHnqIsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBkYXRhID0gew0KICAgICAgICBwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwgLy8g6aG555uuSUQNCiAgICAgICAgZXhwZXJ0UmVzdWx0SWQ6IHRoaXMuZXhwZXJ0SW5mby5yZXN1bHRJZCwgLy8g5LiT5a6257uT5p6cSUQNCiAgICAgICAgc2NvcmluZ01ldGhvZEl0ZW1JZDogdGhpcy4kcm91dGUucXVlcnkuc2NvcmluZ01ldGhvZEl0ZW1JZCwgLy8g6K+E5YiG5pa55rOV6aG5SUQNCiAgICAgIH07DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNoZWNrUmV2aWV3U3VtbWFyeShkYXRhKTsgLy8g5qOA5p+l6K+E5a6h5rGH5oC7DQoNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIC8vIOS/ruaUueS4k+Wutui/m+W6pg0KICAgICAgICAgIGNvbnN0IHN0YXR1cyA9IHsNCiAgICAgICAgICAgIGV2YWxFeHBlcnRTY29yZUluZm9JZDogSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZXZhbEV4cGVydFNjb3JlSW5mbyIpKS5ldmFsRXhwZXJ0U2NvcmVJbmZvSWQsIC8vIOS4k+WutuivhOWIhuS/oeaBr0lEDQogICAgICAgICAgICBldmFsU3RhdGU6IDEsIC8vIOi/m+W6pueKtuaAgQ0KICAgICAgICAgIH07DQogICAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZWRpdEV2YWxFeHBlcnRTY29yZUluZm8oc3RhdHVzKTsgLy8g57yW6L6R5LiT5a626K+E5YiG54q25oCBDQogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5o+Q5Lqk5oiQ5YqfIik7IC8vIOaPkOS6pOaIkOWKn+aPkOekug0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLiRlbWl0KCJzZW5kIiwgInR3byIpOyAvLyDlj5HpgIHkuovku7YNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcocmVzcG9uc2UubXNnKTsgLy8g5o+Q5Lqk5aSx6LSl5o+Q56S6DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5o+Q5Lqk5aSx6LSlIik7IC8vIOW8guW4uOaPkOekug0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqDQogICAgICog5pi+56S66YeH6LSt5paH5Lu2UERGDQogICAgICovDQogICAgdmlld1B1cmNoYXNpbmcoKSB7DQogICAgICB0aGlzLmFjdGl2ZUJ1dHRvbiA9ICdwcm9jdXJlbWVudCc7IC8vIOiuvue9ruW9k+WJjea/gOa0u+aMiemSrg0KICAgICAgdGhpcy5pc0RvdWJsZVZpZXcgPSBmYWxzZTsgLy8g5Y2V5paH5Lu25qih5byPDQogICAgICB0aGlzLmlzU2hvd1Jlc3BvbnNlID0gZmFsc2U7IC8vIOS4jeaYvuekuuWTjeW6lOaWh+S7tg0KICAgICAgdGhpcy5pc1Nob3dQcm9jdXJlbWVudCA9IHRydWU7IC8vIOaYvuekuumHh+i0reaWh+S7tg0KICAgICAgLy8g5Y+z5L6n6K+E5YiG6aG55pi+56S65Li66YeH6LSt5paH5Lu255qE6K+E5YiG6aG5DQogICAgICBsZXQgcGFnZVByb2N1cmVtZW50QXJyID0gW107IC8vIOmHh+i0reaWh+S7tuivhOWIhumhueaVsOe7hA0KDQogICAgICBmb3IgKGxldCBpdGVtIGluIHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlKXsNCiAgICAgICAgcGFnZVByb2N1cmVtZW50QXJyLnB1c2goew0KICAgICAgICAgIGl0ZW1OYW1lOiBpdGVtLA0KICAgICAgICAgIGp1bXBUb1BhZ2U6IHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlW2l0ZW1dDQogICAgICAgIH0pDQogICAgICB9DQoNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuc2NvcmluZ01ldGhvZC51aXRlbXMpOw0KICAgICAgY29uc29sZS5sb2cocGFnZVByb2N1cmVtZW50QXJyKQ0KICAgICAgdGhpcy5wYWdlUHJvY3VyZW1lbnQgPSBbXTsNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5zY29yaW5nTWV0aG9kLnVpdGVtcy5sZW5ndGg7aSsrKXsNCiAgICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCBwYWdlUHJvY3VyZW1lbnRBcnIubGVuZ3RoO2orKyl7DQogICAgICAgICAgaWYgKHRoaXMuc2NvcmluZ01ldGhvZC51aXRlbXNbaV0uaXRlbU5hbWUgPT0gcGFnZVByb2N1cmVtZW50QXJyW2pdLml0ZW1OYW1lKXsNCiAgICAgICAgICAgIHRoaXMucGFnZVByb2N1cmVtZW50LnB1c2goey4uLnRoaXMuc2NvcmluZ01ldGhvZC51aXRlbXNbaV0sLi4ucGFnZVByb2N1cmVtZW50QXJyW2pdfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICBjb25zb2xlLmxvZyh0aGlzLnBhZ2VQcm9jdXJlbWVudCkNCiAgICB9LA0KICAgIC8qKg0KICAgICAqIOi3s+i9rOWIsOS6jOasoeaKpeS7t+mhtemdog0KICAgICAqLw0KICAgIHNlY29uZE9mZmVyKCkgew0KICAgICAgY29uc3QgcXVlcnkgPSB7DQogICAgICAgIHByb2plY3RJZDogdGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkLCAvLyDpobnnm65JRA0KICAgICAgICB6amhtOiB0aGlzLiRyb3V0ZS5xdWVyeS56amhtLCAvLyDkuJPlrrbor4Hku7blj7fnoIENCiAgICAgICAgc2NvcmluZ01ldGhvZEl0ZW1JZDogSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgidGVuZGVyT2ZmZXJTY29yaW5nTWV0aG9kSXRlbXMiKSksIC8vIOS6jOasoeaKpeS7t+ivhOWIhuaWueazlemhuUlEDQogICAgICB9Ow0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAiL3NlY29uZE9mZmVyIiwgcXVlcnkgfSk7IC8vIOi3s+i9rOmhtemdog0KICAgIH0sDQogICAgLyoqDQogICAgICog6Lez6L2s5Yiw6K+i5qCH6aG16Z2iDQogICAgICovDQogICAgYmlkSW5xdWlyeSgpIHsNCiAgICAgIGNvbnN0IHF1ZXJ5ID0gew0KICAgICAgICBwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwgLy8g6aG555uuSUQNCiAgICAgICAgempobTogdGhpcy4kcm91dGUucXVlcnkuempobSwgLy8g5LiT5a626K+B5Lu25Y+356CBDQogICAgICAgIHNjb3JpbmdNZXRob2RJdGVtSWQ6IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oInRlbmRlck9mZmVyU2NvcmluZ01ldGhvZEl0ZW1zIikpLCAvLyDor6LmoIfor4TliIbmlrnms5XpoblJRA0KICAgICAgfTsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogIi9iaWRJbnF1aXJ5IiwgcXVlcnkgfSk7IC8vIOi3s+i9rOmhtemdog0KICAgIH0sDQogICAgLyoqDQogICAgICog6I635Y+W5Zug57Sg5a+55bqU6aG156CB77yI5LuO5pys5Zyw57yT5a2Y77yJDQogICAgICovDQogICAgZ2V0RmFjdG9yc1BhZ2UoKSB7DQogICAgICB0aGlzLmZhY3RvcnNQYWdlTWFwID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZW50RG9jUmVzcG9uc2VQYWdlIikpOyAvLyDojrflj5blm6DlrZDpobXnoIHmmKDlsIQNCiAgICB9LA0KICAgIC8qKg0KICAgICAqIOWIneWni+WMluS4k+WutuWSjOacrOWcsOaVsOaNru+8jOWPquWcqG1vdW50ZWTml7bosIPnlKjkuIDmrKENCiAgICAgKi8NCiAgICBpbml0TG9jYWxEYXRhKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy5sb2NhbEV4cGVydEluZm8gPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJleHBlcnRJbmZvIikgfHwgInt9Iik7IC8vIOiOt+WPluacrOWcsOS4k+WutuS/oeaBrw0KICAgICAgICB0aGlzLmxvY2FsRW50RG9jUmVzcG9uc2VQYWdlID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZW50RG9jUmVzcG9uc2VQYWdlIikgfHwgInt9Iik7IC8vIOiOt+WPluacrOWcsOWTjeW6lOaWh+S7tumhteeggQ0KICAgICAgICB0aGlzLmxvY2FsRmFjdG9yc1BhZ2VNYXAgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJlbnREb2NSZXNwb25zZVBhZ2UiKSB8fCAie30iKTsgLy8g6I635Y+W5pys5Zyw5Zug5a2Q6aG156CB5pig5bCEDQogICAgICAgIHRoaXMuZXhwZXJ0SW5mbyA9IHRoaXMubG9jYWxFeHBlcnRJbmZvOyAvLyDorr7nva7kuJPlrrbkv6Hmga8NCiAgICAgICAgdGhpcy5lbnREb2NSZXNwb25zZVBhZ2UgPSB0aGlzLmxvY2FsRW50RG9jUmVzcG9uc2VQYWdlOyAvLyDorr7nva7lk43lupTmlofku7bpobXnoIENCiAgICAgICAgdGhpcy5mYWN0b3JzUGFnZU1hcCA9IHRoaXMubG9jYWxGYWN0b3JzUGFnZU1hcDsgLy8g6K6+572u5Zug5a2Q6aG156CB5pig5bCEDQogICAgICAgIGNvbnNvbGUubG9nKCLmnKzlnLDmlbDmja7lt7LliJ3lp4vljJYiLCB7IGV4cGVydEluZm86IHRoaXMuZXhwZXJ0SW5mbyB9KTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWIneWni+WMluacrOWcsOaVsOaNruWksei0pToiLCBlcnJvcik7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOWIneWni+WMluS4k+WutuS/oeaBr++8iOeUqOS6juWTjeW6lOS4k+WutuS/oeaBr+abtOaWsO+8iQ0KICAgICAqLw0KICAgIGluaXRFeHBlcnRJbmZvKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgZXhwZXJ0SW5mb1N0ciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJleHBlcnRJbmZvIik7DQogICAgICAgIGlmIChleHBlcnRJbmZvU3RyKSB7DQogICAgICAgICAgdGhpcy5sb2NhbEV4cGVydEluZm8gPSBKU09OLnBhcnNlKGV4cGVydEluZm9TdHIpOw0KICAgICAgICAgIHRoaXMuZXhwZXJ0SW5mbyA9IHRoaXMubG9jYWxFeHBlcnRJbmZvOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCLkuJPlrrbkv6Hmga/lt7LliLfmlrAiLCB0aGlzLmV4cGVydEluZm8pOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLliLfmlrDkuJPlrrbkv6Hmga/lpLHotKU6IiwgZXJyb3IpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqDQogICAgICog6aG16Z2i5Yid5aeL5YyW77yM5Yqg6L295L6b5bqU5ZWG44CB6K+E5YiG5pa55rOV44CB5paH5Lu2562J77yI5bm25Y+R6K+35rGC77yJDQogICAgICovDQogICAgYXN5bmMgaW5pdFBhZ2UoKSB7DQogICAgICB0aGlzLmluaXRMb2NhbERhdGEoKTsgLy8g5Yid5aeL5YyW5pys5Zyw5pWw5o2uDQogICAgICB0cnkgew0KICAgICAgICAvLyDlubblj5Hojrflj5bkvpvlupTllYbjgIHor4TliIbmlrnms5XjgIHmlofku7YNCiAgICAgICAgY29uc3QgW3N1cHBsaWVyUmVzLCBhcHByb3ZhbFJlcywgZmlsZXNSZXNdID0gYXdhaXQgUHJvbWlzZS5hbGwoWw0KICAgICAgICAgIHN1cHBsaWVySW5mbyh7IHByb2plY3RJZDogdGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkIH0pLCAvLyDojrflj5bkvpvlupTllYYNCiAgICAgICAgICBhcHByb3ZhbFByb2Nlc3ModGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkLCB0aGlzLmV4cGVydEluZm8ucmVzdWx0SWQpLCAvLyDojrflj5bor4TliIbmlrnms5UNCiAgICAgICAgICBmaWxlc0J5SWQodGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkKSAvLyDojrflj5bpobnnm67mlofku7YNCiAgICAgICAgXSk7DQogICAgICAgIC8vIOWkhOeQhuS+m+W6lOWVhg0KICAgICAgICBpZiAoc3VwcGxpZXJSZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5zdXBwbGllck9wdGlvbnMgPSBzdXBwbGllclJlcy5yb3dzLmZpbHRlcihpdGVtID0+IGl0ZW0uaXNBYmFuZG9uZWRCaWQgPT0gMCk7IC8vIOi/h+a7pOacquW8g+agh+S+m+W6lOWVhg0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhzdXBwbGllclJlcy5tc2cpOyAvLyDojrflj5blpLHotKXmj5DnpLoNCiAgICAgICAgfQ0KICAgICAgICAvLyDlpITnkIbor4TliIbmlrnms5UNCiAgICAgICAgaWYgKGFwcHJvdmFsUmVzLmNvZGUgPT09IDIwMCkgew0KCSAgICAgICAgdGhpcy5hdHRhY2htZW50c0xpc3QgPSBhcHByb3ZhbFJlcy5kYXRhLmJ1c2lUZW5kZXJOb3RpY2UuYXR0YWNobWVudHMuZmlsdGVyKGl0ZW0gPT4gaXRlbS5maWxlVHlwZSA9PSAiMCIpOw0KICAgICAgICAgIHRoaXMuc2NvcmluZ01ldGhvZCA9IGFwcHJvdmFsUmVzLmRhdGEuc2NvcmluZ01ldGhvZFVpbmZvLnNjb3JpbmdNZXRob2RJdGVtcy5maW5kKA0KICAgICAgICAgICAgaXRlbSA9PiBpdGVtLnNjb3JpbmdNZXRob2RJdGVtSWQgPT0gdGhpcy4kcm91dGUucXVlcnkuc2NvcmluZ01ldGhvZEl0ZW1JZA0KICAgICAgICAgICk7IC8vIOiOt+WPluW9k+WJjeivhOWIhuaWueazlQ0KICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCJldmFsUHJvamVjdEV2YWx1YXRpb25Qcm9jZXNzIiwgSlNPTi5zdHJpbmdpZnkodGhpcy5zY29yaW5nTWV0aG9kLmV2YWxQcm9qZWN0RXZhbHVhdGlvblByb2Nlc3MpKTsgLy8g57yT5a2Y6K+E5YiG5rWB56iLDQogICAgICAgICAgdGhpcy5yYXRpbmdTdGF0ZU1hcCA9IHRoaXMuc2NvcmluZ01ldGhvZC51aXRlbXMucmVkdWNlKChhY2MsIGl0ZW0pID0+IHsNCiAgICAgICAgICAgIGFjY1tpdGVtLmVudE1ldGhvZEl0ZW1JZF0gPSB7IHN0YXRlOiBudWxsLCByZWFzb246ICcnIH07IC8vIOWIneWni+WMluivhOWIhueKtuaAgQ0KICAgICAgICAgICAgcmV0dXJuIGFjYzsNCiAgICAgICAgICB9LCB7fSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGFwcHJvdmFsUmVzLm1zZyk7IC8vIOiOt+WPluWksei0peaPkOekug0KICAgICAgICB9DQogICAgICAgIC8vIOWkhOeQhuaWh+S7tg0KICAgICAgICBpZiAoZmlsZXNSZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5wcm9qZWN0RmlsZXMgPSBmaWxlc1Jlcy5kYXRhOyAvLyDorr7nva7pobnnm67mlofku7YNCiAgICAgICAgICBpZiAodGhpcy5wcm9qZWN0RmlsZXMudGVuZGVyTm90aWNlRmlsZVBhdGgpIHsNCiAgICAgICAgICAgIHRoaXMucHJvY3VyZW1lbnRQZGZVcmwgPSB0aGlzLnByb2plY3RGaWxlcy50ZW5kZXJOb3RpY2VGaWxlUGF0aDsgLy8g6K6+572u6YeH6LSt5paH5Lu2UERGDQogICAgICAgICAgfQ0KICAgICAgICAgIC8vIGlmICh0aGlzLnByb2plY3RGaWxlcy5maWxlKSB7DQogICAgICAgICAgLy8gICB0aGlzLnJlc3BvbnNlUGRmVXJsID0gdGhpcy5wcm9qZWN0RmlsZXMuZmlsZVswXTsgLy8g6K6+572u5ZON5bqU5paH5Lu2UERGDQogICAgICAgICAgLy8gfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhmaWxlc1Jlcy5tc2cpOyAvLyDojrflj5blpLHotKXmj5DnpLoNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLpobXpnaLliJ3lp4vljJblpLHotKUiKTsgLy8g5byC5bi45o+Q56S6DQogICAgICB9DQogICAgfSwNCgkgIA0KCSAgZG93bmxvYWRGaWxlKGl0ZW0pew0KCQkgIHRoaXMuJGRvd25sb2FkLnppcChpdGVtLmZpbGVQYXRoLGl0ZW0uZmlsZU5hbWUpOw0KCSAgfSwNCgkgIA0KCSAgDQoJICAvLyA9PT09PT09PT09IOaCrOWBnOebuOWFsyA9PT09PT09PT09DQoJICAvKioNCgkgICAqIOaYvuekuuivhOWIhumhueaCrOa1ruahhg0KCSAgICogQHBhcmFtIHtPYmplY3R9IGZhY3Rvckl0ZW0g6K+E5YiG6aG55a+56LGhDQoJICAgKi8NCgkgIHNob3dGYWN0b3JUb29sdGlwKGZhY3Rvckl0ZW0pIHsNCgkJICBpZiAoIWZhY3Rvckl0ZW0uaXRlbVJlbWFyaykgcmV0dXJuOyAvLyDlpoLmnpzmsqHmnInor4TlrqHlhoXlrrnliJnkuI3mmL7npLoNCgkJICANCgkJICAvLyDmuIXpmaTkuYvliY3nmoTlrprml7blmagNCgkJICBpZiAodGhpcy50b29sdGlwVGltZXIpIHsNCgkJCSAgY2xlYXJUaW1lb3V0KHRoaXMudG9vbHRpcFRpbWVyKTsNCgkJICB9DQoJCSAgDQoJCSAgLy8g5bu26L+f5pi+56S65oKs5rWu5qGG77yM6YG/5YWN5b+r6YCf56e75Yqo5pe26aKR57mB5pi+56S6DQoJCSAgdGhpcy50b29sdGlwVGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHsNCgkJCSAgdGhpcy5ob3ZlcmVkRmFjdG9yTm9kZSA9IGZhY3Rvckl0ZW07DQoJCSAgfSwgMzAwKTsgLy8gMzAwbXPlu7bov58NCgkgIH0sDQoJICANCgkgIC8qKg0KCSAgICog6ZqQ6JeP6K+E5YiG6aG55oKs5rWu5qGGDQoJICAgKi8NCgkgIGhpZGVGYWN0b3JUb29sdGlwKCkgew0KCQkgIC8vIOa4hemZpOWumuaXtuWZqA0KCQkgIGlmICh0aGlzLnRvb2x0aXBUaW1lcikgew0KCQkJICBjbGVhclRpbWVvdXQodGhpcy50b29sdGlwVGltZXIpOw0KCQkJICB0aGlzLnRvb2x0aXBUaW1lciA9IG51bGw7DQoJCSAgfQ0KCQkgIA0KCQkgIC8vIOW7tui/n+makOiXj++8jOe7meeUqOaIt+aXtumXtOenu+WKqOWIsOaCrOa1ruahhuS4ig0KCQkgIHNldFRpbWVvdXQoKCkgPT4gew0KCQkJICB0aGlzLmhvdmVyZWRGYWN0b3JOb2RlID0gbnVsbDsNCgkJICB9LCAxMDApOw0KCSAgfSwNCgkgIA0KCSAgLyoqDQoJICAgKiDmuIXpmaTmgqzmta7moYblrprml7blmajvvIjlvZPpvKDmoIfnp7vliqjliLDmgqzmta7moYbkuIrml7bvvIkNCgkgICAqLw0KCSAgY2xlYXJUb29sdGlwVGltZXIoKSB7DQoJCSAgaWYgKHRoaXMudG9vbHRpcFRpbWVyKSB7DQoJCQkgIGNsZWFyVGltZW91dCh0aGlzLnRvb2x0aXBUaW1lcik7DQoJCQkgIHRoaXMudG9vbHRpcFRpbWVyID0gbnVsbDsNCgkJICB9DQoJICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQoJICB0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZSA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oImVudERvY1Byb2N1cmVtZW50UGFnZSIpKTsgLy8g5Yid5aeL5YyW6YeH6LSt5paH5Lu26aG156CB5L+h5oGvDQogICAgdGhpcy5pbml0UGFnZSgpOyAvLyDpobXpnaLmjILovb3ml7bliJ3lp4vljJbmlbDmja4NCiAgICB0aGlzLmdldEZhY3RvcnNQYWdlKCk7IC8vIOiOt+WPluWboOWtkOmhteeggQ0KICB9LA0KCWJlZm9yZURlc3Ryb3koKSB7DQoJCS8vIOa4heeQhuWumuaXtuWZqA0KCQlpZiAodGhpcy50b29sdGlwVGltZXIpIHsNCgkJCWNsZWFyVGltZW91dCh0aGlzLnRvb2x0aXBUaW1lcik7DQoJCQl0aGlzLnRvb2x0aXBUaW1lciA9IG51bGw7DQoJCX0NCgl9LA0KfTsNCg=="}, {"version": 3, "sources": ["one.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2OA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "one.vue", "sourceRoot": "src/views/expertReview/compliance", "sourcesContent": ["<template>\r\n  <!-- 页面主容器，flex布局，分为左中右三部分 -->\r\n  <div class=\"compliance-main\">\r\n    <!-- 左侧内容区，包含标题、操作按钮、PDF预览区 -->\r\n    <div class=\"compliance-left\">\r\n      <!-- 顶部标题和操作按钮区 -->\r\n      <div class=\"compliance-header\">\r\n        <!-- 标题及操作步骤图片 -->\r\n        <div class=\"compliance-title-group\">\r\n          <div class=\"compliance-title\">符合性评审</div> <!-- 页面主标题 -->\r\n          <div class=\"compliance-step-img-group\">\r\n            <div class=\"compliance-step-text\">该页面操作说明</div> <!-- 操作步骤说明文字 -->\r\n            <el-image class=\"compliance-step-img\" :src=\"helpImgList[0]\" :preview-src-list=\"helpImgList\">\r\n            </el-image> <!-- 操作步骤图片，可点击放大 -->\r\n          </div>\r\n        </div>\r\n\t      \r\n\t      <!-- 文件列表 -->\r\n\t      <div class=\"fileList\" style=\"width: 200px; border-right: 1px solid #e6e6e6; border-left: 1px solid #e6e6e6; padding: 10px; overflow-y: auto;\">\r\n\t\t      <div style=\"font-weight: bold; margin-bottom: 10px; color: #333;\">响应文件附件下载</div>\r\n\t\t      <el-card\r\n\t\t\t      v-for=\"(item, index) in attachmentsList\"\r\n\t\t\t      :key=\"index\"\r\n\t\t\t      class=\"fileItem\"\r\n\t\t\t      shadow=\"hover\"\r\n\t\t\t      @click.native=\"downloadFile(item)\"\r\n\t\t\t      style=\"margin-bottom: 8px; cursor: pointer;\"\r\n\t\t      >\r\n\t\t\t      <div style=\"display: flex; align-items: center; padding: 5px;\">\r\n\t\t\t\t      <i class=\"el-icon-document\" style=\"margin-right: 8px; color: #409EFF;\"></i>\r\n\t\t\t\t      <span style=\"font-size: 12px; flex: 1; word-break: break-all;\">{{ item.fileName }}</span>\r\n\t\t\t\t      <i class=\"el-icon-download\" style=\"margin-left: 8px; color: #999;\"></i>\r\n\t\t\t      </div>\r\n\t\t      </el-card>\r\n\t      </div>\r\n\t      \r\n        <!-- 右侧操作按钮区 -->\r\n        <div class=\"compliance-header-btns\">\r\n          <el-button class=\"item-button\" @click=\"bidInquiry\">询标</el-button> <!-- 跳转到询标页面 -->\r\n          <!-- <el-button class=\"item-button\" @click=\"secondOffer\">发起二次报价</el-button> -->\r\n          <div class=\"compliance-header-btns-bottom\">\r\n\t          <el-button\r\n\t\t          :class=\"['item-button', activeButton === 'procurement' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n\t\t          @click=\"viewPurchasing\">采购文件</el-button> <!-- 显示采购文件PDF -->\r\n\t          \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'response' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n              @click=\"showResponseFile\">响应文件</el-button> <!-- 显示响应文件PDF -->\r\n           \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'contrast' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n              @click=\"showFileContrast\">对比</el-button> <!-- 响应文件与采购文件对比 -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- PDF文件预览区，支持单文件和双文件对比 -->\r\n      <div style=\"height:82%\">\r\n        <!-- PDF预览区域 - 保持原始尺寸 -->\r\n        <div class=\"compliance-pdf-group\">\r\n\t        <!-- 采购文件PDF预览 -->\r\n\t        <div v-show=\"isShowProcurement\" :class=\"['compliance-pdf', isDoubleView ? 'compliance-pdf-border-left' : '']\">\r\n\t\t        <!--            <pdfView ref=\"procurement\" :pdfurl=\"procurementPdfUrl\" :uni_key=\"'procurement'\"></pdfView>-->\r\n\t\t        \r\n\t\t        <PdfViewImproved ref=\"procurement\"  :pdfurl=\"procurementPdfUrl\"  :page-height=\"800\"\r\n\t\t                         :buffer-size=\"2\" @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"/>\r\n\t        \r\n\t        </div>\r\n\t        \r\n          <!-- 响应文件PDF预览 -->\r\n          <div v-show=\"isShowResponse\" :class=\"['compliance-pdf', isDoubleView ? 'compliance-pdf-border-right' : '']\">\r\n<!--            <pdfView ref=\"response\" :pdfurl=\"responsePdfUrl\" :uni_key=\"'response'\"></pdfView>-->\r\n\t          \r\n\t          <PdfViewImproved ref=\"response\"  :pdfurl=\"responsePdfUrl\"  :page-height=\"800\" :buffer-size=\"2\"\r\n\t                           @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"/>\r\n\t          \r\n          </div>\r\n          \r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 中间分割线 -->\r\n    <div class=\"compliance-divider\"></div>\r\n    <!-- 右侧评分区 -->\r\n    <div class=\"compliance-right\">\r\n      <!-- 供应商选择下拉框 -->\r\n      <div class=\"compliance-select-group\">\r\n        <el-select class=\"compliance-select\" v-model=\"selectedSupplierName\" placeholder=\"请选择供应商\" @change=\"handleSupplierChange\">\r\n          <el-option v-for=\"item in supplierOptions\" :key=\"item.bidderName\" :label=\"item.bidderName\" :value=\"item.bidderName\">\r\n          </el-option>\r\n        </el-select>\r\n      </div>\r\n      <!-- 评分因子列表及操作区 -->\r\n      <div class=\"compliance-factors-group\" v-if=\"isShowResponse\">\r\n\t      <!-- PDF渲染状态提示 -->\r\n\t      <div v-if=\"!responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t      <i class=\"el-icon-loading\"></i>\r\n\t\t      <span>响应文件正在渲染中，请稍候...</span>\r\n\t      </div>\r\n\t      <div v-else class=\"render-status-tip success\">\r\n\t\t      <i class=\"el-icon-success\"></i>\r\n\t\t      <span>响应文件渲染完成，可以点击跳转</span>\r\n\t      </div>\r\n\t      \r\n        <!-- 判空后再渲染评分因子项列表，防止scoringMethod为null时报错 -->\r\n        <template v-if=\"scoringMethod && scoringMethod.uitems\">\r\n          <div v-for=\"(item, index) in scoringMethod.uitems\" :key=\"index\"\r\n               class=\"factor-item\" style=\"margin-bottom:10px\"\r\n               @mouseenter=\"showFactorTooltip(item)\"\r\n               @mouseleave=\"hideFactorTooltip\" >\r\n\t          <!-- 悬浮框 -->\r\n\t          <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t               class=\"factor-tooltip\"\r\n\t               @mouseenter=\"clearTooltipTimer\"\r\n\t               @mouseleave=\"hideFactorTooltip\">\r\n\t\t          <div class=\"tooltip-header\">\r\n\t\t\t          <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t          <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t          </div>\r\n\t\t          <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t          </div>\r\n\t          \r\n            <div>\r\n              <div class=\"factors\">\r\n                <!-- 评分因子名称，点击可跳转PDF对应页码 -->\r\n                <div class=\"compliance-factor-title-group factor-title\" :class=\"{ 'disabled': !canJumpToPage() }\">\r\n                  <div class=\"compliance-factor-title\" @click=\"handleShowFactorInfo(item)\">\r\n                    {{ item.itemName }}\r\n\t                  <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n                  </div>\r\n                </div>\r\n                <!-- 评分单选按钮（通过/不通过） -->\r\n                <div class=\"compliance-factor-radio-group\">\r\n                  <div>\r\n                    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n                    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- 不通过时填写原因 -->\r\n              <el-input v-if=\"ratingStateMap[item.entMethodItemId].state == 0\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n              </el-input>\r\n              <!-- 系统初验结果展示，绿色为通过，红色为未通过 -->\r\n              <span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n                <i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n                <i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n                {{checkResultNameMap[item.itemName]}}</span>\r\n              <div class=\"compliance-factor-divider\"></div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <!-- 提交按钮区 -->\r\n        <div class=\"compliance-submit-group\">\r\n          <!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"show\">保存</el-button></div> -->\r\n          <div><el-button class=\"item-button-little compliance-submit-btn\" @click=\"submit\">提交</el-button></div>\r\n        </div>\r\n        <!-- 当前选中评分因子的详细说明 -->\r\n        <div class=\"compliance-review-content\">\r\n          <div class=\"compliance-review-title\">评审内容：</div>\r\n          <div class=\"compliance-review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n        </div>\r\n      </div>\r\n\r\n\t    <div class=\"compliance-factors-group\" v-else>\r\n\t\t    <!-- PDF渲染状态提示 -->\r\n\t\t    <div v-if=\"!procurementPdfRendered\" class=\"render-status-tip\">\r\n\t\t\t    <i class=\"el-icon-loading\"></i>\r\n\t\t\t    <span>采购文件正在渲染中，请稍候...</span>\r\n\t\t    </div>\r\n\t\t    <div v-else class=\"render-status-tip success\">\r\n\t\t\t    <i class=\"el-icon-success\"></i>\r\n\t\t\t    <span>采购文件渲染完成，可以点击跳转</span>\r\n\t\t    </div>\r\n\t\t    \r\n\t\t    <!-- 判空后再渲染评分因子项列表，防止scoringMethod为null时报错 -->\r\n\t\t    <template v-if=\"pageProcurement\">\r\n\t\t\t    <div v-for=\"(item, index) in pageProcurement\" :key=\"index\" class=\"factor-item\" style=\"margin-bottom:10px\"\r\n\t\t\t         @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t         @mouseleave=\"hideFactorTooltip\" >\r\n\t\t\t\t    <!-- 悬浮框 -->\r\n\t\t\t\t    <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t\t         class=\"factor-tooltip\"\r\n\t\t\t\t         @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t\t         @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t    <div class=\"tooltip-header\">\r\n\t\t\t\t\t\t    <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t\t    <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t\t    </div>\r\n\t\t\t\t\t    <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t\t    </div>\r\n\t\t\t\t    <div>\r\n\t\t\t\t\t    <div class=\"factors\">\r\n\t\t\t\t\t\t    <!-- 评分因子名称，点击可跳转PDF对应页码 -->\r\n\t\t\t\t\t\t    <div class=\"compliance-factor-title-group factor-title\" :class=\"{ 'disabled': !canJumpToPage() }\">\r\n\t\t\t\t\t\t\t    <div class=\"compliance-factor-title\" @click=\"handleShowFactorInfo(item)\">\r\n\t\t\t\t\t\t\t\t    {{ item.itemName }}\r\n\t\t\t\t\t\t\t\t    <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    <!-- 评分单选按钮（通过/不通过） -->\r\n\t\t\t\t\t\t    <div class=\"compliance-factor-radio-group\">\r\n\t\t\t\t\t\t\t    <div>\r\n\t\t\t\t\t\t\t\t    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n\t\t\t\t\t\t\t\t    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n\t\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t    </div>\r\n\t\t\t\t\t    <!-- 不通过时填写原因 -->\r\n\t\t\t\t\t    <el-input v-if=\"ratingStateMap[item.entMethodItemId].state == 0\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n\t\t\t\t\t    </el-input>\r\n\t\t\t\t\t    <!-- 系统初验结果展示，绿色为通过，红色为未通过 -->\r\n\t\t\t\t\t    <span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n                <i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n                <i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n                {{checkResultNameMap[item.itemName]}}</span>\r\n\t\t\t\t\t    <div class=\"compliance-factor-divider\"></div>\r\n\t\t\t\t    </div>\r\n\t\t\t    </div>\r\n\t\t    </template>\r\n\t\t    <!-- 提交按钮区 -->\r\n\t\t    <div class=\"compliance-submit-group\">\r\n\t\t\t    <!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"show\">保存</el-button></div> -->\r\n\t\t\t    <div><el-button class=\"item-button-little compliance-submit-btn\" @click=\"submit\">提交</el-button></div>\r\n\t\t    </div>\r\n\t\t    <!-- 当前选中评分因子的详细说明 -->\r\n\t\t    <div class=\"compliance-review-content\">\r\n\t\t\t    <div class=\"compliance-review-title\">评审内容：</div>\r\n\t\t\t    <div class=\"compliance-review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n\t\t    </div>\r\n\t    </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 常量定义\r\nconst PASS = '1'; // 通过\r\nconst FAIL = '0'; // 不通过\r\nconst CHECK_PASS = '系统初验通过'; // 系统初验通过文本\r\nconst CHECK_FAIL = '系统初验未通过'; // 系统初验未通过文本\r\n\r\nimport {\r\n  supplierInfo, // 获取供应商信息API\r\n  approvalProcess, // 获取评分方法API\r\n  scoringFactors, // 提交评分因子API\r\n  checkReviewSummary, // 检查评审汇总API\r\n  filesById, // 获取项目相关文件API\r\n} from \"@/api/expert/review\"; // 导入专家评审相关API\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\"; // 获取评分详情API\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\"; // 编辑专家评分状态API\r\nimport { resDocReviewFactorsDecision } from \"@/api/docResponse/entInfo\"; // 获取响应文件评审因子决策API\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      supplierOptions: [], // 供应商下拉选项列表\r\n      scoringMethod: null, // 当前评分方法对象\r\n      selectedFactorNode: {}, // 当前选中的评分因子节点\r\n      selectedSupplierName: '', // 当前选中的供应商名称\r\n      selectedSupplier: {}, // 当前选中的供应商对象\r\n      expertInfo: {}, // 当前专家信息\r\n      ratingStateMap: {}, // 评分项状态映射（key为评分项ID，value为{state, reason}）\r\n      projectFiles: {}, // 项目相关文件对象\r\n      isShowResponse: false, // 是否显示响应文件\r\n      isShowProcurement: false, // 是否显示采购文件\r\n      isDoubleView: false, // 是否双文件对比模式\r\n      factorDetailList: [], // 评分因子详细列表\r\n      entDocResponsePage: null, // 企业响应文件页码信息\r\n      factorsPageMap: null, // 供应商因子页码映射\r\n      supplierFactorPage: null, // 当前供应商因子页码\r\n      responsePdfUrl: null, // 响应文件PDF地址\r\n      procurementPdfUrl: null, // 采购文件PDF地址\r\n\r\n      // 按钮状态管理\r\n      activeButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n\t    entDocProcurementPage: {}, // 采购文件页码信息\r\n      pageProcurement:[], // 采购文件的评分项\r\n      attachmentsList:[], // 文件列表\r\n\t    \r\n\t    // PDF渲染状态管理\r\n\t    responsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n\t    procurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n\t    helpImgList: [\"/evalution/help.jpg\"], // 操作帮助图片列表\r\n      // 评分项名称与后端字段映射\r\n      factorCodeMap: {\r\n        \"特定资格要求\": \"zgzs\",\r\n        \"响应内容\": \"jsplb\",\r\n        \"采购需求\": \"jsplb\",\r\n        \"供货期限\": \"ghqx\",\r\n        \"投标报价\": \"tbbj\"\r\n      },\r\n      checkResult: {}, // 系统初验结果对象\r\n      // 系统初验结果名称映射\r\n      checkResultNameMap: {\r\n        \"符合《中华人民共和国政府采购法》第二十二条规定\": CHECK_PASS,\r\n        \"特定资格要求\": CHECK_PASS,\r\n        \"信用查询\": CHECK_PASS,\r\n        \"响应人名称\": CHECK_PASS,\r\n        \"响应内容\": CHECK_PASS,\r\n        \"采购需求\": CHECK_PASS,\r\n        \"供货期限\": CHECK_PASS,\r\n        \"投标报价\": CHECK_PASS\r\n      },\r\n      // 本地缓存数据\r\n      localExpertInfo: null, // 本地专家信息\r\n      localEntDocResponsePage: null, // 本地响应文件页码\r\n      localFactorsPageMap: null, // 本地因子页码映射\r\n\t    \r\n\t    // 悬停状态管理\r\n\t    hoveredFactorNode: null, // 悬停时的评分项\r\n\t    tooltipTimer: null, // 悬浮框显示定时器\r\n    };\r\n  },\r\n  methods: {\r\n    /**\r\n     * 校验所有评分项是否填写完整\r\n     * @returns {boolean} 是否全部填写\r\n     */\r\n    validateRatings() {\r\n      for (const item of this.scoringMethod.uitems) { // 遍历所有评分项\r\n        const state = this.ratingStateMap[item.entMethodItemId].state; // 获取评分状态\r\n        const reason = this.ratingStateMap[item.entMethodItemId].reason; // 获取评分原因\r\n        // 评分结果未填写\r\n        if (state === null || state === '') {\r\n          // this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`); // 提示未填写\r\n          return true;\r\n        }\r\n        // 不通过但未填写原因\r\n        if (state === FAIL && (!reason || reason.trim() === '')) {\r\n          this.$message.warning(`${item.itemName}评审不通过但未填写备注，不进行保存`); // 提示未填写原因\r\n          return false;\r\n        }\r\n      }\r\n      return true; // 全部填写返回true\r\n    },\r\n    /**\r\n     * 获取系统初验结果（通过/未通过）\r\n     * @param {string} factorName 评分项名称\r\n     * @returns {string} 1-通过 0-未通过\r\n     */\r\n    getCheckResultState(factorName) {\r\n      if (!this.checkResult || Object.keys(this.checkResult).length === 0) return ''; // 没有初验结果直接返回空\r\n      let code = this.factorCodeMap[factorName]; // 获取评分项对应的后端字段\r\n      let check = PASS; // 默认通过\r\n      if (code) {\r\n        check = this.checkResult[code]; // 获取初验结果\r\n        // 投标报价特殊处理\r\n        if (factorName === \"投标报价\" && check === PASS) {\r\n          check = this.checkResult['mxbjb']; // 明细报价表\r\n        }\r\n      }\r\n      // 设置初验结果名称\r\n      if (check === FAIL) {\r\n        this.checkResultNameMap[factorName] = CHECK_FAIL; // 未通过\r\n      } else {\r\n        check = PASS;\r\n        this.checkResultNameMap[factorName] = CHECK_PASS; // 通过\r\n      }\r\n      return check; // 返回初验结果\r\n    },\r\n    /**\r\n     * 重置所有评分项的状态\r\n     */\r\n    resetRatingStateMap() {\r\n      if (!this.scoringMethod) return; // 没有评分方法直接返回\r\n      for (const key of Object.keys(this.ratingStateMap)) { // 遍历所有评分项\r\n        this.ratingStateMap[key].state = null; // 重置状态\r\n        this.ratingStateMap[key].reason = ''; // 重置原因\r\n      }\r\n    },\r\n    /**\r\n     * 临时保存评分结果到后端\r\n     * 校验通过后才会保存\r\n     * @returns {boolean} 保存是否成功\r\n     */\r\n    async saveTempRating() {\r\n      if (!this.validateRatings()) return false; // 校验不通过不保存，返回false\r\n      // 构造提交数据\r\n      const data = this.scoringMethod.uitems.map(item => {\r\n        const itemId = item.entMethodItemId; // 获取评分项ID\r\n        return {\r\n          scoringMethodUitemId: itemId, // 评分项ID\r\n          expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n          entId: this.selectedSupplier.bidderId, // 供应商ID\r\n          evaluationResult: this.ratingStateMap[itemId].state, // 评分结果\r\n          evaluationRemark: this.ratingStateMap[itemId].reason || '' // 评分原因\r\n        };\r\n      }).filter(d => d.evaluationResult !== null && d.evaluationResult !== ''); // 过滤未填写的项\r\n      if (data.length > 0) {\r\n        try {\r\n          const response = await scoringFactors(data); // 提交评分因子\r\n          if (response.code === 200) {\r\n            this.$message.success(\"保存成功\"); // 保存成功提示\r\n            return true; // 保存成功返回true\r\n          } else {\r\n            this.$message.warning(response.msg); // 保存失败提示\r\n            return false; // 保存失败返回false\r\n          }\r\n        } catch (e) {\r\n          this.$message.error(\"保存失败\"); // 异常提示\r\n          return false; // 异常返回false\r\n        }\r\n      }\r\n      return true; // 没有数据需要保存时也返回true\r\n    },\r\n    /**\r\n     * 供应商切换事件，切换时自动保存上一个供应商评分，并并发获取新供应商的评分详情和系统初验\r\n     * @param {string} supplierName 供应商名称\r\n     */\r\n    async handleSupplierChange(supplierName) {\r\n      // 切换前保存上一个供应商评分\r\n      if (Object.keys(this.selectedSupplier).length !== 0) {\r\n        await this.saveTempRating(); // 保存评分\r\n      }\r\n      // 查找当前选中的供应商对象\r\n      this.selectedSupplier = this.supplierOptions.find(item => item.bidderName === supplierName); // 查找供应商\r\n      // 获取当前供应商因子页码\r\n      this.supplierFactorPage = this.factorsPageMap[this.selectedSupplier.bidderId]; // 获取页码\r\n      // 并发获取评分详情和系统初验\r\n      // 使用 Promise.allSettled 让两个请求独立执行，互不影响\r\n      try {\r\n        const [detailResult, checkResult] = await Promise.allSettled([\r\n          getDetailByPsxx({\r\n            expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n            projectId: this.$route.query.projectId, // 项目ID\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项ID\r\n          }),\r\n          resDocReviewFactorsDecision({\r\n            projectId: this.$route.query.projectId, // 项目ID\r\n            entId: this.selectedSupplier.bidderId, // 供应商ID\r\n          })\r\n        ]);\r\n\r\n        // 处理评分详情请求结果\r\n        if (detailResult.status === 'fulfilled') {\r\n          const detailRes = detailResult.value;\r\n          if (detailRes.code === 200) {\r\n            this.factorDetailList = detailRes.data; // 评分详情列表\r\n            const factor = this.factorDetailList.find(item => item.bidderName === supplierName)?.evalExpertEvaluationDetails; // 当前供应商评分详情\r\n            this.resetRatingStateMap(); // 重置评分状态\r\n            if (factor) {\r\n              for (const item of factor) {\r\n                this.ratingStateMap[item.scoringMethodUitemId].reason = item.evaluationRemark; // 设置评分原因\r\n                this.ratingStateMap[item.scoringMethodUitemId].state = item.evaluationResult; // 设置评分结果\r\n              }\r\n            }\r\n          } else {\r\n            this.$message.warning(detailRes.msg); // 评分详情获取失败\r\n          }\r\n        } else {\r\n          console.error(\"获取评分详情失败:\", detailResult.reason);\r\n          this.$message.error(\"获取评分详情失败\"); // 评分详情请求异常\r\n        }\r\n\r\n        // 处理系统初验请求结果\r\n        if (checkResult.status === 'fulfilled') {\r\n          const checkRes = checkResult.value;\r\n          if (checkRes.code === 200) {\r\n            this.checkResult = checkRes.data; // 设置初验结果\r\n          } else {\r\n            console.error(\"获取系统初验结果失败:\", checkRes.msg);\r\n            this.$message.warning(\"获取系统初验结果失败\"); // 系统初验获取失败\r\n          }\r\n        } else {\r\n          console.error(\"系统初验请求失败:\", checkResult.reason);\r\n          this.$message.error(\"系统初验请求失败\"); // 系统初验请求异常\r\n        }\r\n      } catch (e) {\r\n        console.error(\"请求处理异常:\", e);\r\n        this.$message.error(\"获取供应商详情失败\"); // 异常提示\r\n      }\r\n      // 默认显示响应文件\r\n      this.showResponseFile();\r\n    },\r\n    /**\r\n     * 显示响应文件PDF\r\n     */\r\n    showResponseFile() {\r\n      if (!this.selectedSupplier || Object.keys(this.selectedSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n      this.activeButton = 'response'; // 设置当前激活按钮\r\n      this.isDoubleView = false; // 单文件模式\r\n      this.isShowProcurement = false; // 不显示采购文件\r\n      this.isShowResponse = true; // 显示响应文件\r\n      this.responsePdfUrl = this.projectFiles.file[this.selectedSupplier.bidderId]; // 设置响应文件PDF地址\r\n    },\r\n    /**\r\n     * 文件对比（双文件模式）\r\n     */\r\n    showFileContrast() {\r\n      if (!this.selectedSupplier || Object.keys(this.selectedSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n      this.activeButton = 'contrast'; // 设置当前激活按钮\r\n      this.isDoubleView = true; // 双文件模式\r\n      this.isShowProcurement = true; // 显示采购文件\r\n      this.isShowResponse = true; // 显示响应文件\r\n      this.responsePdfUrl = this.projectFiles.file[this.selectedSupplier.bidderId]; // 设置响应文件PDF地址\r\n    },\r\n    /**\r\n     * 点击评分项名称，跳转到对应PDF页码\r\n     * @param {Object} factorItem 当前评分因子项\r\n     */\r\n    handleShowFactorInfo(factorItem) {\r\n\t    // 检查PDF是否渲染完成\r\n\t    if (!this.canJumpToPage()) {\r\n\t\t    this.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t    return;\r\n\t    }\r\n\t\t\t\r\n      this.selectedFactorNode = factorItem; // 设置当前选中因子\r\n\r\n      // 如果只显示采购文件，使用采购文件页码信息\r\n      if (this.isShowProcurement && !this.isShowResponse) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        if (factorItem.jumpToPage) {\r\n          this.$refs.procurement.skipPage(factorItem.jumpToPage); // 采购文件跳页\r\n        } else if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 如果显示响应文件或对比模式，需要选择供应商\r\n      if (!this.supplierFactorPage || Object.keys(this.supplierFactorPage).length === 0) {\r\n        this.$message.warning(\"请先选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n\r\n      // 跳转到响应文件对应页码\r\n      if (this.isShowResponse && this.$refs.response) {\r\n\t      if (!this.responsePdfRendered) {\r\n\t\t      this.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n        this.$refs.response.skipPage(this.supplierFactorPage[this.selectedFactorNode.itemName]); // 响应文件跳页\r\n      }\r\n\r\n      // 跳转到采购文件对应页码\r\n      if (this.isShowProcurement && this.$refs.procurement) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        // 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n        if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n        } else {\r\n          // 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n          // 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n        }\r\n      }\r\n    },\r\n\t  \r\n\t  /**\r\n\t   * 检查是否可以跳转页面\r\n\t   * @returns {boolean} 是否可以跳转\r\n\t   */\r\n\t  canJumpToPage() {\r\n\t\t  // 如果只显示采购文件\r\n\t\t  if (this.isShowProcurement && !this.isShowResponse) {\r\n\t\t\t  return this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  // 如果只显示响应文件\r\n\t\t  if (this.isShowResponse && !this.isShowProcurement) {\r\n\t\t\t  return this.responsePdfRendered;\r\n\t\t  }\r\n\t\t  // 如果对比模式（两个都显示）\r\n\t\t  if (this.isShowResponse && this.isShowProcurement) {\r\n\t\t\t  return this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  return false;\r\n\t  },\r\n\t  /**\r\n\t   * 处理PDF渲染状态变化\r\n\t   * @param {boolean} isRendered 是否渲染完成\r\n\t   * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t   */\r\n\t  handlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t\t  if (pdfType === 'response') {\r\n\t\t\t  this.responsePdfRendered = isRendered;\r\n\t\t  } else if (pdfType === 'procurement') {\r\n\t\t\t  this.procurementPdfRendered = isRendered;\r\n\t\t  }\r\n\t\t  \r\n\t\t  if (isRendered) {\r\n\t\t\t  console.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t\t  }\r\n\t  },\r\n\t  \r\n    /**\r\n     * 提交评分并修改专家进度\r\n     */\r\n    async submit() {\r\n      // 先保存评分，如果保存失败则不继续提交\r\n      const saveResult = await this.saveTempRating();\r\n      if (!saveResult) {\r\n        // 保存失败，不继续提交流程\r\n        return;\r\n      }\r\n\r\n      const data = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项ID\r\n      };\r\n      try {\r\n        const response = await checkReviewSummary(data); // 检查评审汇总\r\n\r\n        if (response.code === 200) {\r\n          // 修改专家进度\r\n          const status = {\r\n            evalExpertScoreInfoId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\")).evalExpertScoreInfoId, // 专家评分信息ID\r\n            evalState: 1, // 进度状态\r\n          };\r\n          const res = await editEvalExpertScoreInfo(status); // 编辑专家评分状态\r\n          if (res.code === 200) {\r\n            this.$message.success(\"提交成功\"); // 提交成功提示\r\n          }\r\n          this.$emit(\"send\", \"two\"); // 发送事件\r\n        } else {\r\n          this.$message.warning(response.msg); // 提交失败提示\r\n        }\r\n      } catch (e) {\r\n        this.$message.error(\"提交失败\"); // 异常提示\r\n      }\r\n    },\r\n    /**\r\n     * 显示采购文件PDF\r\n     */\r\n    viewPurchasing() {\r\n      this.activeButton = 'procurement'; // 设置当前激活按钮\r\n      this.isDoubleView = false; // 单文件模式\r\n      this.isShowResponse = false; // 不显示响应文件\r\n      this.isShowProcurement = true; // 显示采购文件\r\n      // 右侧评分项显示为采购文件的评分项\r\n      let pageProcurementArr = []; // 采购文件评分项数组\r\n\r\n      for (let item in this.entDocProcurementPage){\r\n        pageProcurementArr.push({\r\n          itemName: item,\r\n          jumpToPage: this.entDocProcurementPage[item]\r\n        })\r\n      }\r\n\r\n      console.log(this.scoringMethod.uitems);\r\n      console.log(pageProcurementArr)\r\n      this.pageProcurement = [];\r\n      for (let i = 0; i < this.scoringMethod.uitems.length;i++){\r\n        for (let j = 0; j < pageProcurementArr.length;j++){\r\n          if (this.scoringMethod.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n            this.pageProcurement.push({...this.scoringMethod.uitems[i],...pageProcurementArr[j]});\r\n          }\r\n        }\r\n      }\r\n      console.log(this.pageProcurement)\r\n    },\r\n    /**\r\n     * 跳转到二次报价页面\r\n     */\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        zjhm: this.$route.query.zjhm, // 专家证件号码\r\n        scoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")), // 二次报价评分方法项ID\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query }); // 跳转页面\r\n    },\r\n    /**\r\n     * 跳转到询标页面\r\n     */\r\n    bidInquiry() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        zjhm: this.$route.query.zjhm, // 专家证件号码\r\n        scoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")), // 询标评分方法项ID\r\n      };\r\n      this.$router.push({ path: \"/bidInquiry\", query }); // 跳转页面\r\n    },\r\n    /**\r\n     * 获取因素对应页码（从本地缓存）\r\n     */\r\n    getFactorsPage() {\r\n      this.factorsPageMap = JSON.parse(localStorage.getItem(\"entDocResponsePage\")); // 获取因子页码映射\r\n    },\r\n    /**\r\n     * 初始化专家和本地数据，只在mounted时调用一次\r\n     */\r\n    initLocalData() {\r\n      try {\r\n        this.localExpertInfo = JSON.parse(localStorage.getItem(\"expertInfo\") || \"{}\"); // 获取本地专家信息\r\n        this.localEntDocResponsePage = JSON.parse(localStorage.getItem(\"entDocResponsePage\") || \"{}\"); // 获取本地响应文件页码\r\n        this.localFactorsPageMap = JSON.parse(localStorage.getItem(\"entDocResponsePage\") || \"{}\"); // 获取本地因子页码映射\r\n        this.expertInfo = this.localExpertInfo; // 设置专家信息\r\n        this.entDocResponsePage = this.localEntDocResponsePage; // 设置响应文件页码\r\n        this.factorsPageMap = this.localFactorsPageMap; // 设置因子页码映射\r\n        console.log(\"本地数据已初始化\", { expertInfo: this.expertInfo });\r\n      } catch (error) {\r\n        console.error(\"初始化本地数据失败:\", error);\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 初始化专家信息（用于响应专家信息更新）\r\n     */\r\n    initExpertInfo() {\r\n      try {\r\n        const expertInfoStr = localStorage.getItem(\"expertInfo\");\r\n        if (expertInfoStr) {\r\n          this.localExpertInfo = JSON.parse(expertInfoStr);\r\n          this.expertInfo = this.localExpertInfo;\r\n          console.log(\"专家信息已刷新\", this.expertInfo);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"刷新专家信息失败:\", error);\r\n      }\r\n    },\r\n    /**\r\n     * 页面初始化，加载供应商、评分方法、文件等（并发请求）\r\n     */\r\n    async initPage() {\r\n      this.initLocalData(); // 初始化本地数据\r\n      try {\r\n        // 并发获取供应商、评分方法、文件\r\n        const [supplierRes, approvalRes, filesRes] = await Promise.all([\r\n          supplierInfo({ projectId: this.$route.query.projectId }), // 获取供应商\r\n          approvalProcess(this.$route.query.projectId, this.expertInfo.resultId), // 获取评分方法\r\n          filesById(this.$route.query.projectId) // 获取项目文件\r\n        ]);\r\n        // 处理供应商\r\n        if (supplierRes.code === 200) {\r\n          this.supplierOptions = supplierRes.rows.filter(item => item.isAbandonedBid == 0); // 过滤未弃标供应商\r\n        } else {\r\n          this.$message.warning(supplierRes.msg); // 获取失败提示\r\n        }\r\n        // 处理评分方法\r\n        if (approvalRes.code === 200) {\r\n\t        this.attachmentsList = approvalRes.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n          this.scoringMethod = approvalRes.data.scoringMethodUinfo.scoringMethodItems.find(\r\n            item => item.scoringMethodItemId == this.$route.query.scoringMethodItemId\r\n          ); // 获取当前评分方法\r\n          localStorage.setItem(\"evalProjectEvaluationProcess\", JSON.stringify(this.scoringMethod.evalProjectEvaluationProcess)); // 缓存评分流程\r\n          this.ratingStateMap = this.scoringMethod.uitems.reduce((acc, item) => {\r\n            acc[item.entMethodItemId] = { state: null, reason: '' }; // 初始化评分状态\r\n            return acc;\r\n          }, {});\r\n        } else {\r\n          this.$message.warning(approvalRes.msg); // 获取失败提示\r\n        }\r\n        // 处理文件\r\n        if (filesRes.code === 200) {\r\n          this.projectFiles = filesRes.data; // 设置项目文件\r\n          if (this.projectFiles.tenderNoticeFilePath) {\r\n            this.procurementPdfUrl = this.projectFiles.tenderNoticeFilePath; // 设置采购文件PDF\r\n          }\r\n          // if (this.projectFiles.file) {\r\n          //   this.responsePdfUrl = this.projectFiles.file[0]; // 设置响应文件PDF\r\n          // }\r\n        } else {\r\n          this.$message.warning(filesRes.msg); // 获取失败提示\r\n        }\r\n      } catch (e) {\r\n        this.$message.error(\"页面初始化失败\"); // 异常提示\r\n      }\r\n    },\r\n\t  \r\n\t  downloadFile(item){\r\n\t\t  this.$download.zip(item.filePath,item.fileName);\r\n\t  },\r\n\t  \r\n\t  \r\n\t  // ========== 悬停相关 ==========\r\n\t  /**\r\n\t   * 显示评分项悬浮框\r\n\t   * @param {Object} factorItem 评分项对象\r\n\t   */\r\n\t  showFactorTooltip(factorItem) {\r\n\t\t  if (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\t\t  \r\n\t\t  // 清除之前的定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t  this.tooltipTimer = setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = factorItem;\r\n\t\t  }, 300); // 300ms延迟\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 隐藏评分项悬浮框\r\n\t   */\r\n\t  hideFactorTooltip() {\r\n\t\t  // 清除定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t  setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = null;\r\n\t\t  }, 100);\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t   */\r\n\t  clearTooltipTimer() {\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t  }\r\n  },\r\n  mounted() {\r\n\t  this.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\")); // 初始化采购文件页码信息\r\n    this.initPage(); // 页面挂载时初始化数据\r\n    this.getFactorsPage(); // 获取因子页码\r\n  },\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.compliance-main {\r\n  min-height: 57vh;\r\n  display: flex;\r\n}\r\n.compliance-left {\r\n  min-height: 57vh;\r\n  width: 79%;\r\n}\r\n.compliance-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.compliance-title-group {\r\n  display: flex;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333;\r\n}\r\n.compliance-title {\r\n  // nothing extra\r\n}\r\n.compliance-step-img-group {\r\n  display: grid;\r\n  justify-items: center;\r\n  position: relative;\r\n  bottom: -30px;\r\n}\r\n.compliance-step-text {\r\n  font-size: 12px;\r\n}\r\n.compliance-step-img {\r\n  width: 80px;\r\n  height: 30px;\r\n  margin-right: 20px;\r\n}\r\n.compliance-header-btns {\r\n  text-align: right;\r\n}\r\n.compliance-header-btns-bottom {\r\n  margin-top: 20px;\r\n}\r\n.compliance-blue-btn {\r\n  background-color: #176ADB !important;\r\n  color: #fff !important;\r\n  border: 1px solid #176ADB !important;\r\n}\r\n.compliance-blue-btn-active {\r\n  background-color: #FF6B35 !important;\r\n  color: #fff !important;\r\n  border: 1px solid #FF6B35 !important;\r\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.compliance-pdf-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  height: 82%;\r\n}\r\n.compliance-pdf {\r\n  width: 49%;\r\n}\r\n.compliance-pdf-border-right {\r\n  border-right: 1px solid #176ADB;\r\n}\r\n.compliance-pdf-border-left {\r\n  border-left: 1px solid #176ADB;\r\n}\r\n.compliance-divider {\r\n  min-height: 57vh;\r\n  width: 1%;\r\n  background-color: #F5F5F5;\r\n}\r\n.compliance-right {\r\n  min-height: 57vh;\r\n  width: 20%;\r\n}\r\n.compliance-select-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.compliance-select {\r\n  width: 100%;\r\n}\r\n.compliance-factors-group {\r\n  padding: 15px 20px;\r\n}\r\n.compliance-factor-title-group {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  text-align: left;\r\n  width: 98%;\r\n}\r\n.compliance-factor-title {\r\n  cursor: pointer;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 16px;\r\n  color: #333;\r\n  letter-spacing: 0;\r\n}\r\n.compliance-factor-radio-group {\r\n  display: flex;\r\n  width: 100%;\r\n  justify-content: flex-end;\r\n}\r\n.compliance-factor-divider {\r\n  height: 1px;\r\n  background-color: #DCDFE6;\r\n  margin-top: 10px;\r\n}\r\n.compliance-submit-group {\r\n  display: flex;\r\n  margin: 32px 0;\r\n  justify-content: space-evenly;\r\n}\r\n.compliance-submit-btn {\r\n  background-color: #176ADB;\r\n}\r\n.compliance-review-content {\r\n  text-align: left;\r\n  font-size: 14px;\r\n}\r\n.compliance-review-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 15px;\r\n  color: #176ADB;\r\n  letter-spacing: 0;\r\n}\r\n.compliance-review-html {\r\n  padding: 6px 30px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n\t  transition: all 0.3s ease;\r\n\t  padding: 4px 8px;\r\n\t  border-radius: 4px;\r\n\t  \r\n\t  &:hover {\r\n\t\t  background-color: #f0f8ff;\r\n\t\t  color: #176ADB;\r\n\t\t  transform: translateX(2px);\r\n\t  }\r\n  }\r\n}\r\n.item-button {\r\n  border: 1px solid #979797;\r\n  width: 150px;\r\n  height: 36px;\r\n  margin: 0 10px;\r\n  font-weight: 700;\r\n  font-size: 17px;\r\n  border-radius: 6px;\r\n  color: #333;\r\n  &:hover {\r\n    color: #333;\r\n  }\r\n}\r\n.item-button-little {\r\n  width: 124px;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #fff;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n\t.fileItem {\r\n\t\ttransition: all 0.3s ease;\r\n\t\t&:hover {\r\n\t\t\ttransform: translateY(-2px);\r\n\t\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\t\t}\r\n\t\t\r\n\t\t::v-deep .el-card__body {\r\n\t\t\tpadding: 0;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\t\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\t\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\t\t\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\t\t\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\t\t\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n\r\n</style>\r\n"]}]}