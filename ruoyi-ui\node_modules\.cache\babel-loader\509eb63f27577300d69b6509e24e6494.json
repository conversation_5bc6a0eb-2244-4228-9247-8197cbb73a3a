{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\technical\\one.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\technical\\one.vue", "mtime": 1753924220136}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\babel.config.js", "mtime": 1750995480034}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_review", "require", "_detail", "_expertStatus", "data", "options", "scoringSystem", "selectNode", "supplier", "selectSupplier", "expertInfo", "defaultRatingArray", "file", "responseShow", "procurementShow", "double", "factorList", "entDocResponsePage", "factorsPage", "bidderFactor", "responsePdf", "procurementPdf", "currentMaxScore", "activeButton", "entDocProcurementPage", "pageProcurement", "attachmentsList", "responsePdfRendered", "procurementPdfRendered", "srcList", "hoveredFactorNode", "tooltipTimer", "methods", "only<PERSON><PERSON><PERSON>", "event", "charCode", "which", "keyCode", "preventDefault", "handleScoreInput", "itemId", "value", "cleanValue", "replace", "parts", "split", "length", "slice", "join", "substring", "state", "validateScore", "init", "_this", "JSON", "parse", "localStorage", "getItem", "supplierInfo", "projectId", "$route", "query", "then", "response", "code", "rows", "filter", "item", "isAbandonedBid", "$message", "warning", "msg", "approvalProcess", "resultId", "busiTenderNotice", "attachments", "fileType", "scoringMethodUinfo", "scoringMethodItems", "find", "scoringMethodItemId", "setItem", "stringify", "evalProjectEvaluationProcess", "uitems", "reduce", "acc", "_", "index", "entMethodItemId", "reason", "console", "log", "$messgae", "filesById", "tenderNoticeFilePath", "undefined", "initExpertInfo", "itemString", "warn", "error", "handleChange", "_this2", "Object", "keys", "tmpSave", "bidderName", "bidderId", "expertResultId", "getDetailByPsxx", "factor", "evalExpertEvaluationDetails", "map", "scoringMethodUitemId", "evaluationRemark", "evaluationResult", "for<PERSON>ach", "key", "showResponseFile", "inputValue", "parseFloat", "currentItem", "maxScore", "scoreLevel", "scoreLevels", "trim", "isNaN", "Math", "max", "apply", "_toConsumableArray2", "default", "score", "concat", "fileContrast", "showInfo", "canJumpToPage", "jumpToPage", "$refs", "procurement", "skipPage", "itemName", "initDefaultRatingArray", "_this3", "validateAllRatings", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "getMaxScore", "level", "includes", "toString", "err", "e", "f", "_this4", "Promise", "resolve", "success", "ratingArray", "push", "entId", "scoringFactors", "catch", "save", "_this5", "generatingSavedData", "submit", "_this6", "saveResult", "checkReviewSummary", "status", "evalExpertScoreInfoId", "evalState", "editEvalExpertScoreInfo", "res", "$emit", "viewPurchasing", "pageProcurementArr", "i", "j", "_objectSpread2", "jumpToProcurementPage", "handlePdfRenderStatusChange", "isRendered", "pdfType", "secondOffer", "zjhm", "$router", "path", "bidInquiry", "getFactorsPage", "downloadFile", "$download", "zip", "filePath", "fileName", "showFactorTooltip", "factorItem", "_this7", "itemRemark", "clearTimeout", "setTimeout", "hideFactorTooltip", "_this8", "clearTooltipTimer", "mounted", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/expertReview/technical/one.vue"], "sourcesContent": ["<template>\r\n  <div class=\"technical-review-container\">\r\n    <div class=\"main-content\">\r\n      <div class=\"header-section\">\r\n        <div class=\"title-section\">\r\n          <div class=\"main-title\">技术标评审</div>\r\n          <div class=\"help-section\">\r\n            <div class=\"help-text\">该页面操作说明</div>\r\n            <el-image class=\"help-image\" :src=\"srcList[0]\" :preview-src-list=\"srcList\">\r\n            </el-image>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 文件列表 -->\r\n        <div class=\"file-list-container\">\r\n          <div class=\"file-list-title\">响应文件附件下载</div>\r\n          <el-card\r\n            v-for=\"(item, index) in attachmentsList\"\r\n            :key=\"index\"\r\n            class=\"file-item\"\r\n            shadow=\"hover\"\r\n            @click.native=\"downloadFile(item)\"\r\n          >\r\n            <div class=\"file-item-content\">\r\n              <i class=\"el-icon-document file-icon\"></i>\r\n              <span class=\"file-name\">{{ item.fileName }}</span>\r\n              <i class=\"el-icon-download download-icon\"></i>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <div class=\"action-buttons\">\r\n          <el-button class=\"item-button\" @click=\"bidInquiry\">询标</el-button>\r\n          <el-button class=\"item-button\" v-if=\"expertInfo.expertLeader==1\" @click=\"secondOffer\">发起二次报价</el-button>\r\n          <div class=\"button-group\">\r\n\t          <el-button\r\n\t\t          :class=\"['item-button', activeButton === 'procurement' ? 'technical-blue-btn-active' : 'technical-blue-btn']\"\r\n\t\t          @click=\"viewPurchasing\">采购文件</el-button>\r\n\t          \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'response' ? 'technical-blue-btn-active' : 'technical-blue-btn']\"\r\n              @click=\"showResponseFile()\">响应文件</el-button>\r\n            \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'contrast' ? 'technical-blue-btn-active' : 'technical-blue-btn']\"\r\n              @click=\"fileContrast\">对比</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"content-section\">\r\n        \r\n\r\n        <!-- PDF预览区域 - 保持原始尺寸 -->\r\n        <div class=\"pdf-preview-area\">\r\n          <div\r\n            v-show=\"procurementShow\"\r\n            class=\"pdf-panel procurement-panel\"\r\n            :class=\"{ 'border-left': double }\"\r\n          >\r\n<!--            <pdfView-->\r\n<!--              ref=\"procurement\"-->\r\n<!--              :pdfurl=\"procurementPdf\"-->\r\n<!--              :uni_key=\"'procurement'\"-->\r\n<!--            ></pdfView>-->\r\n\t          \r\n\t          <PdfViewImproved ref=\"procurement\"  :pdfurl=\"procurementPdf\"  :page-height=\"800\" :buffer-size=\"2\" @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"/>\r\n          </div>\r\n\t        \r\n\t        <div\r\n\t\t        v-show=\"responseShow\"\r\n\t\t        class=\"pdf-panel response-panel\"\r\n\t\t        :class=\"{ 'border-right': double }\"\r\n\t        >\r\n\t\t        <!--            <pdfView-->\r\n\t\t        <!--              ref=\"response\"-->\r\n\t\t        <!--              :pdfurl=\"responsePdf\"-->\r\n\t\t        <!--              :uni_key=\"'response'\"-->\r\n\t\t        <!--            ></pdfView>-->\r\n\t\t        \r\n\t\t        <PdfViewImproved ref=\"response\"  :pdfurl=\"responsePdf\"  :page-height=\"800\" :buffer-size=\"2\" @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"/>\r\n\t        </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"divider\">\r\n    </div>\r\n    <div class=\"sidebar\">\r\n      <div class=\"sidebar-header\">\r\n        <el-select\r\n          class=\"supplier-select\"\r\n          v-model=\"supplier\"\r\n          placeholder=\"请选择供应商\"\r\n          @change=\"handleChange\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in options\"\r\n            :key=\"item.bidderName\"\r\n            :label=\"item.bidderName\"\r\n            :value=\"item.bidderName\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </div>\r\n\t    \r\n      <div class=\"sidebar-content\" >\r\n        <!-- 响应文件评分项显示 -->\r\n\t      <template v-if=\"responseShow || double\">\r\n\t\t      <!-- PDF渲染状态提示 -->\r\n\t\t      <div v-if=\"!responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t\t      <i class=\"el-icon-loading\"></i>\r\n\t\t\t      <span>响应文件正在渲染中，请稍候...</span>\r\n\t\t      </div>\r\n\t\t      <div v-else class=\"render-status-tip success\">\r\n\t\t\t      <i class=\"el-icon-success\"></i>\r\n\t\t\t      <span>响应文件渲染完成，可以点击跳转</span>\r\n\t\t      </div>\r\n\t\t      \r\n\t\t      <div\r\n\t\t\t      v-for=\"(item, index) in scoringSystem.uitems\"\r\n\t\t\t      :key=\"'response-' + index\"\r\n\t\t\t      class=\"factor-item\"\r\n\t\t\t      @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t      @mouseleave=\"hideFactorTooltip\"\r\n\t\t      >\r\n\t\t\t      <!-- 悬浮框 -->\r\n\t\t\t      <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t           class=\"factor-tooltip\"\r\n\t\t\t           @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t      <div class=\"tooltip-header\">\r\n\t\t\t\t\t      <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t      <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t      </div>\r\n\t\t\t\t      <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t      </div>\r\n\t\t\t      <div>\r\n\t\t\t\t      <div class=\"factors\">\r\n\t\t\t\t\t      <div class=\"factor-header\">\r\n\t\t\t\t\t\t      <div class=\"factor-name factor-title\"\r\n\t\t\t\t\t\t           :class=\"{ 'disabled': !canJumpToPage() }\"\r\n\t\t\t\t\t\t           @click=\"showInfo(item)\">\r\n\t\t\t\t\t\t\t      {{ item.itemName }}\r\n\t\t\t\t\t\t\t      <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t      \r\n\t\t\t\t\t\t\t      <span class=\"max-score\">\r\n\t\t\t\t\t\t\t        最高{{ getMaxScore(item) }}分\r\n\t\t\t\t\t\t\t      </span>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t\t      <div class=\"factor-input\">\r\n\t\t\t\t\t\t      <div v-if=\"!(item.scoreLevel.length == 0 || item.scoreLevel == null || item.scoreLevel == undefined)\">\r\n\t\t\t\t\t\t\t      <el-radio\r\n\t\t\t\t\t\t\t\t      v-for=\"(score,index) in item.scoreLevel.split(',')\"\r\n\t\t\t\t\t\t\t\t      :key=\"index\"\r\n\t\t\t\t\t\t\t\t      v-model=\"defaultRatingArray[item.entMethodItemId].state\"\r\n\t\t\t\t\t\t\t\t      :label=\"score\"\r\n\t\t\t\t\t\t\t      ><span class=\"score-value\">{{ score }}</span></el-radio>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t\t      <div v-else>\r\n\t\t\t\t\t\t\t      <el-input\r\n\t\t\t\t\t\t\t\t      placeholder=\"请输入分数\"\r\n\t\t\t\t\t\t\t\t      v-model=\"defaultRatingArray[item.entMethodItemId].state\"\r\n\t\t\t\t\t\t\t\t      @input=\"handleScoreInput(item.entMethodItemId, $event)\"\r\n\t\t\t\t\t\t\t\t      @keypress=\"onlyNumber\"\r\n\t\t\t\t\t\t\t\t      type=\"number\"\r\n\t\t\t\t\t\t\t\t      step=\"1\"\r\n\t\t\t\t\t\t\t\t      min=\"0\"\r\n\t\t\t\t\t\t\t      ></el-input>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t      \r\n\t\t\t\t      </div>\r\n\t\t\t      </div>\r\n\t\t      </div>\r\n\t      </template>\r\n\t      \r\n\t      <template v-else-if=\"procurementShow\" >\r\n\t\t      \r\n\t\t      <!-- PDF渲染状态提示 -->\r\n          <div v-if=\"!procurementPdfRendered\" class=\"render-status-tip\">\r\n            <i class=\"el-icon-loading\"></i>\r\n            <span>采购文件正在渲染中，请稍候...</span>\r\n          </div>\r\n          <div v-else class=\"render-status-tip success\">\r\n            <i class=\"el-icon-success\"></i>\r\n            <span>采购文件渲染完成，可以点击跳转</span>\r\n          </div>\r\n\t\t      \r\n\t\t      <!-- 采购文件评分项显示 -->\r\n\t\t      <div\r\n\t\t\t      v-for=\"(item, index) in pageProcurement\"\r\n\t\t\t      :key=\"'procurement-' + index\"\r\n\t\t\t      class=\"factor-item\"\r\n\t\t\t      @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t      @mouseleave=\"hideFactorTooltip\"\r\n\t\t      >\r\n\t\t\t      <!-- 悬浮框 -->\r\n\t\t\t      <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t           class=\"factor-tooltip\"\r\n\t\t\t           @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t      <div class=\"tooltip-header\">\r\n\t\t\t\t\t      <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t      <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t      </div>\r\n\t\t\t\t      <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t      </div>\r\n\t\t\t      <div>\r\n\t\t\t\t      <div class=\"factors\">\r\n\t\t\t\t\t      <div class=\"factor-header\">\r\n\t\t\t\t\t\t      <div class=\"factor-name factor-title\"\r\n\t\t\t\t\t\t           :class=\"{ 'disabled': !canJumpToPage() }\"\r\n\t\t\t\t\t\t           @click=\"jumpToProcurementPage(item)\">\r\n\t\t\t\t\t\t\t      {{ item.itemName }}\r\n\t\t\t\t\t\t\t      \r\n\t\t\t\t\t\t\t      <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t      </div>\r\n\t\t\t      </div>\r\n\t\t      </div>\r\n\t      </template>\r\n\t      \r\n        <div class=\"submit-section\">\r\n          <!-- <div><el-button\r\n              class=\"item-button-little\"\r\n              style=\"background-color:#F5F5F5;color:#176ADB\"\r\n              @click=\"save\"\r\n            >保存</el-button></div> -->\r\n          <div><el-button\r\n              class=\"item-button-little primary-btn\"\r\n              @click=\"submit\"\r\n            >提交</el-button></div>\r\n        </div>\r\n\r\n        <div class=\"review-content\">\r\n          <div class=\"review-title\">评审内容：</div>\r\n          <div class=\"review-text\" v-html=\"selectNode.itemRemark\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  supplierInfo,\r\n  approvalProcess,\r\n  scoringFactors,\r\n  checkReviewSummary,\r\n  filesById,\r\n} from \"@/api/expert/review\";\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\";\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      options: [],\r\n      scoringSystem: [],\r\n      selectNode: {},\r\n      supplier: \"\",\r\n      selectSupplier: {},\r\n      expertInfo: {},\r\n      defaultRatingArray: {},\r\n      file: {},\r\n      responseShow: false,\r\n      procurementShow: false,\r\n      double: false,\r\n      factorList: [],\r\n      entDocResponsePage: {},\r\n      factorsPage: {},\r\n      bidderFactor: {},\r\n\r\n      responsePdf: null,\r\n      procurementPdf: null,\r\n      currentMaxScore: null,\r\n\r\n      // 按钮状态管理\r\n      activeButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n      // 采购文件相关数据\r\n      entDocProcurementPage: {}, // 采购文件页码信息\r\n      pageProcurement: [], // 采购文件的评分项\r\n\t    attachmentsList:[], // 文件列表\r\n\t    \r\n\t    // PDF渲染状态管理\r\n\t    responsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n\t    procurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n      srcList: [\"/evalution/help.jpg\"],\r\n\t    \r\n\t    // 悬停状态管理\r\n\t    hoveredFactorNode: null, // 悬停时的评分项\r\n\t    tooltipTimer: null, // 悬浮框显示定时器\r\n    };\r\n  },\r\n  methods: {\r\n    /**\r\n     * 限制输入框只能输入数字和小数点\r\n     * @param {Event} event - 键盘事件\r\n     */\r\n    onlyNumber(event) {\r\n      // 获取按键的字符码\r\n      const charCode = event.which || event.keyCode;\r\n\r\n      // 允许的字符：数字(48-57)、小数点(46)、退格(8)、删除(46)、Tab(9)、Enter(13)、方向键(37-40)\r\n      if (\r\n        (charCode >= 48 && charCode <= 57) || // 数字 0-9\r\n        charCode === 46 || // 小数点\r\n        charCode === 8 ||  // 退格键\r\n        charCode === 9 ||  // Tab键\r\n        charCode === 13 || // Enter键\r\n        (charCode >= 37 && charCode <= 40) // 方向键\r\n      ) {\r\n        return true;\r\n      }\r\n\r\n      // 阻止其他字符输入\r\n      event.preventDefault();\r\n      return false;\r\n    },\r\n\r\n    /**\r\n     * 处理分数输入，确保只能输入有效的数字\r\n     * @param {string} itemId - 评估项ID\r\n     * @param {string} value - 输入值\r\n     */\r\n    handleScoreInput(itemId, value) {\r\n      // 移除非数字字符（保留小数点）\r\n      let cleanValue = value.replace(/[^\\d.]/g, '');\r\n\r\n      // 确保只有一个小数点\r\n      const parts = cleanValue.split('.');\r\n      if (parts.length > 2) {\r\n        cleanValue = parts[0] + '.' + parts.slice(1).join('');\r\n      }\r\n\r\n      // 限制小数点后最多2位\r\n      if (parts.length === 2 && parts[1].length > 2) {\r\n        cleanValue = parts[0] + '.' + parts[1].substring(0, 2);\r\n      }\r\n\r\n      // 更新值\r\n      this.defaultRatingArray[itemId].state = cleanValue;\r\n\r\n      // 调用原有的验证方法\r\n      this.validateScore(itemId, cleanValue);\r\n    },\r\n\r\n    init() {\r\n      const expertInfo = JSON.parse(localStorage.getItem(\"expertInfo\"));\r\n      this.entDocResponsePage = JSON.parse(\r\n        localStorage.getItem(\"entDocResponsePage\")\r\n      );\r\n      // 初始化采购文件页码信息\r\n      this.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\"));\r\n      supplierInfo({ projectId: this.$route.query.projectId }).then(\r\n        (response) => {\r\n          if (response.code == 200) {\r\n            this.options = response.rows.filter(item => item.isAbandonedBid == 0);\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        }\r\n      );\r\n      approvalProcess(this.$route.query.projectId, expertInfo.resultId).then(\r\n        (response) => {\r\n          if (response.code == 200) {\r\n\t\t\t\t\t\t// 文件列表\r\n\t          this.attachmentsList = response.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n\t\t\t\t\t\t\r\n            this.scoringSystem =\r\n              response.data.scoringMethodUinfo.scoringMethodItems.find(\r\n                (item) => {\r\n                  return (\r\n                    item.scoringMethodItemId ==\r\n                    this.$route.query.scoringMethodItemId\r\n                  );\r\n                }\r\n              );\r\n            localStorage.setItem(\r\n              \"evalProjectEvaluationProcess\",\r\n              JSON.stringify(this.scoringSystem.evalProjectEvaluationProcess)\r\n            );\r\n            // TODO 首先生成长度为this.scoringSystem.length的数组，结构为{state：false，reason：“”}\r\n            this.defaultRatingArray = this.scoringSystem.uitems.reduce(\r\n              (acc, _, index) => {\r\n                acc[this.scoringSystem.uitems[index].entMethodItemId] = {\r\n                  state: null,\r\n                  reason: \"\",\r\n                };\r\n                console.log(\"scoringSystem\", this.scoringSystem);\r\n                return acc;\r\n              },\r\n              {}\r\n            );\r\n          } else {\r\n            this.$messgae.warning(response.msg);\r\n          }\r\n        }\r\n      );\r\n      filesById(this.$route.query.projectId).then((response) => {\r\n        if (response.code == 200) {\r\n          this.file = response.data;\r\n          if (this.file.tenderNoticeFilePath != undefined) {\r\n            this.procurementPdf = this.file.tenderNoticeFilePath;\r\n          }\r\n          // if (this.file.file != undefined) {\r\n          //   this.responsePdf = this.file.file[0];\r\n          // }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      // 初始化专家信息\r\n      this.initExpertInfo();\r\n    },\r\n\r\n    /**\r\n     * 初始化专家信息\r\n     */\r\n    initExpertInfo() {\r\n      try {\r\n        const itemString = localStorage.getItem(\"expertInfo\");\r\n        if (itemString) {\r\n          this.expertInfo = JSON.parse(itemString);\r\n          console.log(\"专家信息已初始化\", this.expertInfo);\r\n        } else {\r\n          console.warn(\"localStorage中未找到expertInfo\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"初始化专家信息失败:\", error);\r\n      }\r\n    },\r\n\r\n    handleChange(value) {\r\n      if(Object.keys(this.selectSupplier).length != 0){\r\n        this.tmpSave();\r\n      }\r\n      this.selectSupplier = this.options.find((item) => {\r\n        return item.bidderName == value;\r\n      });\r\n\r\n      // 根据bidderid获取供应商因素及其对应页码\r\n      this.bidderFactor = this.factorsPage[this.selectSupplier.bidderId];\r\n\r\n      const data = {\r\n        expertResultId: this.expertInfo.resultId,\r\n        projectId: this.$route.query.projectId,\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n      };\r\n      getDetailByPsxx(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.factorList = response.data;\r\n          const factor = this.factorList.find((item) => {\r\n            return item.bidderName == value;\r\n          }).evalExpertEvaluationDetails;\r\n          if (factor != null) {\r\n            factor.map((item) => {\r\n              this.defaultRatingArray[item.scoringMethodUitemId].reason =\r\n                item.evaluationRemark;\r\n              this.defaultRatingArray[item.scoringMethodUitemId].state =\r\n                item.evaluationResult;\r\n            });\r\n          } else {\r\n            Object.keys(this.defaultRatingArray).forEach((key) => {\r\n              this.defaultRatingArray[key].state = null;\r\n              this.defaultRatingArray[key].reason = \"\";\r\n            });\r\n          }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      this.showResponseFile();\r\n      // 重置最大分数值\r\n      // this.currentMaxScore = null;\r\n    },\r\n\t  \r\n\t  \r\n    validateScore(itemId, event) {\r\n      const inputValue = parseFloat(event);\r\n      console.log(\"inputValue\", inputValue);\r\n      \r\n      // 获取当前评分项的最大分值\r\n      const currentItem = this.scoringSystem.uitems.find(item => item.entMethodItemId === itemId);\r\n      let maxScore = null;\r\n      \r\n      if (currentItem) {\r\n        // 如果有分数挡位，使用挡位中的最大值\r\n        if (currentItem.scoreLevel && currentItem.scoreLevel.length > 0) {\r\n          const scoreLevels = currentItem.scoreLevel.split(',').map(item => parseFloat(item.trim())).filter(item => !isNaN(item));\r\n          if (scoreLevels.length > 0) {\r\n            maxScore = Math.max(...scoreLevels);\r\n          }\r\n        } else {\r\n          // 否则使用配置的最大分值\r\n          maxScore = parseFloat(currentItem.score);\r\n        }\r\n      }\r\n      \r\n      console.log(\"maxScore\", maxScore);\r\n\r\n      if (!isNaN(inputValue) && maxScore !== null) {\r\n        if (inputValue > maxScore) {\r\n          this.$message.warning(`输入分数不能超过${maxScore}分，请重新输入`);\r\n          // 将输入值限制为最大分数值\r\n          this.defaultRatingArray[itemId].state = \"\";\r\n        } else if (inputValue < 0) {\r\n          this.$message.warning(\"输入分数不能小于0分\");\r\n          this.defaultRatingArray[itemId].state = \"\";\r\n        }\r\n      }\r\n    },\r\n    showResponseFile() {\r\n      if (Object.keys(this.selectSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        this.activeButton = 'response'; // 设置当前激活按钮\r\n        this.double = false;\r\n        this.procurementShow = false;\r\n        this.responseShow = true;\r\n        this.responsePdf = this.file.file[this.selectSupplier.bidderId];\r\n      }\r\n    },\r\n    fileContrast() {\r\n      if (Object.keys(this.selectSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        this.activeButton = 'contrast'; // 设置当前激活按钮\r\n        this.double = true;\r\n        this.procurementShow = true;\r\n        this.responseShow = true;\r\n        this.responsePdf = this.file.file[this.selectSupplier.bidderId];\r\n      }\r\n    },\r\n    showInfo(item) {\r\n\t    // 检查PDF是否渲染完成\r\n\t    if (!this.canJumpToPage()) {\r\n\t\t    this.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t    return;\r\n\t    }\r\n\t\t\t\r\n      this.selectNode = item;\r\n\r\n      // 如果只显示采购文件，使用采购文件页码信息\r\n      if (this.procurementShow && !this.responseShow) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        if (item.jumpToPage) {\r\n          this.$refs.procurement.skipPage(item.jumpToPage);\r\n        } else if (this.entDocProcurementPage && this.entDocProcurementPage[item.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[item.itemName]);\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 如果显示响应文件或对比模式，需要选择供应商\r\n      if (Object.keys(this.bidderFactor).length != 0) {\r\n        // 跳转到响应文件对应页码\r\n        if (this.responseShow && this.$refs.response) {\r\n\t        if (!this.responsePdfRendered) {\r\n\t\t        this.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t        return;\r\n\t        }\r\n          this.$refs.response.skipPage(\r\n            this.bidderFactor[this.selectNode.itemName]\r\n          );\r\n        }\r\n\r\n        // 跳转到采购文件对应页码\r\n        if (this.procurementShow && this.$refs.procurement) {\r\n\t        if (!this.procurementPdfRendered) {\r\n\t\t        this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t        return;\r\n\t        }\r\n\t\t\t\t\t\r\n          // 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n          if (this.entDocProcurementPage && this.entDocProcurementPage[item.itemName]) {\r\n            this.$refs.procurement.skipPage(this.entDocProcurementPage[item.itemName]);\r\n          } else {\r\n            // 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n            // 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n          }\r\n        }\r\n\r\n        // 获取当前项目的最大分数值，假设scoreLevel中第一个值为最大值（可根据实际规则调整）\r\n        // const maxScore = item.score;\r\n        // console.log(\"此项目最大分值是：\"+maxScore);\r\n        // this.currentMaxScore = maxScore; // 将最大分数值存储到实例变量中，方便后续校验使用\r\n      } else {\r\n        this.$message.warning(\"请先选择供应商\");\r\n      }\r\n    },\r\n    initDefaultRatingArray(){\r\n      Object.keys(this.defaultRatingArray).forEach((key) => {\r\n        this.defaultRatingArray[key].state = null;\r\n        this.defaultRatingArray[key].reason = \"\";\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 校验所有评分项是否填写完整\r\n     * @returns {boolean} 是否全部填写\r\n     */\r\n    validateAllRatings() {\r\n      for (const item of this.scoringSystem.uitems) {\r\n        const state = this.defaultRatingArray[item.entMethodItemId].state;\r\n\r\n        // 评分结果未填写\r\n        if (state === null || state === '' || state === undefined) {\r\n          // this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n          return true;\r\n        }\r\n\r\n        // 对于分数评分，检查是否为有效数值\r\n        if (!item.scoreLevel || item.scoreLevel.length === 0 || item.scoreLevel === null || item.scoreLevel === undefined) {\r\n          const score = parseFloat(state);\r\n          if (isNaN(score) || score < 0) {\r\n            this.$message.warning(`${item.itemName}的评分必须是有效的数值且不能小于0`);\r\n            return false;\r\n          }\r\n          // 检查分数是否超过最大值\r\n          const maxScore = this.getMaxScore(item);\r\n          if (score > maxScore) {\r\n            this.$message.warning(`${item.itemName}的评分不能超过${maxScore}分`);\r\n            return false;\r\n          }\r\n        } else {\r\n          // 对于有挡位的评分，检查是否在允许的挡位范围内\r\n          const scoreLevels = item.scoreLevel.split(',').map(level => level.trim());\r\n          if (!scoreLevels.includes(state.toString())) {\r\n            this.$message.warning(`${item.itemName}的评分必须选择指定的挡位：${item.scoreLevel}`);\r\n            return false;\r\n          }\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n\r\n    tmpSave(){\r\n      console.log(\"-------开始保存评审结果----------------\");\r\n\r\n      // 先校验所有评分项是否填写完整\r\n      if (!this.validateAllRatings()) {\r\n        return Promise.resolve({ code: 0, success: false }); // 校验失败\r\n      }\r\n\r\n      var ratingArray = JSON.parse(JSON.stringify(this.defaultRatingArray));\r\n\r\n      var data = [];\r\n      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        const item = this.scoringSystem.uitems[index];\r\n        const itemId = item.entMethodItemId;\r\n        // 获取当前项对应的评分结果\r\n        const evaluationResult = ratingArray[itemId].state;\r\n        if (evaluationResult === null || evaluationResult === \"\") {\r\n          // 如果评分结果为空，则不保存此条信息\r\n          console.log(\"-------评分结果为空，不保存此条信息----------------\");\r\n          continue;\r\n        }\r\n          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n          const evaluationRemark = ratingArray[itemId].reason || \"\";\r\n          data.push({\r\n            scoringMethodUitemId: itemId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            entId: this.selectSupplier.bidderId,\r\n            evaluationResult: evaluationResult,\r\n            evaluationRemark: evaluationRemark\r\n          });\r\n        }\r\n        if(data.length>0){\r\n            console.log(\"-------开始后台保存评审结果----------------\");\r\n            return scoringFactors(data).then((response) => {\r\n              console.log(response.msg);\r\n              if (response.code == 200) {\r\n                this.$message.success(\"保存成功\");\r\n                return { code: 200, success: true };\r\n              } else {\r\n                this.$message.warning(response.msg);\r\n                return { code: response.code, success: false };\r\n              }\r\n            }).catch((error) => {\r\n              this.$message.error(\"保存失败\");\r\n              return { code: 0, success: false };\r\n            });\r\n        }else{\r\n          return Promise.resolve({ code: 200, success: true }); // 没有数据需要保存时也返回成功\r\n        }\r\n    },\r\n    save() {\r\n      if (this.supplier == \"\") {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        //const data = this.generatingSavedData();\r\n        var data = [];\r\n        for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n          const item = this.scoringSystem.uitems[index];\r\n          const itemId = item.entMethodItemId;\r\n          // 获取当前项对应的评分结果\r\n          const evaluationResult = this.defaultRatingArray[itemId].state;\r\n          if (evaluationResult === null || evaluationResult === \"\") {\r\n            // 如果评分结果为空，弹出提示，提示内容包含该项的itemName\r\n            this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n            return; // 直接返回，不再继续构建数据，等待用户填写完整\r\n          }\r\n          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n          const evaluationRemark = this.defaultRatingArray[itemId].reason || \"\";\r\n          data.push({\r\n            scoringMethodUitemId: itemId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            entId: this.selectSupplier.bidderId,\r\n            evaluationResult: evaluationResult,\r\n            evaluationRemark: evaluationRemark\r\n          });\r\n        }\r\n\r\n        scoringFactors(data).then((response) => {\r\n          if (response.code == 200) {\r\n            this.$message.success(response.msg);\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 生成保存数据\r\n    generatingSavedData() {\r\n      var data = [];\r\n      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        const item = this.scoringSystem.uitems[index];\r\n        const itemId = item.entMethodItemId;\r\n        // 获取当前项对应的评分结果\r\n        const evaluationResult = this.defaultRatingArray[itemId].state;\r\n        if (evaluationResult === null || evaluationResult === \"\") {\r\n          // 如果评分结果为空，弹出提示，提示内容包含该项的itemName\r\n          this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n          return; // 直接返回，不再继续构建数据，等待用户填写完整\r\n        }\r\n        // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n        const evaluationRemark = this.defaultRatingArray[itemId].reason || \"\";\r\n        data.push({\r\n          scoringMethodUitemId: itemId,\r\n          expertResultId: this.expertInfo.resultId,\r\n          entId: this.selectSupplier.bidderId,\r\n          evaluationResult: evaluationResult,\r\n          evaluationRemark: evaluationRemark\r\n        });\r\n      }\r\n\r\n      /*for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        data.push({\r\n          scoringMethodUitemId:\r\n            this.scoringSystem.uitems[index].entMethodItemId,\r\n          expertResultId: this.expertInfo.resultId,\r\n          entId: this.selectSupplier.bidderId,\r\n          evaluationResult:\r\n            this.defaultRatingArray[\r\n              this.scoringSystem.uitems[index].entMethodItemId\r\n            ].state,\r\n          evaluationRemark:\r\n            this.defaultRatingArray[\r\n              this.scoringSystem.uitems[index].entMethodItemId\r\n            ].reason,\r\n        });\r\n      }*/\r\n      return data;\r\n    },\r\n    submit() {\r\n        this.tmpSave().then((saveResult) => {\r\n          // 检查保存结果，如果校验失败则不继续提交\r\n          if (!saveResult || saveResult.success === false) {\r\n            return; // 校验失败，不继续提交流程\r\n          }\r\n\r\n          const data = {\r\n            projectId: this.$route.query.projectId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n          };\r\n          checkReviewSummary(data).then((response) => {\r\n            if (response.code == 200) {\r\n            // 修改专家进度\r\n            const status = {\r\n              evalExpertScoreInfoId: JSON.parse(\r\n                localStorage.getItem(\"evalExpertScoreInfo\")\r\n              ).evalExpertScoreInfoId,\r\n              evalState: 1,\r\n            };\r\n            editEvalExpertScoreInfo(status).then((res) => {\r\n              if (res.code == 200) {\r\n                this.$message.success(\"提交成功\");\r\n              }\r\n            });\r\n            this.$emit(\"send\", \"two\");\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      })\r\n    },\r\n    /**\r\n     * 显示采购文件PDF\r\n     */\r\n    viewPurchasing() {\r\n      this.activeButton = 'procurement'; // 设置当前激活按钮\r\n      this.double = false; // 单文件模式\r\n      this.responseShow = false; // 不显示响应文件\r\n      this.procurementShow = true; // 显示采购文件\r\n\r\n      // 右侧评分项显示为采购文件的评分项\r\n      let pageProcurementArr = []; // 采购文件评分项数组\r\n      for (let item in this.entDocProcurementPage){\r\n        pageProcurementArr.push({\r\n          itemName: item,\r\n          jumpToPage: this.entDocProcurementPage[item]\r\n        })\r\n      }\r\n\r\n      console.log(this.scoringSystem.uitems);\r\n      console.log(pageProcurementArr)\r\n      this.pageProcurement = [];\r\n      for (let i = 0; i < this.scoringSystem.uitems.length;i++){\r\n        for (let j = 0; j < pageProcurementArr.length;j++){\r\n          if (this.scoringSystem.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n            this.pageProcurement.push({...this.scoringSystem.uitems[i],...pageProcurementArr[j]});\r\n          }\r\n        }\r\n      }\r\n      console.log(this.pageProcurement)\r\n    },\r\n\r\n    /**\r\n     * 跳转到采购文件对应页码\r\n     * @param {Object} item - 评分项对象\r\n     */\r\n    jumpToProcurementPage(item) {\r\n      if (item.jumpToPage && this.$refs.procurement) {\r\n        this.$refs.procurement.skipPage(item.jumpToPage);\r\n      }\r\n    },\r\n\t  \r\n\t  /**\r\n\t   * 检查是否可以跳转页面\r\n\t   * @returns {boolean} 是否可以跳转\r\n\t   */\r\n\t  canJumpToPage() {\r\n\t\t  // 如果只显示采购文件\r\n\t\t  if (this.procurementShow && !this.responseShow) {\r\n\t\t\t  return this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  // 如果只显示响应文件\r\n\t\t  if (this.responseShow && !this.procurementShow) {\r\n\t\t\t  return this.responsePdfRendered;\r\n\t\t  }\r\n\t\t  // 如果对比模式（两个都显示）\r\n\t\t  if (this.responseShow && this.procurementShow) {\r\n\t\t\t  return this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  return false;\r\n\t  },\r\n\t  /**\r\n\t   * 处理PDF渲染状态变化\r\n\t   * @param {boolean} isRendered 是否渲染完成\r\n\t   * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t   */\r\n\t  handlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t\t  if (pdfType === 'response') {\r\n\t\t\t  this.responsePdfRendered = isRendered;\r\n\t\t  } else if (pdfType === 'procurement') {\r\n\t\t\t  this.procurementPdfRendered = isRendered;\r\n\t\t  }\r\n\t\t  \r\n\t\t  if (isRendered) {\r\n\t\t\t  console.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t\t  }\r\n\t  },\r\n\t  \r\n    // 跳转到二次报价\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query: query });\r\n    },\r\n    // 跳转到询标\r\n    bidInquiry() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      console.log(\"query\", query);\r\n      this.$router.push({ path: \"/bidInquiry\", query: query });\r\n    },\r\n    // 获取因素对应页码\r\n    getFactorsPage() {\r\n      this.factorsPage = JSON.parse(localStorage.getItem(\"entDocResponsePage\"));\r\n    },\r\n    \r\n    /**\r\n     * 获取评分项的最大分值\r\n     * @param {Object} item - 评分项对象\r\n     * @returns {number} 最大分值\r\n     */\r\n    getMaxScore(item) {\r\n      if (!item) return 0;\r\n      \r\n      // 如果有分数挡位，使用挡位中的最大值\r\n      if (item.scoreLevel && item.scoreLevel.length > 0 && item.scoreLevel !== null && item.scoreLevel !== undefined) {\r\n        const scoreLevels = item.scoreLevel.split(',').map(level => parseFloat(level.trim())).filter(level => !isNaN(level));\r\n        if (scoreLevels.length > 0) {\r\n          return Math.max(...scoreLevels);\r\n        }\r\n      }\r\n      \r\n      // 否则使用配置的最大分值\r\n      return parseFloat(item.score) || 0;\r\n    },\r\n\t  downloadFile(item){\r\n\t\t\tthis.$download.zip(item.filePath,item.fileName);\r\n\t  },\r\n\t  \r\n\t  // ========== 悬停相关 ==========\r\n\t  /**\r\n\t   * 显示评分项悬浮框\r\n\t   * @param {Object} factorItem 评分项对象\r\n\t   */\r\n\t  showFactorTooltip(factorItem) {\r\n\t\t  if (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\t\t  \r\n\t\t  // 清除之前的定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t  this.tooltipTimer = setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = factorItem;\r\n\t\t  }, 300); // 300ms延迟\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 隐藏评分项悬浮框\r\n\t   */\r\n\t  hideFactorTooltip() {\r\n\t\t  // 清除定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t  setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = null;\r\n\t\t  }, 100);\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t   */\r\n\t  clearTooltipTimer() {\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t  }\r\n  },\r\n  mounted() {\r\n    this.init();\r\n    this.getFactorsPage();\r\n  },\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// Main layout containers\r\n.technical-review-container {\r\n  min-height: 57vh;\r\n  display: flex;\r\n}\r\n\r\n.main-content {\r\n  min-height: 57vh;\r\n  width: 79%;\r\n}\r\n\r\n// Header section styles\r\n.header-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n\r\n.title-section {\r\n  display: flex;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333333;\r\n}\r\n\r\n.main-title {\r\n  // Inherits from title-section\r\n}\r\n\r\n.help-section {\r\n  display: grid;\r\n  justify-items: center;\r\n  position: relative;\r\n  bottom: -30px;\r\n}\r\n\r\n.help-text {\r\n  font-size: 12px;\r\n}\r\n\r\n.help-image {\r\n  width: 80px;\r\n  height: 30px;\r\n  margin-right: 20px;\r\n}\r\n\r\n// File list container\r\n.file-list-container {\r\n  border-right: 1px solid #e6e6e6;\r\n  border-left: 1px solid #e6e6e6;\r\n  padding: 10px;\r\n  overflow-y: auto;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\ttransition: all 0.3s ease;\r\n\t::v-deep .el-card__body {\r\n\t\tpadding: 0;\r\n\t}\r\n}\r\n\r\n.file-list-title {\r\n  font-weight: bold;\r\n  margin-bottom: 10px;\r\n  color: #333;\r\n}\r\n\r\n.file-item {\r\n  margin-bottom: 8px;\r\n  cursor: pointer;\r\n}\r\n\r\n.file-item-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 5px;\r\n}\r\n\r\n.file-icon {\r\n  margin-right: 8px;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-name {\r\n  font-size: 12px;\r\n  flex: 1;\r\n  word-break: break-all;\r\n}\r\n\r\n.download-icon {\r\n  margin-left: 8px;\r\n  color: #999;\r\n}\r\n\r\n// Action buttons section\r\n.action-buttons {\r\n  text-align: right;\r\n}\r\n\r\n.button-group {\r\n  margin-top: 20px;\r\n\t.item-button{\r\n\t\tcolor: #fff;\r\n\t}\r\n}\r\n\r\n.primary-btn {\r\n  background-color: #176ADB;\r\n  color: #FFFFFF;\r\n  border: 1px solid #176ADB;\r\n}\r\n\r\n// Content section styles\r\n.content-section {\r\n  display: flex;\r\n  height: 82%;\r\n}\r\n\r\n// PDF preview area\r\n.pdf-preview-area {\r\n  display: flex;\r\n  justify-content: center;\r\n  flex: 1;\r\n}\r\n\r\n.pdf-panel {\r\n  width: 49%;\r\n}\r\n\r\n.response-panel {\r\n  &.border-right {\r\n    border-right: 1px solid #176ADB;\r\n  }\r\n}\r\n\r\n.procurement-panel {\r\n  &.border-left {\r\n    border-left: 1px solid #176ADB;\r\n  }\r\n}\r\n\r\n// Divider styles\r\n.divider {\r\n  min-height: 57vh;\r\n  width: 1%;\r\n  background-color: #F5F5F5;\r\n}\r\n\r\n// Sidebar styles\r\n.sidebar {\r\n  min-height: 57vh;\r\n  width: 20%;\r\n}\r\n\r\n.sidebar-header {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n\r\n.supplier-select {\r\n  width: 100%;\r\n}\r\n\r\n.sidebar-content {\r\n  padding: 15px 20px;\r\n}\r\n\r\n// Factor items styles\r\n.factor-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.factor-header {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  text-align: left;\r\n  width: 98%;\r\n}\r\n\r\n.factor-name {\r\n  cursor: pointer;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 500;\r\n  font-size: 16px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n}\r\n\r\n.max-score {\r\n  font-size: 12px;\r\n  color: red;\r\n}\r\n\r\n.factor-input {\r\n  display: flex;\r\n  width: 100%;\r\n  justify-content: flex-end;\r\n  padding: 10px;\r\n}\r\n\r\n.score-value {\r\n  color: green;\r\n  font-size: 16px;\r\n}\r\n\r\n// Submit section\r\n.submit-section {\r\n  display: flex;\r\n  margin: 32px 0;\r\n  justify-content: space-evenly;\r\n}\r\n\r\n// Review content styles\r\n.review-content {\r\n  text-align: left;\r\n  font-size: 14px;\r\n}\r\n\r\n.review-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 15px;\r\n  color: #176ADB;\r\n  letter-spacing: 0;\r\n}\r\n\r\n.review-text {\r\n  padding: 6px 30px;\r\n}\r\n\r\n// Existing styles\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n\t  transition: all 0.3s ease;\r\n\t  padding: 4px 8px;\r\n\t  border-radius: 4px;\r\n\t  \r\n\t  &:hover {\r\n\t\t  background-color: #f0f8ff;\r\n\t\t  color: #176ADB;\r\n\t\t  transform: translateX(2px);\r\n\t  }\r\n  }\r\n}\r\n.item-button {\r\n  border: 1px solid #979797;\r\n  width: 150px;\r\n  height: 36px;\r\n  margin: 0 10px;\r\n  font-weight: 700;\r\n  font-size: 17px;\r\n  border-radius: 6px;\r\n  color: #333333;\r\n  &:hover {\r\n    color: #333333;\r\n  }\r\n}\r\n.technical-blue-btn {\r\n  background-color: #176ADB !important;\r\n  color: #fff !important;\r\n  border: 1px solid #176ADB !important;\r\n}\r\n.technical-blue-btn-active {\r\n  background-color: #FF6B35 !important;\r\n  color: #fff !important;\r\n  border: 1px solid #FF6B35 !important;\r\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.item-button-little {\r\n  width: 124px;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #fff;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  }\r\n\r\n  ::v-deep .el-card__body {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\t\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\t\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\t\t\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\t\t\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\t\t\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsPA,IAAAA,OAAA,GAAAC,OAAA;AAOA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,aAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,aAAA;MACAC,UAAA;MACAC,QAAA;MACAC,cAAA;MACAC,UAAA;MACAC,kBAAA;MACAC,IAAA;MACAC,YAAA;MACAC,eAAA;MACAC,MAAA;MACAC,UAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,YAAA;MAEAC,WAAA;MACAC,cAAA;MACAC,eAAA;MAEA;MACAC,YAAA;MAAA;;MAEA;MACAC,qBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,eAAA;MAAA;;MAEA;MACAC,mBAAA;MAAA;MACAC,sBAAA;MAAA;;MAEAC,OAAA;MAEA;MACAC,iBAAA;MAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA;IACA;AACA;AACA;AACA;IACAC,UAAA,WAAAA,WAAAC,KAAA;MACA;MACA,IAAAC,QAAA,GAAAD,KAAA,CAAAE,KAAA,IAAAF,KAAA,CAAAG,OAAA;;MAEA;MACA,IACAF,QAAA,UAAAA,QAAA;MAAA;MACAA,QAAA;MAAA;MACAA,QAAA;MAAA;MACAA,QAAA;MAAA;MACAA,QAAA;MAAA;MACAA,QAAA,UAAAA,QAAA;MAAA,EACA;QACA;MACA;;MAEA;MACAD,KAAA,CAAAI,cAAA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC,gBAAA,WAAAA,iBAAAC,MAAA,EAAAC,KAAA;MACA;MACA,IAAAC,UAAA,GAAAD,KAAA,CAAAE,OAAA;;MAEA;MACA,IAAAC,KAAA,GAAAF,UAAA,CAAAG,KAAA;MACA,IAAAD,KAAA,CAAAE,MAAA;QACAJ,UAAA,GAAAE,KAAA,YAAAA,KAAA,CAAAG,KAAA,IAAAC,IAAA;MACA;;MAEA;MACA,IAAAJ,KAAA,CAAAE,MAAA,UAAAF,KAAA,IAAAE,MAAA;QACAJ,UAAA,GAAAE,KAAA,YAAAA,KAAA,IAAAK,SAAA;MACA;;MAEA;MACA,KAAAtC,kBAAA,CAAA6B,MAAA,EAAAU,KAAA,GAAAR,UAAA;;MAEA;MACA,KAAAS,aAAA,CAAAX,MAAA,EAAAE,UAAA;IACA;IAEAU,IAAA,WAAAA,KAAA;MAAA,IAAAC,KAAA;MACA,IAAA3C,UAAA,GAAA4C,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACA,KAAAxC,kBAAA,GAAAqC,IAAA,CAAAC,KAAA,CACAC,YAAA,CAAAC,OAAA,sBACA;MACA;MACA,KAAAjC,qBAAA,GAAA8B,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACA,IAAAC,oBAAA;QAAAC,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF;MAAA,GAAAG,IAAA,CACA,UAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAX,KAAA,CAAAhD,OAAA,GAAA0D,QAAA,CAAAE,IAAA,CAAAC,MAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAC,cAAA;UAAA;QACA;UACAf,KAAA,CAAAgB,QAAA,CAAAC,OAAA,CAAAP,QAAA,CAAAQ,GAAA;QACA;MACA,CACA;MACA,IAAAC,uBAAA,OAAAZ,MAAA,CAAAC,KAAA,CAAAF,SAAA,EAAAjD,UAAA,CAAA+D,QAAA,EAAAX,IAAA,CACA,UAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACA;UACAX,KAAA,CAAA3B,eAAA,GAAAqC,QAAA,CAAA3D,IAAA,CAAAsE,gBAAA,CAAAC,WAAA,CAAAT,MAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAS,QAAA;UAAA;UAEAvB,KAAA,CAAA/C,aAAA,GACAyD,QAAA,CAAA3D,IAAA,CAAAyE,kBAAA,CAAAC,kBAAA,CAAAC,IAAA,CACA,UAAAZ,IAAA;YACA,OACAA,IAAA,CAAAa,mBAAA,IACA3B,KAAA,CAAAO,MAAA,CAAAC,KAAA,CAAAmB,mBAAA;UAEA,CACA;UACAxB,YAAA,CAAAyB,OAAA,CACA,gCACA3B,IAAA,CAAA4B,SAAA,CAAA7B,KAAA,CAAA/C,aAAA,CAAA6E,4BAAA,CACA;UACA;UACA9B,KAAA,CAAA1C,kBAAA,GAAA0C,KAAA,CAAA/C,aAAA,CAAA8E,MAAA,CAAAC,MAAA,CACA,UAAAC,GAAA,EAAAC,CAAA,EAAAC,KAAA;YACAF,GAAA,CAAAjC,KAAA,CAAA/C,aAAA,CAAA8E,MAAA,CAAAI,KAAA,EAAAC,eAAA;cACAvC,KAAA;cACAwC,MAAA;YACA;YACAC,OAAA,CAAAC,GAAA,kBAAAvC,KAAA,CAAA/C,aAAA;YACA,OAAAgF,GAAA;UACA,GACA,EACA;QACA;UACAjC,KAAA,CAAAwC,QAAA,CAAAvB,OAAA,CAAAP,QAAA,CAAAQ,GAAA;QACA;MACA,CACA;MACA,IAAAuB,iBAAA,OAAAlC,MAAA,CAAAC,KAAA,CAAAF,SAAA,EAAAG,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAX,KAAA,CAAAzC,IAAA,GAAAmD,QAAA,CAAA3D,IAAA;UACA,IAAAiD,KAAA,CAAAzC,IAAA,CAAAmF,oBAAA,IAAAC,SAAA;YACA3C,KAAA,CAAAhC,cAAA,GAAAgC,KAAA,CAAAzC,IAAA,CAAAmF,oBAAA;UACA;UACA;UACA;UACA;QACA;UACA1C,KAAA,CAAAgB,QAAA,CAAAC,OAAA,CAAAP,QAAA,CAAAQ,GAAA;QACA;MACA;MACA;MACA,KAAA0B,cAAA;IACA;IAEA;AACA;AACA;IACAA,cAAA,WAAAA,eAAA;MACA;QACA,IAAAC,UAAA,GAAA1C,YAAA,CAAAC,OAAA;QACA,IAAAyC,UAAA;UACA,KAAAxF,UAAA,GAAA4C,IAAA,CAAAC,KAAA,CAAA2C,UAAA;UACAP,OAAA,CAAAC,GAAA,kBAAAlF,UAAA;QACA;UACAiF,OAAA,CAAAQ,IAAA;QACA;MACA,SAAAC,KAAA;QACAT,OAAA,CAAAS,KAAA,eAAAA,KAAA;MACA;IACA;IAEAC,YAAA,WAAAA,aAAA5D,KAAA;MAAA,IAAA6D,MAAA;MACA,IAAAC,MAAA,CAAAC,IAAA,MAAA/F,cAAA,EAAAqC,MAAA;QACA,KAAA2D,OAAA;MACA;MACA,KAAAhG,cAAA,QAAAJ,OAAA,CAAA0E,IAAA,WAAAZ,IAAA;QACA,OAAAA,IAAA,CAAAuC,UAAA,IAAAjE,KAAA;MACA;;MAEA;MACA,KAAAtB,YAAA,QAAAD,WAAA,MAAAT,cAAA,CAAAkG,QAAA;MAEA,IAAAvG,IAAA;QACAwG,cAAA,OAAAlG,UAAA,CAAA+D,QAAA;QACAd,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QACAqB,mBAAA,OAAApB,MAAA,CAAAC,KAAA,CAAAmB;MACA;MACA,IAAA6B,uBAAA,EAAAzG,IAAA,EAAA0D,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAsC,MAAA,CAAAtF,UAAA,GAAA+C,QAAA,CAAA3D,IAAA;UACA,IAAA0G,MAAA,GAAAR,MAAA,CAAAtF,UAAA,CAAA+D,IAAA,WAAAZ,IAAA;YACA,OAAAA,IAAA,CAAAuC,UAAA,IAAAjE,KAAA;UACA,GAAAsE,2BAAA;UACA,IAAAD,MAAA;YACAA,MAAA,CAAAE,GAAA,WAAA7C,IAAA;cACAmC,MAAA,CAAA3F,kBAAA,CAAAwD,IAAA,CAAA8C,oBAAA,EAAAvB,MAAA,GACAvB,IAAA,CAAA+C,gBAAA;cACAZ,MAAA,CAAA3F,kBAAA,CAAAwD,IAAA,CAAA8C,oBAAA,EAAA/D,KAAA,GACAiB,IAAA,CAAAgD,gBAAA;YACA;UACA;YACAZ,MAAA,CAAAC,IAAA,CAAAF,MAAA,CAAA3F,kBAAA,EAAAyG,OAAA,WAAAC,GAAA;cACAf,MAAA,CAAA3F,kBAAA,CAAA0G,GAAA,EAAAnE,KAAA;cACAoD,MAAA,CAAA3F,kBAAA,CAAA0G,GAAA,EAAA3B,MAAA;YACA;UACA;QACA;UACAY,MAAA,CAAAjC,QAAA,CAAAC,OAAA,CAAAP,QAAA,CAAAQ,GAAA;QACA;MACA;MACA,KAAA+C,gBAAA;MACA;MACA;IACA;IAGAnE,aAAA,WAAAA,cAAAX,MAAA,EAAAN,KAAA;MACA,IAAAqF,UAAA,GAAAC,UAAA,CAAAtF,KAAA;MACAyD,OAAA,CAAAC,GAAA,eAAA2B,UAAA;;MAEA;MACA,IAAAE,WAAA,QAAAnH,aAAA,CAAA8E,MAAA,CAAAL,IAAA,WAAAZ,IAAA;QAAA,OAAAA,IAAA,CAAAsB,eAAA,KAAAjD,MAAA;MAAA;MACA,IAAAkF,QAAA;MAEA,IAAAD,WAAA;QACA;QACA,IAAAA,WAAA,CAAAE,UAAA,IAAAF,WAAA,CAAAE,UAAA,CAAA7E,MAAA;UACA,IAAA8E,WAAA,GAAAH,WAAA,CAAAE,UAAA,CAAA9E,KAAA,MAAAmE,GAAA,WAAA7C,IAAA;YAAA,OAAAqD,UAAA,CAAArD,IAAA,CAAA0D,IAAA;UAAA,GAAA3D,MAAA,WAAAC,IAAA;YAAA,QAAA2D,KAAA,CAAA3D,IAAA;UAAA;UACA,IAAAyD,WAAA,CAAA9E,MAAA;YACA4E,QAAA,GAAAK,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAC,OAAA,EAAAP,WAAA;UACA;QACA;UACA;UACAF,QAAA,GAAAF,UAAA,CAAAC,WAAA,CAAAW,KAAA;QACA;MACA;MAEAzC,OAAA,CAAAC,GAAA,aAAA8B,QAAA;MAEA,KAAAI,KAAA,CAAAP,UAAA,KAAAG,QAAA;QACA,IAAAH,UAAA,GAAAG,QAAA;UACA,KAAArD,QAAA,CAAAC,OAAA,oDAAA+D,MAAA,CAAAX,QAAA;UACA;UACA,KAAA/G,kBAAA,CAAA6B,MAAA,EAAAU,KAAA;QACA,WAAAqE,UAAA;UACA,KAAAlD,QAAA,CAAAC,OAAA;UACA,KAAA3D,kBAAA,CAAA6B,MAAA,EAAAU,KAAA;QACA;MACA;IACA;IACAoE,gBAAA,WAAAA,iBAAA;MACA,IAAAf,MAAA,CAAAC,IAAA,MAAA/F,cAAA,EAAAqC,MAAA;QACA,KAAAuB,QAAA,CAAAC,OAAA;MACA;QACA,KAAA/C,YAAA;QACA,KAAAR,MAAA;QACA,KAAAD,eAAA;QACA,KAAAD,YAAA;QACA,KAAAO,WAAA,QAAAR,IAAA,CAAAA,IAAA,MAAAH,cAAA,CAAAkG,QAAA;MACA;IACA;IACA2B,YAAA,WAAAA,aAAA;MACA,IAAA/B,MAAA,CAAAC,IAAA,MAAA/F,cAAA,EAAAqC,MAAA;QACA,KAAAuB,QAAA,CAAAC,OAAA;MACA;QACA,KAAA/C,YAAA;QACA,KAAAR,MAAA;QACA,KAAAD,eAAA;QACA,KAAAD,YAAA;QACA,KAAAO,WAAA,QAAAR,IAAA,CAAAA,IAAA,MAAAH,cAAA,CAAAkG,QAAA;MACA;IACA;IACA4B,QAAA,WAAAA,SAAApE,IAAA;MACA;MACA,UAAAqE,aAAA;QACA,KAAAnE,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAA/D,UAAA,GAAA4D,IAAA;;MAEA;MACA,SAAArD,eAAA,UAAAD,YAAA;QACA,UAAAe,sBAAA;UACA,KAAAyC,QAAA,CAAAC,OAAA;UACA;QACA;QAEA,IAAAH,IAAA,CAAAsE,UAAA;UACA,KAAAC,KAAA,CAAAC,WAAA,CAAAC,QAAA,CAAAzE,IAAA,CAAAsE,UAAA;QACA,gBAAAjH,qBAAA,SAAAA,qBAAA,CAAA2C,IAAA,CAAA0E,QAAA;UACA,KAAAH,KAAA,CAAAC,WAAA,CAAAC,QAAA,MAAApH,qBAAA,CAAA2C,IAAA,CAAA0E,QAAA;QACA;QACA;MACA;;MAEA;MACA,IAAAtC,MAAA,CAAAC,IAAA,MAAArF,YAAA,EAAA2B,MAAA;QACA;QACA,SAAAjC,YAAA,SAAA6H,KAAA,CAAA3E,QAAA;UACA,UAAApC,mBAAA;YACA,KAAA0C,QAAA,CAAAC,OAAA;YACA;UACA;UACA,KAAAoE,KAAA,CAAA3E,QAAA,CAAA6E,QAAA,CACA,KAAAzH,YAAA,MAAAZ,UAAA,CAAAsI,QAAA,CACA;QACA;;QAEA;QACA,SAAA/H,eAAA,SAAA4H,KAAA,CAAAC,WAAA;UACA,UAAA/G,sBAAA;YACA,KAAAyC,QAAA,CAAAC,OAAA;YACA;UACA;;UAEA;UACA,SAAA9C,qBAAA,SAAAA,qBAAA,CAAA2C,IAAA,CAAA0E,QAAA;YACA,KAAAH,KAAA,CAAAC,WAAA,CAAAC,QAAA,MAAApH,qBAAA,CAAA2C,IAAA,CAAA0E,QAAA;UACA;YACA;YACA;UAAA;QAEA;;QAEA;QACA;QACA;QACA;MACA;QACA,KAAAxE,QAAA,CAAAC,OAAA;MACA;IACA;IACAwE,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACAxC,MAAA,CAAAC,IAAA,MAAA7F,kBAAA,EAAAyG,OAAA,WAAAC,GAAA;QACA0B,MAAA,CAAApI,kBAAA,CAAA0G,GAAA,EAAAnE,KAAA;QACA6F,MAAA,CAAApI,kBAAA,CAAA0G,GAAA,EAAA3B,MAAA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAsD,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAf,OAAA,EACA,KAAA7H,aAAA,CAAA8E,MAAA;QAAA+D,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAAnF,IAAA,GAAAgF,KAAA,CAAA1G,KAAA;UACA,IAAAS,KAAA,QAAAvC,kBAAA,CAAAwD,IAAA,CAAAsB,eAAA,EAAAvC,KAAA;;UAEA;UACA,IAAAA,KAAA,aAAAA,KAAA,WAAAA,KAAA,KAAA8C,SAAA;YACA;YACA;UACA;;UAEA;UACA,KAAA7B,IAAA,CAAAwD,UAAA,IAAAxD,IAAA,CAAAwD,UAAA,CAAA7E,MAAA,UAAAqB,IAAA,CAAAwD,UAAA,aAAAxD,IAAA,CAAAwD,UAAA,KAAA3B,SAAA;YACA,IAAAoC,KAAA,GAAAZ,UAAA,CAAAtE,KAAA;YACA,IAAA4E,KAAA,CAAAM,KAAA,KAAAA,KAAA;cACA,KAAA/D,QAAA,CAAAC,OAAA,IAAA+D,MAAA,CAAAlE,IAAA,CAAA0E,QAAA;cACA;YACA;YACA;YACA,IAAAnB,QAAA,QAAA6B,WAAA,CAAApF,IAAA;YACA,IAAAiE,KAAA,GAAAV,QAAA;cACA,KAAArD,QAAA,CAAAC,OAAA,IAAA+D,MAAA,CAAAlE,IAAA,CAAA0E,QAAA,gDAAAR,MAAA,CAAAX,QAAA;cACA;YACA;UACA;YACA;YACA,IAAAE,WAAA,GAAAzD,IAAA,CAAAwD,UAAA,CAAA9E,KAAA,MAAAmE,GAAA,WAAAwC,KAAA;cAAA,OAAAA,KAAA,CAAA3B,IAAA;YAAA;YACA,KAAAD,WAAA,CAAA6B,QAAA,CAAAvG,KAAA,CAAAwG,QAAA;cACA,KAAArF,QAAA,CAAAC,OAAA,IAAA+D,MAAA,CAAAlE,IAAA,CAAA0E,QAAA,oFAAAR,MAAA,CAAAlE,IAAA,CAAAwD,UAAA;cACA;YACA;UACA;QACA;MAAA,SAAAgC,GAAA;QAAAV,SAAA,CAAAW,CAAA,CAAAD,GAAA;MAAA;QAAAV,SAAA,CAAAY,CAAA;MAAA;MACA;IACA;IAEApD,OAAA,WAAAA,QAAA;MAAA,IAAAqD,MAAA;MACAnE,OAAA,CAAAC,GAAA;;MAEA;MACA,UAAAoD,kBAAA;QACA,OAAAe,OAAA,CAAAC,OAAA;UAAAhG,IAAA;UAAAiG,OAAA;QAAA;MACA;MAEA,IAAAC,WAAA,GAAA5G,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAA4B,SAAA,MAAAvE,kBAAA;MAEA,IAAAP,IAAA;MACA,SAAAoF,KAAA,MAAAA,KAAA,QAAAlF,aAAA,CAAA8E,MAAA,CAAAtC,MAAA,EAAA0C,KAAA;QACA,IAAArB,IAAA,QAAA7D,aAAA,CAAA8E,MAAA,CAAAI,KAAA;QACA,IAAAhD,MAAA,GAAA2B,IAAA,CAAAsB,eAAA;QACA;QACA,IAAA0B,gBAAA,GAAA+C,WAAA,CAAA1H,MAAA,EAAAU,KAAA;QACA,IAAAiE,gBAAA,aAAAA,gBAAA;UACA;UACAxB,OAAA,CAAAC,GAAA;UACA;QACA;QACA;QACA,IAAAsB,gBAAA,GAAAgD,WAAA,CAAA1H,MAAA,EAAAkD,MAAA;QACAtF,IAAA,CAAA+J,IAAA;UACAlD,oBAAA,EAAAzE,MAAA;UACAoE,cAAA,OAAAlG,UAAA,CAAA+D,QAAA;UACA2F,KAAA,OAAA3J,cAAA,CAAAkG,QAAA;UACAQ,gBAAA,EAAAA,gBAAA;UACAD,gBAAA,EAAAA;QACA;MACA;MACA,IAAA9G,IAAA,CAAA0C,MAAA;QACA6C,OAAA,CAAAC,GAAA;QACA,WAAAyE,sBAAA,EAAAjK,IAAA,EAAA0D,IAAA,WAAAC,QAAA;UACA4B,OAAA,CAAAC,GAAA,CAAA7B,QAAA,CAAAQ,GAAA;UACA,IAAAR,QAAA,CAAAC,IAAA;YACA8F,MAAA,CAAAzF,QAAA,CAAA4F,OAAA;YACA;cAAAjG,IAAA;cAAAiG,OAAA;YAAA;UACA;YACAH,MAAA,CAAAzF,QAAA,CAAAC,OAAA,CAAAP,QAAA,CAAAQ,GAAA;YACA;cAAAP,IAAA,EAAAD,QAAA,CAAAC,IAAA;cAAAiG,OAAA;YAAA;UACA;QACA,GAAAK,KAAA,WAAAlE,KAAA;UACA0D,MAAA,CAAAzF,QAAA,CAAA+B,KAAA;UACA;YAAApC,IAAA;YAAAiG,OAAA;UAAA;QACA;MACA;QACA,OAAAF,OAAA,CAAAC,OAAA;UAAAhG,IAAA;UAAAiG,OAAA;QAAA;MACA;IACA;IACAM,IAAA,WAAAA,KAAA;MAAA,IAAAC,MAAA;MACA,SAAAhK,QAAA;QACA,KAAA6D,QAAA,CAAAC,OAAA;MACA;QACA;QACA,IAAAlE,IAAA;QACA,SAAAoF,KAAA,MAAAA,KAAA,QAAAlF,aAAA,CAAA8E,MAAA,CAAAtC,MAAA,EAAA0C,KAAA;UACA,IAAArB,IAAA,QAAA7D,aAAA,CAAA8E,MAAA,CAAAI,KAAA;UACA,IAAAhD,MAAA,GAAA2B,IAAA,CAAAsB,eAAA;UACA;UACA,IAAA0B,gBAAA,QAAAxG,kBAAA,CAAA6B,MAAA,EAAAU,KAAA;UACA,IAAAiE,gBAAA,aAAAA,gBAAA;YACA;YACA,KAAA9C,QAAA,CAAAC,OAAA,8CAAA+D,MAAA,CAAAlE,IAAA,CAAA0E,QAAA;YACA;UACA;UACA;UACA,IAAA3B,gBAAA,QAAAvG,kBAAA,CAAA6B,MAAA,EAAAkD,MAAA;UACAtF,IAAA,CAAA+J,IAAA;YACAlD,oBAAA,EAAAzE,MAAA;YACAoE,cAAA,OAAAlG,UAAA,CAAA+D,QAAA;YACA2F,KAAA,OAAA3J,cAAA,CAAAkG,QAAA;YACAQ,gBAAA,EAAAA,gBAAA;YACAD,gBAAA,EAAAA;UACA;QACA;QAEA,IAAAmD,sBAAA,EAAAjK,IAAA,EAAA0D,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA;YACAwG,MAAA,CAAAnG,QAAA,CAAA4F,OAAA,CAAAlG,QAAA,CAAAQ,GAAA;UACA;YACAiG,MAAA,CAAAnG,QAAA,CAAAC,OAAA,CAAAP,QAAA,CAAAQ,GAAA;UACA;QACA;MACA;IACA;IACA;IACAkG,mBAAA,WAAAA,oBAAA;MACA,IAAArK,IAAA;MACA,SAAAoF,KAAA,MAAAA,KAAA,QAAAlF,aAAA,CAAA8E,MAAA,CAAAtC,MAAA,EAAA0C,KAAA;QACA,IAAArB,IAAA,QAAA7D,aAAA,CAAA8E,MAAA,CAAAI,KAAA;QACA,IAAAhD,MAAA,GAAA2B,IAAA,CAAAsB,eAAA;QACA;QACA,IAAA0B,gBAAA,QAAAxG,kBAAA,CAAA6B,MAAA,EAAAU,KAAA;QACA,IAAAiE,gBAAA,aAAAA,gBAAA;UACA;UACA,KAAA9C,QAAA,CAAAC,OAAA,8CAAA+D,MAAA,CAAAlE,IAAA,CAAA0E,QAAA;UACA;QACA;QACA;QACA,IAAA3B,gBAAA,QAAAvG,kBAAA,CAAA6B,MAAA,EAAAkD,MAAA;QACAtF,IAAA,CAAA+J,IAAA;UACAlD,oBAAA,EAAAzE,MAAA;UACAoE,cAAA,OAAAlG,UAAA,CAAA+D,QAAA;UACA2F,KAAA,OAAA3J,cAAA,CAAAkG,QAAA;UACAQ,gBAAA,EAAAA,gBAAA;UACAD,gBAAA,EAAAA;QACA;MACA;;MAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACA,OAAA9G,IAAA;IACA;IACAsK,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,KAAAlE,OAAA,GAAA3C,IAAA,WAAA8G,UAAA;QACA;QACA,KAAAA,UAAA,IAAAA,UAAA,CAAAX,OAAA;UACA;QACA;QAEA,IAAA7J,IAAA;UACAuD,SAAA,EAAAgH,MAAA,CAAA/G,MAAA,CAAAC,KAAA,CAAAF,SAAA;UACAiD,cAAA,EAAA+D,MAAA,CAAAjK,UAAA,CAAA+D,QAAA;UACAO,mBAAA,EAAA2F,MAAA,CAAA/G,MAAA,CAAAC,KAAA,CAAAmB;QACA;QACA,IAAA6F,0BAAA,EAAAzK,IAAA,EAAA0D,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA;YACA;YACA,IAAA8G,MAAA;cACAC,qBAAA,EAAAzH,IAAA,CAAAC,KAAA,CACAC,YAAA,CAAAC,OAAA,uBACA,EAAAsH,qBAAA;cACAC,SAAA;YACA;YACA,IAAAC,qCAAA,EAAAH,MAAA,EAAAhH,IAAA,WAAAoH,GAAA;cACA,IAAAA,GAAA,CAAAlH,IAAA;gBACA2G,MAAA,CAAAtG,QAAA,CAAA4F,OAAA;cACA;YACA;YACAU,MAAA,CAAAQ,KAAA;UACA;YACAR,MAAA,CAAAtG,QAAA,CAAAC,OAAA,CAAAP,QAAA,CAAAQ,GAAA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACA6G,cAAA,WAAAA,eAAA;MACA,KAAA7J,YAAA;MACA,KAAAR,MAAA;MACA,KAAAF,YAAA;MACA,KAAAC,eAAA;;MAEA;MACA,IAAAuK,kBAAA;MACA,SAAAlH,IAAA,SAAA3C,qBAAA;QACA6J,kBAAA,CAAAlB,IAAA;UACAtB,QAAA,EAAA1E,IAAA;UACAsE,UAAA,OAAAjH,qBAAA,CAAA2C,IAAA;QACA;MACA;MAEAwB,OAAA,CAAAC,GAAA,MAAAtF,aAAA,CAAA8E,MAAA;MACAO,OAAA,CAAAC,GAAA,CAAAyF,kBAAA;MACA,KAAA5J,eAAA;MACA,SAAA6J,CAAA,MAAAA,CAAA,QAAAhL,aAAA,CAAA8E,MAAA,CAAAtC,MAAA,EAAAwI,CAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,kBAAA,CAAAvI,MAAA,EAAAyI,CAAA;UACA,SAAAjL,aAAA,CAAA8E,MAAA,CAAAkG,CAAA,EAAAzC,QAAA,IAAAwC,kBAAA,CAAAE,CAAA,EAAA1C,QAAA;YACA,KAAApH,eAAA,CAAA0I,IAAA,KAAAqB,cAAA,CAAArD,OAAA,MAAAqD,cAAA,CAAArD,OAAA,WAAA7H,aAAA,CAAA8E,MAAA,CAAAkG,CAAA,IAAAD,kBAAA,CAAAE,CAAA;UACA;QACA;MACA;MACA5F,OAAA,CAAAC,GAAA,MAAAnE,eAAA;IACA;IAEA;AACA;AACA;AACA;IACAgK,qBAAA,WAAAA,sBAAAtH,IAAA;MACA,IAAAA,IAAA,CAAAsE,UAAA,SAAAC,KAAA,CAAAC,WAAA;QACA,KAAAD,KAAA,CAAAC,WAAA,CAAAC,QAAA,CAAAzE,IAAA,CAAAsE,UAAA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAD,aAAA,WAAAA,cAAA;MACA;MACA,SAAA1H,eAAA,UAAAD,YAAA;QACA,YAAAe,sBAAA;MACA;MACA;MACA,SAAAf,YAAA,UAAAC,eAAA;QACA,YAAAa,mBAAA;MACA;MACA;MACA,SAAAd,YAAA,SAAAC,eAAA;QACA,YAAAa,mBAAA,SAAAC,sBAAA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACA8J,2BAAA,WAAAA,4BAAAC,UAAA,EAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAAjK,mBAAA,GAAAgK,UAAA;MACA,WAAAC,OAAA;QACA,KAAAhK,sBAAA,GAAA+J,UAAA;MACA;MAEA,IAAAA,UAAA;QACAhG,OAAA,CAAAC,GAAA,IAAAyC,MAAA,CAAAuD,OAAA;MACA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,IAAAhI,KAAA;QACAF,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QACAmI,IAAA,OAAAlI,MAAA,CAAAC,KAAA,CAAAiI,IAAA;QACA9G,mBAAA,EAAA1B,IAAA,CAAAC,KAAA,CACAC,YAAA,CAAAC,OAAA,iCACA;MACA;MACA,KAAAsI,OAAA,CAAA5B,IAAA;QAAA6B,IAAA;QAAAnI,KAAA,EAAAA;MAAA;IACA;IACA;IACAoI,UAAA,WAAAA,WAAA;MACA,IAAApI,KAAA;QACAF,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QACAmI,IAAA,OAAAlI,MAAA,CAAAC,KAAA,CAAAiI,IAAA;QACA9G,mBAAA,EAAA1B,IAAA,CAAAC,KAAA,CACAC,YAAA,CAAAC,OAAA,iCACA;MACA;MACAkC,OAAA,CAAAC,GAAA,UAAA/B,KAAA;MACA,KAAAkI,OAAA,CAAA5B,IAAA;QAAA6B,IAAA;QAAAnI,KAAA,EAAAA;MAAA;IACA;IACA;IACAqI,cAAA,WAAAA,eAAA;MACA,KAAAhL,WAAA,GAAAoC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACA8F,WAAA,WAAAA,YAAApF,IAAA;MACA,KAAAA,IAAA;;MAEA;MACA,IAAAA,IAAA,CAAAwD,UAAA,IAAAxD,IAAA,CAAAwD,UAAA,CAAA7E,MAAA,QAAAqB,IAAA,CAAAwD,UAAA,aAAAxD,IAAA,CAAAwD,UAAA,KAAA3B,SAAA;QACA,IAAA4B,WAAA,GAAAzD,IAAA,CAAAwD,UAAA,CAAA9E,KAAA,MAAAmE,GAAA,WAAAwC,KAAA;UAAA,OAAAhC,UAAA,CAAAgC,KAAA,CAAA3B,IAAA;QAAA,GAAA3D,MAAA,WAAAsF,KAAA;UAAA,QAAA1B,KAAA,CAAA0B,KAAA;QAAA;QACA,IAAA5B,WAAA,CAAA9E,MAAA;UACA,OAAAiF,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAC,OAAA,EAAAP,WAAA;QACA;MACA;;MAEA;MACA,OAAAJ,UAAA,CAAArD,IAAA,CAAAiE,KAAA;IACA;IACA+D,YAAA,WAAAA,aAAAhI,IAAA;MACA,KAAAiI,SAAA,CAAAC,GAAA,CAAAlI,IAAA,CAAAmI,QAAA,EAAAnI,IAAA,CAAAoI,QAAA;IACA;IAEA;IACA;AACA;AACA;AACA;IACAC,iBAAA,WAAAA,kBAAAC,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAD,UAAA,CAAAE,UAAA;;MAEA;MACA,SAAA5K,YAAA;QACA6K,YAAA,MAAA7K,YAAA;MACA;;MAEA;MACA,KAAAA,YAAA,GAAA8K,UAAA;QACAH,MAAA,CAAA5K,iBAAA,GAAA2K,UAAA;MACA;IACA;IAEA;AACA;AACA;IACAK,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAAhL,YAAA;QACA6K,YAAA,MAAA7K,YAAA;QACA,KAAAA,YAAA;MACA;;MAEA;MACA8K,UAAA;QACAE,MAAA,CAAAjL,iBAAA;MACA;IACA;IAEA;AACA;AACA;IACAkL,iBAAA,WAAAA,kBAAA;MACA,SAAAjL,YAAA;QACA6K,YAAA,MAAA7K,YAAA;QACA,KAAAA,YAAA;MACA;IACA;EACA;EACAkL,OAAA,WAAAA,QAAA;IACA,KAAA7J,IAAA;IACA,KAAA8I,cAAA;EACA;EACAgB,aAAA,WAAAA,cAAA;IACA;IACA,SAAAnL,YAAA;MACA6K,YAAA,MAAA7K,YAAA;MACA,KAAAA,YAAA;IACA;EACA;AACA", "ignoreList": []}]}