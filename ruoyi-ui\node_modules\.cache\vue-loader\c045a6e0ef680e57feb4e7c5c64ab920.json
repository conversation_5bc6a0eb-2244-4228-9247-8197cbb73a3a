{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\agentComponent\\end.vue?vue&type=style&index=0&id=524c45d6&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\agentComponent\\end.vue", "mtime": 1753922059064}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8qQGltcG9ydCB1cmwoKSovDQouZW5kIHsNCiAgLmVuZC1saW5lLW9uZSB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgIGhlaWdodDogMzAwcHg7DQoNCiAgICBjb2xvcjogIzE3NmFkYjsNCiAgICBsZXR0ZXItc3BhY2luZzogMDsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgLmNsb3NpbmdSZW1hcmtzIHsNCiAgICAgIC5lbmQtaGVhZGxpbmUgew0KICAgICAgICBmb250LXdlaWdodDogNzAwOw0KICAgICAgICBmb250LXNpemU6IDM1cHg7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDE1cHg7DQogICAgICB9DQogICAgfQ0KICB9DQogIC5lbmQtbGluZS10d28gew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWV2ZW5seTsNCiAgICBwYWRkaW5nOiAwIDE1MHB4Ow0KICAgIC5lbmQtYnV0dG9uIHsNCiAgICAgIHdpZHRoOiAxNjRweDsNCiAgICAgIGhlaWdodDogNDVweDsNCiAgICAgIGJhY2tncm91bmQ6ICMxNzZhZGI7DQoNCiAgICAgIGNvbG9yOiAjZmZmOw0KICAgICAgZm9udC13ZWlnaHQ6IDcwMDsNCiAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["end.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "end.vue", "sourceRoot": "src/views/bidOpeningHall/agentComponent", "sourcesContent": ["<!-- 供应商开标结束 -->\r\n<template>\r\n  <div class=\"end\" \r\n  v-loading=\"loading\">\r\n    <div class=\"end-line-one\">\r\n      <div class=\"closingRemarks\">\r\n        <div class=\"end-headline\">\r\n          开标已结束\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"end-line-two\">\r\n      <el-button\r\n        :disabled=\"show\"\r\n        class=\"end-button\"\r\n        @click=\"bidOpeningEnds\"\r\n      >开标结束</el-button>\r\n      <el-button\r\n        class=\"end-button\"\r\n        style=\"background: #F5F5F5;color: #176ADB;\"\r\n        @click=\"printRecordSheet\"\r\n      >打印开标记录表</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\r\n//例如：import 《组件名称》 from '《组件路径》';\r\nimport { operationRecord, exportBidOpeningRecords } from \"@/api/onlineBidOpening/info\";\r\nimport { formatDateOption } from \"@/utils/index\";\r\nimport { listRecord } from \"@/api/operation/record\";\r\n\r\nexport default {\r\n  //import引入的组件需要注入到对象中才能使用\r\n  data() {\r\n    //这里存放数据\r\n    return {\r\n      loading: false,\r\n      queryParams: {\r\n        projectId: null,\r\n      },\r\n      show: true,\r\n    };\r\n  },\r\n  //监听属性 类似于data概念\r\n  computed: {},\r\n  //监控data中的数据变化\r\n  watch: {},\r\n  //方法集合\r\n  methods: {\r\n    // 开标结束\r\n    bidOpeningEnds() {\r\n      this.loading = true;\r\n      // 记录操作\r\n      operationRecord({\r\n        projectId: this.$route.query.projectId,\r\n        operationType: 6,\r\n        operationTime: formatDateOption(new Date()),\r\n        decryptionTime: formatDateOption(this.decryptionDeadline),\r\n      }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.buttonShow();\r\n          this.$emit(\"sendMessage\", \"end\");\r\n          this.$modal.msgSuccess(\"开标结束，可打印开标记录表\");\r\n          this.show = false;\r\n        } else {\r\n          this.$modal.msgwarning(response.msg);\r\n        }\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 打印开标记录表\r\n    printRecordSheet() {\r\n      this.queryParams.projectId = this.$route.query.projectId;\r\n      exportBidOpeningRecords(this.$route.query.projectId).then((result) => {\r\n          console.info(result)\r\n          if(result.code==200){\r\n            let downloads = document.createElement(\"a\");\r\n            downloads.href = \"/prod-api\"+result.data.attachments[0].filePath;\r\n            let noticeVersion = \"\";\r\n            downloads.download = result.data.projectName+'-开标记录表.'+result.data.attachments[0].fileSuffix;\r\n            document.body.appendChild(downloads);\r\n            downloads.click();\r\n            document.body.removeChild(downloads);\r\n            window.URL.revokeObjectURL(href);\r\n          }\r\n        })\r\n\r\n      // this.download(\r\n      //   \"bidding/info/exportBidOpeningRecords\",\r\n      //   {\r\n      //     ...this.queryParams,\r\n      //   },\r\n      //   `开标记录表_${new Date().getTime()}.pdf`\r\n      // );\r\n    },\r\n    // 按钮是否显示\r\n    buttonShow() {\r\n      listRecord({\r\n        projectId: this.$route.query.projectId,\r\n        operationType: 6,\r\n      }).then((response) => {\r\n        if (response.code == 200) {\r\n          \r\n          if (response.rows.length == 1) {\r\n            // if (this.$store.getters.agentBidOpenStatus == 5) {\r\n              this.show = true;\r\n            // }\r\n          }else{\r\n            this.show = false;\r\n          }\r\n        }\r\n      });\r\n    },\r\n  },\r\n  //生命周期 - 创建完成（可以访问当前this实例）\r\n  created() {},\r\n  //生命周期 - 挂载完成（可以访问DOM元素）\r\n  mounted() {\r\n    this.buttonShow();\r\n  },\r\n  beforeCreate() {}, //生命周期 - 创建之前\r\n  beforeMount() {}, //生命周期 - 挂载之前\r\n  beforeUpdate() {}, //生命周期 - 更新之前\r\n  updated() {}, //生命周期 - 更新之后\r\n  beforeDestroy() {}, //生命周期 - 销毁之前\r\n  destroyed() {}, //生命周期 - 销毁完成\r\n  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n/*@import url()*/\r\n.end {\r\n  .end-line-one {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 300px;\r\n\r\n    color: #176adb;\r\n    letter-spacing: 0;\r\n    text-align: center;\r\n    .closingRemarks {\r\n      .end-headline {\r\n        font-weight: 700;\r\n        font-size: 35px;\r\n        margin-bottom: 15px;\r\n      }\r\n    }\r\n  }\r\n  .end-line-two {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-evenly;\r\n    padding: 0 150px;\r\n    .end-button {\r\n      width: 164px;\r\n      height: 45px;\r\n      background: #176adb;\r\n\r\n      color: #fff;\r\n      font-weight: 700;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}