{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\technical\\one.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\technical\\one.vue", "mtime": 1753924220136}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBzdXBwbGllckluZm8sDQogIGFwcHJvdmFsUHJvY2VzcywNCiAgc2NvcmluZ0ZhY3RvcnMsDQogIGNoZWNrUmV2aWV3U3VtbWFyeSwNCiAgZmlsZXNCeUlkLA0KfSBmcm9tICJAL2FwaS9leHBlcnQvcmV2aWV3IjsNCmltcG9ydCB7IGdldERldGFpbEJ5UHN4eCB9IGZyb20gIkAvYXBpL2V2YWx1YXRpb24vZGV0YWlsLyI7DQppbXBvcnQgeyBlZGl0RXZhbEV4cGVydFNjb3JlSW5mbyB9IGZyb20gIkAvYXBpL2V2YWx1YXRpb24vZXhwZXJ0U3RhdHVzIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBvcHRpb25zOiBbXSwNCiAgICAgIHNjb3JpbmdTeXN0ZW06IFtdLA0KICAgICAgc2VsZWN0Tm9kZToge30sDQogICAgICBzdXBwbGllcjogIiIsDQogICAgICBzZWxlY3RTdXBwbGllcjoge30sDQogICAgICBleHBlcnRJbmZvOiB7fSwNCiAgICAgIGRlZmF1bHRSYXRpbmdBcnJheToge30sDQogICAgICBmaWxlOiB7fSwNCiAgICAgIHJlc3BvbnNlU2hvdzogZmFsc2UsDQogICAgICBwcm9jdXJlbWVudFNob3c6IGZhbHNlLA0KICAgICAgZG91YmxlOiBmYWxzZSwNCiAgICAgIGZhY3Rvckxpc3Q6IFtdLA0KICAgICAgZW50RG9jUmVzcG9uc2VQYWdlOiB7fSwNCiAgICAgIGZhY3RvcnNQYWdlOiB7fSwNCiAgICAgIGJpZGRlckZhY3Rvcjoge30sDQoNCiAgICAgIHJlc3BvbnNlUGRmOiBudWxsLA0KICAgICAgcHJvY3VyZW1lbnRQZGY6IG51bGwsDQogICAgICBjdXJyZW50TWF4U2NvcmU6IG51bGwsDQoNCiAgICAgIC8vIOaMiemSrueKtuaAgeeuoeeQhg0KICAgICAgYWN0aXZlQnV0dG9uOiAncmVzcG9uc2UnLCAvLyDlvZPliY3mv4DmtLvnmoTmjInpkq7vvJoncmVzcG9uc2Un44CBJ3Byb2N1cmVtZW50J+OAgSdjb250cmFzdCcNCg0KICAgICAgLy8g6YeH6LSt5paH5Lu255u45YWz5pWw5o2uDQogICAgICBlbnREb2NQcm9jdXJlbWVudFBhZ2U6IHt9LCAvLyDph4fotK3mlofku7bpobXnoIHkv6Hmga8NCiAgICAgIHBhZ2VQcm9jdXJlbWVudDogW10sIC8vIOmHh+i0reaWh+S7tueahOivhOWIhumhuQ0KCSAgICBhdHRhY2htZW50c0xpc3Q6W10sIC8vIOaWh+S7tuWIl+ihqA0KCSAgICANCgkgICAgLy8gUERG5riy5p+T54q25oCB566h55CGDQoJICAgIHJlc3BvbnNlUGRmUmVuZGVyZWQ6IGZhbHNlLCAvLyDlk43lupTmlofku7ZQREbmmK/lkKbmuLLmn5PlrozmiJANCgkgICAgcHJvY3VyZW1lbnRQZGZSZW5kZXJlZDogZmFsc2UsIC8vIOmHh+i0reaWh+S7tlBERuaYr+WQpua4suafk+WujOaIkA0KDQogICAgICBzcmNMaXN0OiBbIi9ldmFsdXRpb24vaGVscC5qcGciXSwNCgkgICAgDQoJICAgIC8vIOaCrOWBnOeKtuaAgeeuoeeQhg0KCSAgICBob3ZlcmVkRmFjdG9yTm9kZTogbnVsbCwgLy8g5oKs5YGc5pe255qE6K+E5YiG6aG5DQoJICAgIHRvb2x0aXBUaW1lcjogbnVsbCwgLy8g5oKs5rWu5qGG5pi+56S65a6a5pe25ZmoDQogICAgfTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKg0KICAgICAqIOmZkOWItui+k+WFpeahhuWPquiDvei+k+WFpeaVsOWtl+WSjOWwj+aVsOeCuQ0KICAgICAqIEBwYXJhbSB7RXZlbnR9IGV2ZW50IC0g6ZSu55uY5LqL5Lu2DQogICAgICovDQogICAgb25seU51bWJlcihldmVudCkgew0KICAgICAgLy8g6I635Y+W5oyJ6ZSu55qE5a2X56ym56CBDQogICAgICBjb25zdCBjaGFyQ29kZSA9IGV2ZW50LndoaWNoIHx8IGV2ZW50LmtleUNvZGU7DQoNCiAgICAgIC8vIOWFgeiuuOeahOWtl+espu+8muaVsOWtlyg0OC01NynjgIHlsI/mlbDngrkoNDYp44CB6YCA5qC8KDgp44CB5Yig6ZmkKDQ2KeOAgVRhYig5KeOAgUVudGVyKDEzKeOAgeaWueWQkemUrigzNy00MCkNCiAgICAgIGlmICgNCiAgICAgICAgKGNoYXJDb2RlID49IDQ4ICYmIGNoYXJDb2RlIDw9IDU3KSB8fCAvLyDmlbDlrZcgMC05DQogICAgICAgIGNoYXJDb2RlID09PSA0NiB8fCAvLyDlsI/mlbDngrkNCiAgICAgICAgY2hhckNvZGUgPT09IDggfHwgIC8vIOmAgOagvOmUrg0KICAgICAgICBjaGFyQ29kZSA9PT0gOSB8fCAgLy8gVGFi6ZSuDQogICAgICAgIGNoYXJDb2RlID09PSAxMyB8fCAvLyBFbnRlcumUrg0KICAgICAgICAoY2hhckNvZGUgPj0gMzcgJiYgY2hhckNvZGUgPD0gNDApIC8vIOaWueWQkemUrg0KICAgICAgKSB7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfQ0KDQogICAgICAvLyDpmLvmraLlhbbku5blrZfnrKbovpPlhaUNCiAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7DQogICAgICByZXR1cm4gZmFsc2U7DQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOWkhOeQhuWIhuaVsOi+k+WFpe+8jOehruS/neWPquiDvei+k+WFpeacieaViOeahOaVsOWtlw0KICAgICAqIEBwYXJhbSB7c3RyaW5nfSBpdGVtSWQgLSDor4TkvLDpoblJRA0KICAgICAqIEBwYXJhbSB7c3RyaW5nfSB2YWx1ZSAtIOi+k+WFpeWAvA0KICAgICAqLw0KICAgIGhhbmRsZVNjb3JlSW5wdXQoaXRlbUlkLCB2YWx1ZSkgew0KICAgICAgLy8g56e76Zmk6Z2e5pWw5a2X5a2X56ym77yI5L+d55WZ5bCP5pWw54K577yJDQogICAgICBsZXQgY2xlYW5WYWx1ZSA9IHZhbHVlLnJlcGxhY2UoL1teXGQuXS9nLCAnJyk7DQoNCiAgICAgIC8vIOehruS/neWPquacieS4gOS4quWwj+aVsOeCuQ0KICAgICAgY29uc3QgcGFydHMgPSBjbGVhblZhbHVlLnNwbGl0KCcuJyk7DQogICAgICBpZiAocGFydHMubGVuZ3RoID4gMikgew0KICAgICAgICBjbGVhblZhbHVlID0gcGFydHNbMF0gKyAnLicgKyBwYXJ0cy5zbGljZSgxKS5qb2luKCcnKTsNCiAgICAgIH0NCg0KICAgICAgLy8g6ZmQ5Yi25bCP5pWw54K55ZCO5pyA5aSaMuS9jQ0KICAgICAgaWYgKHBhcnRzLmxlbmd0aCA9PT0gMiAmJiBwYXJ0c1sxXS5sZW5ndGggPiAyKSB7DQogICAgICAgIGNsZWFuVmFsdWUgPSBwYXJ0c1swXSArICcuJyArIHBhcnRzWzFdLnN1YnN0cmluZygwLCAyKTsNCiAgICAgIH0NCg0KICAgICAgLy8g5pu05paw5YC8DQogICAgICB0aGlzLmRlZmF1bHRSYXRpbmdBcnJheVtpdGVtSWRdLnN0YXRlID0gY2xlYW5WYWx1ZTsNCg0KICAgICAgLy8g6LCD55So5Y6f5pyJ55qE6aqM6K+B5pa55rOVDQogICAgICB0aGlzLnZhbGlkYXRlU2NvcmUoaXRlbUlkLCBjbGVhblZhbHVlKTsNCiAgICB9LA0KDQogICAgaW5pdCgpIHsNCiAgICAgIGNvbnN0IGV4cGVydEluZm8gPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJleHBlcnRJbmZvIikpOw0KICAgICAgdGhpcy5lbnREb2NSZXNwb25zZVBhZ2UgPSBKU09OLnBhcnNlKA0KICAgICAgICBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZW50RG9jUmVzcG9uc2VQYWdlIikNCiAgICAgICk7DQogICAgICAvLyDliJ3lp4vljJbph4fotK3mlofku7bpobXnoIHkv6Hmga8NCiAgICAgIHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZW50RG9jUHJvY3VyZW1lbnRQYWdlIikpOw0KICAgICAgc3VwcGxpZXJJbmZvKHsgcHJvamVjdElkOiB0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQgfSkudGhlbigNCiAgICAgICAgKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICB0aGlzLm9wdGlvbnMgPSByZXNwb25zZS5yb3dzLmZpbHRlcihpdGVtID0+IGl0ZW0uaXNBYmFuZG9uZWRCaWQgPT0gMCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhyZXNwb25zZS5tc2cpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgKTsNCiAgICAgIGFwcHJvdmFsUHJvY2Vzcyh0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQsIGV4cGVydEluZm8ucmVzdWx0SWQpLnRoZW4oDQogICAgICAgIChyZXNwb25zZSkgPT4gew0KICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09IDIwMCkgew0KCQkJCQkJLy8g5paH5Lu25YiX6KGoDQoJICAgICAgICAgIHRoaXMuYXR0YWNobWVudHNMaXN0ID0gcmVzcG9uc2UuZGF0YS5idXNpVGVuZGVyTm90aWNlLmF0dGFjaG1lbnRzLmZpbHRlcihpdGVtID0+IGl0ZW0uZmlsZVR5cGUgPT0gIjAiKTsNCgkJCQkJCQ0KICAgICAgICAgICAgdGhpcy5zY29yaW5nU3lzdGVtID0NCiAgICAgICAgICAgICAgcmVzcG9uc2UuZGF0YS5zY29yaW5nTWV0aG9kVWluZm8uc2NvcmluZ01ldGhvZEl0ZW1zLmZpbmQoDQogICAgICAgICAgICAgICAgKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgICAgIHJldHVybiAoDQogICAgICAgICAgICAgICAgICAgIGl0ZW0uc2NvcmluZ01ldGhvZEl0ZW1JZCA9PQ0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRyb3V0ZS5xdWVyeS5zY29yaW5nTWV0aG9kSXRlbUlkDQogICAgICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgKTsNCiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKA0KICAgICAgICAgICAgICAiZXZhbFByb2plY3RFdmFsdWF0aW9uUHJvY2VzcyIsDQogICAgICAgICAgICAgIEpTT04uc3RyaW5naWZ5KHRoaXMuc2NvcmluZ1N5c3RlbS5ldmFsUHJvamVjdEV2YWx1YXRpb25Qcm9jZXNzKQ0KICAgICAgICAgICAgKTsNCiAgICAgICAgICAgIC8vIFRPRE8g6aaW5YWI55Sf5oiQ6ZW/5bqm5Li6dGhpcy5zY29yaW5nU3lzdGVtLmxlbmd0aOeahOaVsOe7hO+8jOe7k+aehOS4untzdGF0Ze+8mmZhbHNl77yMcmVhc29u77ya4oCc4oCdfQ0KICAgICAgICAgICAgdGhpcy5kZWZhdWx0UmF0aW5nQXJyYXkgPSB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zLnJlZHVjZSgNCiAgICAgICAgICAgICAgKGFjYywgXywgaW5kZXgpID0+IHsNCiAgICAgICAgICAgICAgICBhY2NbdGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtc1tpbmRleF0uZW50TWV0aG9kSXRlbUlkXSA9IHsNCiAgICAgICAgICAgICAgICAgIHN0YXRlOiBudWxsLA0KICAgICAgICAgICAgICAgICAgcmVhc29uOiAiIiwNCiAgICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCJzY29yaW5nU3lzdGVtIiwgdGhpcy5zY29yaW5nU3lzdGVtKTsNCiAgICAgICAgICAgICAgICByZXR1cm4gYWNjOw0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICB7fQ0KICAgICAgICAgICAgKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2dhZS53YXJuaW5nKHJlc3BvbnNlLm1zZyk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICApOw0KICAgICAgZmlsZXNCeUlkKHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5maWxlID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgICBpZiAodGhpcy5maWxlLnRlbmRlck5vdGljZUZpbGVQYXRoICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgdGhpcy5wcm9jdXJlbWVudFBkZiA9IHRoaXMuZmlsZS50ZW5kZXJOb3RpY2VGaWxlUGF0aDsNCiAgICAgICAgICB9DQogICAgICAgICAgLy8gaWYgKHRoaXMuZmlsZS5maWxlICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAgIC8vICAgdGhpcy5yZXNwb25zZVBkZiA9IHRoaXMuZmlsZS5maWxlWzBdOw0KICAgICAgICAgIC8vIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcocmVzcG9uc2UubXNnKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICAvLyDliJ3lp4vljJbkuJPlrrbkv6Hmga8NCiAgICAgIHRoaXMuaW5pdEV4cGVydEluZm8oKTsNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog5Yid5aeL5YyW5LiT5a625L+h5oGvDQogICAgICovDQogICAgaW5pdEV4cGVydEluZm8oKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBpdGVtU3RyaW5nID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oImV4cGVydEluZm8iKTsNCiAgICAgICAgaWYgKGl0ZW1TdHJpbmcpIHsNCiAgICAgICAgICB0aGlzLmV4cGVydEluZm8gPSBKU09OLnBhcnNlKGl0ZW1TdHJpbmcpOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCLkuJPlrrbkv6Hmga/lt7LliJ3lp4vljJYiLCB0aGlzLmV4cGVydEluZm8pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUud2FybigibG9jYWxTdG9yYWdl5Lit5pyq5om+5YiwZXhwZXJ0SW5mbyIpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLliJ3lp4vljJbkuJPlrrbkv6Hmga/lpLHotKU6IiwgZXJyb3IpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBoYW5kbGVDaGFuZ2UodmFsdWUpIHsNCiAgICAgIGlmKE9iamVjdC5rZXlzKHRoaXMuc2VsZWN0U3VwcGxpZXIpLmxlbmd0aCAhPSAwKXsNCiAgICAgICAgdGhpcy50bXBTYXZlKCk7DQogICAgICB9DQogICAgICB0aGlzLnNlbGVjdFN1cHBsaWVyID0gdGhpcy5vcHRpb25zLmZpbmQoKGl0ZW0pID0+IHsNCiAgICAgICAgcmV0dXJuIGl0ZW0uYmlkZGVyTmFtZSA9PSB2YWx1ZTsNCiAgICAgIH0pOw0KDQogICAgICAvLyDmoLnmja5iaWRkZXJpZOiOt+WPluS+m+W6lOWVhuWboOe0oOWPiuWFtuWvueW6lOmhteeggQ0KICAgICAgdGhpcy5iaWRkZXJGYWN0b3IgPSB0aGlzLmZhY3RvcnNQYWdlW3RoaXMuc2VsZWN0U3VwcGxpZXIuYmlkZGVySWRdOw0KDQogICAgICBjb25zdCBkYXRhID0gew0KICAgICAgICBleHBlcnRSZXN1bHRJZDogdGhpcy5leHBlcnRJbmZvLnJlc3VsdElkLA0KICAgICAgICBwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwNCiAgICAgICAgc2NvcmluZ01ldGhvZEl0ZW1JZDogdGhpcy4kcm91dGUucXVlcnkuc2NvcmluZ01ldGhvZEl0ZW1JZCwNCiAgICAgIH07DQogICAgICBnZXREZXRhaWxCeVBzeHgoZGF0YSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5mYWN0b3JMaXN0ID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgICBjb25zdCBmYWN0b3IgPSB0aGlzLmZhY3Rvckxpc3QuZmluZCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgcmV0dXJuIGl0ZW0uYmlkZGVyTmFtZSA9PSB2YWx1ZTsNCiAgICAgICAgICB9KS5ldmFsRXhwZXJ0RXZhbHVhdGlvbkRldGFpbHM7DQogICAgICAgICAgaWYgKGZhY3RvciAhPSBudWxsKSB7DQogICAgICAgICAgICBmYWN0b3IubWFwKChpdGVtKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuZGVmYXVsdFJhdGluZ0FycmF5W2l0ZW0uc2NvcmluZ01ldGhvZFVpdGVtSWRdLnJlYXNvbiA9DQogICAgICAgICAgICAgICAgaXRlbS5ldmFsdWF0aW9uUmVtYXJrOw0KICAgICAgICAgICAgICB0aGlzLmRlZmF1bHRSYXRpbmdBcnJheVtpdGVtLnNjb3JpbmdNZXRob2RVaXRlbUlkXS5zdGF0ZSA9DQogICAgICAgICAgICAgICAgaXRlbS5ldmFsdWF0aW9uUmVzdWx0Ow0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIE9iamVjdC5rZXlzKHRoaXMuZGVmYXVsdFJhdGluZ0FycmF5KS5mb3JFYWNoKChrZXkpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy5kZWZhdWx0UmF0aW5nQXJyYXlba2V5XS5zdGF0ZSA9IG51bGw7DQogICAgICAgICAgICAgIHRoaXMuZGVmYXVsdFJhdGluZ0FycmF5W2tleV0ucmVhc29uID0gIiI7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKHJlc3BvbnNlLm1zZyk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgdGhpcy5zaG93UmVzcG9uc2VGaWxlKCk7DQogICAgICAvLyDph43nva7mnIDlpKfliIbmlbDlgLwNCiAgICAgIC8vIHRoaXMuY3VycmVudE1heFNjb3JlID0gbnVsbDsNCiAgICB9LA0KCSAgDQoJICANCiAgICB2YWxpZGF0ZVNjb3JlKGl0ZW1JZCwgZXZlbnQpIHsNCiAgICAgIGNvbnN0IGlucHV0VmFsdWUgPSBwYXJzZUZsb2F0KGV2ZW50KTsNCiAgICAgIGNvbnNvbGUubG9nKCJpbnB1dFZhbHVlIiwgaW5wdXRWYWx1ZSk7DQogICAgICANCiAgICAgIC8vIOiOt+WPluW9k+WJjeivhOWIhumhueeahOacgOWkp+WIhuWAvA0KICAgICAgY29uc3QgY3VycmVudEl0ZW0gPSB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zLmZpbmQoaXRlbSA9PiBpdGVtLmVudE1ldGhvZEl0ZW1JZCA9PT0gaXRlbUlkKTsNCiAgICAgIGxldCBtYXhTY29yZSA9IG51bGw7DQogICAgICANCiAgICAgIGlmIChjdXJyZW50SXRlbSkgew0KICAgICAgICAvLyDlpoLmnpzmnInliIbmlbDmjKHkvY3vvIzkvb/nlKjmjKHkvY3kuK3nmoTmnIDlpKflgLwNCiAgICAgICAgaWYgKGN1cnJlbnRJdGVtLnNjb3JlTGV2ZWwgJiYgY3VycmVudEl0ZW0uc2NvcmVMZXZlbC5sZW5ndGggPiAwKSB7DQogICAgICAgICAgY29uc3Qgc2NvcmVMZXZlbHMgPSBjdXJyZW50SXRlbS5zY29yZUxldmVsLnNwbGl0KCcsJykubWFwKGl0ZW0gPT4gcGFyc2VGbG9hdChpdGVtLnRyaW0oKSkpLmZpbHRlcihpdGVtID0+ICFpc05hTihpdGVtKSk7DQogICAgICAgICAgaWYgKHNjb3JlTGV2ZWxzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIG1heFNjb3JlID0gTWF0aC5tYXgoLi4uc2NvcmVMZXZlbHMpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlkKbliJnkvb/nlKjphY3nva7nmoTmnIDlpKfliIblgLwNCiAgICAgICAgICBtYXhTY29yZSA9IHBhcnNlRmxvYXQoY3VycmVudEl0ZW0uc2NvcmUpOw0KICAgICAgICB9DQogICAgICB9DQogICAgICANCiAgICAgIGNvbnNvbGUubG9nKCJtYXhTY29yZSIsIG1heFNjb3JlKTsNCg0KICAgICAgaWYgKCFpc05hTihpbnB1dFZhbHVlKSAmJiBtYXhTY29yZSAhPT0gbnVsbCkgew0KICAgICAgICBpZiAoaW5wdXRWYWx1ZSA+IG1heFNjb3JlKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGDovpPlhaXliIbmlbDkuI3og73otoXov4cke21heFNjb3JlfeWIhu+8jOivt+mHjeaWsOi+k+WFpWApOw0KICAgICAgICAgIC8vIOWwhui+k+WFpeWAvOmZkOWItuS4uuacgOWkp+WIhuaVsOWAvA0KICAgICAgICAgIHRoaXMuZGVmYXVsdFJhdGluZ0FycmF5W2l0ZW1JZF0uc3RhdGUgPSAiIjsNCiAgICAgICAgfSBlbHNlIGlmIChpbnB1dFZhbHVlIDwgMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6L6T5YWl5YiG5pWw5LiN6IO95bCP5LqOMOWIhiIpOw0KICAgICAgICAgIHRoaXMuZGVmYXVsdFJhdGluZ0FycmF5W2l0ZW1JZF0uc3RhdGUgPSAiIjsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgc2hvd1Jlc3BvbnNlRmlsZSgpIHsNCiAgICAgIGlmIChPYmplY3Qua2V5cyh0aGlzLnNlbGVjdFN1cHBsaWVyKS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nkvpvlupTllYYiKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuYWN0aXZlQnV0dG9uID0gJ3Jlc3BvbnNlJzsgLy8g6K6+572u5b2T5YmN5r+A5rS75oyJ6ZKuDQogICAgICAgIHRoaXMuZG91YmxlID0gZmFsc2U7DQogICAgICAgIHRoaXMucHJvY3VyZW1lbnRTaG93ID0gZmFsc2U7DQogICAgICAgIHRoaXMucmVzcG9uc2VTaG93ID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5yZXNwb25zZVBkZiA9IHRoaXMuZmlsZS5maWxlW3RoaXMuc2VsZWN0U3VwcGxpZXIuYmlkZGVySWRdOw0KICAgICAgfQ0KICAgIH0sDQogICAgZmlsZUNvbnRyYXN0KCkgew0KICAgICAgaWYgKE9iamVjdC5rZXlzKHRoaXMuc2VsZWN0U3VwcGxpZXIpLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeS+m+W6lOWVhiIpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5hY3RpdmVCdXR0b24gPSAnY29udHJhc3QnOyAvLyDorr7nva7lvZPliY3mv4DmtLvmjInpkq4NCiAgICAgICAgdGhpcy5kb3VibGUgPSB0cnVlOw0KICAgICAgICB0aGlzLnByb2N1cmVtZW50U2hvdyA9IHRydWU7DQogICAgICAgIHRoaXMucmVzcG9uc2VTaG93ID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5yZXNwb25zZVBkZiA9IHRoaXMuZmlsZS5maWxlW3RoaXMuc2VsZWN0U3VwcGxpZXIuYmlkZGVySWRdOw0KICAgICAgfQ0KICAgIH0sDQogICAgc2hvd0luZm8oaXRlbSkgew0KCSAgICAvLyDmo4Dmn6VQREbmmK/lkKbmuLLmn5PlrozmiJANCgkgICAgaWYgKCF0aGlzLmNhbkp1bXBUb1BhZ2UoKSkgew0KCQkgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCJQREbpobXpnaLmraPlnKjmuLLmn5PkuK3vvIzor7fnqI3lgJnlho3or5UiKTsNCgkJICAgIHJldHVybjsNCgkgICAgfQ0KCQkJDQogICAgICB0aGlzLnNlbGVjdE5vZGUgPSBpdGVtOw0KDQogICAgICAvLyDlpoLmnpzlj6rmmL7npLrph4fotK3mlofku7bvvIzkvb/nlKjph4fotK3mlofku7bpobXnoIHkv6Hmga8NCiAgICAgIGlmICh0aGlzLnByb2N1cmVtZW50U2hvdyAmJiAhdGhpcy5yZXNwb25zZVNob3cpIHsNCgkgICAgICBpZiAoIXRoaXMucHJvY3VyZW1lbnRQZGZSZW5kZXJlZCkgew0KCQkgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIumHh+i0reaWh+S7tuato+WcqOa4suafk+S4re+8jOivt+eojeWAmeWGjeivlSIpOw0KCQkgICAgICByZXR1cm47DQoJICAgICAgfQ0KCQkJCQ0KICAgICAgICBpZiAoaXRlbS5qdW1wVG9QYWdlKSB7DQogICAgICAgICAgdGhpcy4kcmVmcy5wcm9jdXJlbWVudC5za2lwUGFnZShpdGVtLmp1bXBUb1BhZ2UpOw0KICAgICAgICB9IGVsc2UgaWYgKHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlICYmIHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlW2l0ZW0uaXRlbU5hbWVdKSB7DQogICAgICAgICAgdGhpcy4kcmVmcy5wcm9jdXJlbWVudC5za2lwUGFnZSh0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZVtpdGVtLml0ZW1OYW1lXSk7DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDlpoLmnpzmmL7npLrlk43lupTmlofku7bmiJblr7nmr5TmqKHlvI/vvIzpnIDopoHpgInmi6nkvpvlupTllYYNCiAgICAgIGlmIChPYmplY3Qua2V5cyh0aGlzLmJpZGRlckZhY3RvcikubGVuZ3RoICE9IDApIHsNCiAgICAgICAgLy8g6Lez6L2s5Yiw5ZON5bqU5paH5Lu25a+55bqU6aG156CBDQogICAgICAgIGlmICh0aGlzLnJlc3BvbnNlU2hvdyAmJiB0aGlzLiRyZWZzLnJlc3BvbnNlKSB7DQoJICAgICAgICBpZiAoIXRoaXMucmVzcG9uc2VQZGZSZW5kZXJlZCkgew0KCQkgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi5ZON5bqU5paH5Lu25q2j5Zyo5riy5p+T5Lit77yM6K+356iN5YCZ5YaN6K+VIik7DQoJCSAgICAgICAgcmV0dXJuOw0KCSAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuJHJlZnMucmVzcG9uc2Uuc2tpcFBhZ2UoDQogICAgICAgICAgICB0aGlzLmJpZGRlckZhY3Rvclt0aGlzLnNlbGVjdE5vZGUuaXRlbU5hbWVdDQogICAgICAgICAgKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOi3s+i9rOWIsOmHh+i0reaWh+S7tuWvueW6lOmhteeggQ0KICAgICAgICBpZiAodGhpcy5wcm9jdXJlbWVudFNob3cgJiYgdGhpcy4kcmVmcy5wcm9jdXJlbWVudCkgew0KCSAgICAgICAgaWYgKCF0aGlzLnByb2N1cmVtZW50UGRmUmVuZGVyZWQpIHsNCgkJICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIumHh+i0reaWh+S7tuato+WcqOa4suafk+S4re+8jOivt+eojeWAmeWGjeivlSIpOw0KCQkgICAgICAgIHJldHVybjsNCgkgICAgICAgIH0NCgkJCQkJDQogICAgICAgICAgLy8g5Zyo5a+55q+U5qih5byP5LiL77yM6YeH6LSt5paH5Lu25bqU6K+l6Lez6L2s5Yiw6YeH6LSt5paH5Lu255qE5a+55bqU6aG156CB77yM6ICM5LiN5piv5L6b5bqU5ZWG55qE6aG156CBDQogICAgICAgICAgaWYgKHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlICYmIHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlW2l0ZW0uaXRlbU5hbWVdKSB7DQogICAgICAgICAgICB0aGlzLiRyZWZzLnByb2N1cmVtZW50LnNraXBQYWdlKHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlW2l0ZW0uaXRlbU5hbWVdKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g5Zyo5a+55q+U5qih5byP5LiL77yM5aaC5p6c5rKh5pyJ6YeH6LSt5paH5Lu26aG156CB5L+h5oGv77yM5YiZ5Y+q6Lez6L2s5ZON5bqU5paH5Lu255qE6aG156CB77yM5LiN6Lez6L2s6YeH6LSt5paH5Lu2DQogICAgICAgICAgICAvLyDov5nmoLflj6/ku6Xpgb/lhY3ph4fotK3mlofku7blkozlk43lupTmlofku7bmmL7npLrkuI3lkIznmoTlhoXlrrnpgKDmiJDmt7fmt4YNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAvLyDojrflj5blvZPliY3pobnnm67nmoTmnIDlpKfliIbmlbDlgLzvvIzlgYforr5zY29yZUxldmVs5Lit56ys5LiA5Liq5YC85Li65pyA5aSn5YC877yI5Y+v5qC55o2u5a6e6ZmF6KeE5YiZ6LCD5pW077yJDQogICAgICAgIC8vIGNvbnN0IG1heFNjb3JlID0gaXRlbS5zY29yZTsNCiAgICAgICAgLy8gY29uc29sZS5sb2coIuatpOmhueebruacgOWkp+WIhuWAvOaYr++8miIrbWF4U2NvcmUpOw0KICAgICAgICAvLyB0aGlzLmN1cnJlbnRNYXhTY29yZSA9IG1heFNjb3JlOyAvLyDlsIbmnIDlpKfliIbmlbDlgLzlrZjlgqjliLDlrp7kvovlj5jph4/kuK3vvIzmlrnkvr/lkI7nu63moKHpqozkvb/nlKgNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35YWI6YCJ5oup5L6b5bqU5ZWGIik7DQogICAgICB9DQogICAgfSwNCiAgICBpbml0RGVmYXVsdFJhdGluZ0FycmF5KCl7DQogICAgICBPYmplY3Qua2V5cyh0aGlzLmRlZmF1bHRSYXRpbmdBcnJheSkuZm9yRWFjaCgoa2V5KSA9PiB7DQogICAgICAgIHRoaXMuZGVmYXVsdFJhdGluZ0FycmF5W2tleV0uc3RhdGUgPSBudWxsOw0KICAgICAgICB0aGlzLmRlZmF1bHRSYXRpbmdBcnJheVtrZXldLnJlYXNvbiA9ICIiOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOagoemqjOaJgOacieivhOWIhumhueaYr+WQpuWhq+WGmeWujOaVtA0KICAgICAqIEByZXR1cm5zIHtib29sZWFufSDmmK/lkKblhajpg6jloavlhpkNCiAgICAgKi8NCiAgICB2YWxpZGF0ZUFsbFJhdGluZ3MoKSB7DQogICAgICBmb3IgKGNvbnN0IGl0ZW0gb2YgdGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtcykgew0KICAgICAgICBjb25zdCBzdGF0ZSA9IHRoaXMuZGVmYXVsdFJhdGluZ0FycmF5W2l0ZW0uZW50TWV0aG9kSXRlbUlkXS5zdGF0ZTsNCg0KICAgICAgICAvLyDor4TliIbnu5PmnpzmnKrloavlhpkNCiAgICAgICAgaWYgKHN0YXRlID09PSBudWxsIHx8IHN0YXRlID09PSAnJyB8fCBzdGF0ZSA9PT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgLy8gdGhpcy4kbWVzc2FnZS53YXJuaW5nKGDor7floavlhpnor4TliIbpobnvvJoke2l0ZW0uaXRlbU5hbWV9IOeahOivhOWIhue7k+aenGApOw0KICAgICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5a+55LqO5YiG5pWw6K+E5YiG77yM5qOA5p+l5piv5ZCm5Li65pyJ5pWI5pWw5YC8DQogICAgICAgIGlmICghaXRlbS5zY29yZUxldmVsIHx8IGl0ZW0uc2NvcmVMZXZlbC5sZW5ndGggPT09IDAgfHwgaXRlbS5zY29yZUxldmVsID09PSBudWxsIHx8IGl0ZW0uc2NvcmVMZXZlbCA9PT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgY29uc3Qgc2NvcmUgPSBwYXJzZUZsb2F0KHN0YXRlKTsNCiAgICAgICAgICBpZiAoaXNOYU4oc2NvcmUpIHx8IHNjb3JlIDwgMCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGAke2l0ZW0uaXRlbU5hbWV955qE6K+E5YiG5b+F6aG75piv5pyJ5pWI55qE5pWw5YC85LiU5LiN6IO95bCP5LqOMGApOw0KICAgICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICAgIH0NCiAgICAgICAgICAvLyDmo4Dmn6XliIbmlbDmmK/lkKbotoXov4fmnIDlpKflgLwNCiAgICAgICAgICBjb25zdCBtYXhTY29yZSA9IHRoaXMuZ2V0TWF4U2NvcmUoaXRlbSk7DQogICAgICAgICAgaWYgKHNjb3JlID4gbWF4U2NvcmUpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhgJHtpdGVtLml0ZW1OYW1lfeeahOivhOWIhuS4jeiDvei2hei/hyR7bWF4U2NvcmV95YiGYCk7DQogICAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOWvueS6juacieaMoeS9jeeahOivhOWIhu+8jOajgOafpeaYr+WQpuWcqOWFgeiuuOeahOaMoeS9jeiMg+WbtOWGhQ0KICAgICAgICAgIGNvbnN0IHNjb3JlTGV2ZWxzID0gaXRlbS5zY29yZUxldmVsLnNwbGl0KCcsJykubWFwKGxldmVsID0+IGxldmVsLnRyaW0oKSk7DQogICAgICAgICAgaWYgKCFzY29yZUxldmVscy5pbmNsdWRlcyhzdGF0ZS50b1N0cmluZygpKSkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGAke2l0ZW0uaXRlbU5hbWV955qE6K+E5YiG5b+F6aG76YCJ5oup5oyH5a6a55qE5oyh5L2N77yaJHtpdGVtLnNjb3JlTGV2ZWx9YCk7DQogICAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gdHJ1ZTsNCiAgICB9LA0KDQogICAgdG1wU2F2ZSgpew0KICAgICAgY29uc29sZS5sb2coIi0tLS0tLS3lvIDlp4vkv53lrZjor4TlrqHnu5PmnpwtLS0tLS0tLS0tLS0tLS0tIik7DQoNCiAgICAgIC8vIOWFiOagoemqjOaJgOacieivhOWIhumhueaYr+WQpuWhq+WGmeWujOaVtA0KICAgICAgaWYgKCF0aGlzLnZhbGlkYXRlQWxsUmF0aW5ncygpKSB7DQogICAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoeyBjb2RlOiAwLCBzdWNjZXNzOiBmYWxzZSB9KTsgLy8g5qCh6aqM5aSx6LSlDQogICAgICB9DQoNCiAgICAgIHZhciByYXRpbmdBcnJheSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5kZWZhdWx0UmF0aW5nQXJyYXkpKTsNCg0KICAgICAgdmFyIGRhdGEgPSBbXTsNCiAgICAgIGZvciAobGV0IGluZGV4ID0gMDsgaW5kZXggPCB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zLmxlbmd0aDsgaW5kZXgrKykgew0KICAgICAgICBjb25zdCBpdGVtID0gdGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtc1tpbmRleF07DQogICAgICAgIGNvbnN0IGl0ZW1JZCA9IGl0ZW0uZW50TWV0aG9kSXRlbUlkOw0KICAgICAgICAvLyDojrflj5blvZPliY3pobnlr7nlupTnmoTor4TliIbnu5PmnpwNCiAgICAgICAgY29uc3QgZXZhbHVhdGlvblJlc3VsdCA9IHJhdGluZ0FycmF5W2l0ZW1JZF0uc3RhdGU7DQogICAgICAgIGlmIChldmFsdWF0aW9uUmVzdWx0ID09PSBudWxsIHx8IGV2YWx1YXRpb25SZXN1bHQgPT09ICIiKSB7DQogICAgICAgICAgLy8g5aaC5p6c6K+E5YiG57uT5p6c5Li656m677yM5YiZ5LiN5L+d5a2Y5q2k5p2h5L+h5oGvDQogICAgICAgICAgY29uc29sZS5sb2coIi0tLS0tLS3or4TliIbnu5PmnpzkuLrnqbrvvIzkuI3kv53lrZjmraTmnaHkv6Hmga8tLS0tLS0tLS0tLS0tLS0tIik7DQogICAgICAgICAgY29udGludWU7DQogICAgICAgIH0NCiAgICAgICAgICAvLyDojrflj5blvZPliY3pobnlr7nlupTnmoTor4TliIblpIfms6jvvIzov5vooYzpnZ7nqbrliKTmlq3vvIzkuLrnqbrliJnotYvkuojpu5jorqTlgLzvvIjov5nph4zorr7kuLrnqbrlrZfnrKbkuLLvvIkNCiAgICAgICAgICBjb25zdCBldmFsdWF0aW9uUmVtYXJrID0gcmF0aW5nQXJyYXlbaXRlbUlkXS5yZWFzb24gfHwgIiI7DQogICAgICAgICAgZGF0YS5wdXNoKHsNCiAgICAgICAgICAgIHNjb3JpbmdNZXRob2RVaXRlbUlkOiBpdGVtSWQsDQogICAgICAgICAgICBleHBlcnRSZXN1bHRJZDogdGhpcy5leHBlcnRJbmZvLnJlc3VsdElkLA0KICAgICAgICAgICAgZW50SWQ6IHRoaXMuc2VsZWN0U3VwcGxpZXIuYmlkZGVySWQsDQogICAgICAgICAgICBldmFsdWF0aW9uUmVzdWx0OiBldmFsdWF0aW9uUmVzdWx0LA0KICAgICAgICAgICAgZXZhbHVhdGlvblJlbWFyazogZXZhbHVhdGlvblJlbWFyaw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICAgIGlmKGRhdGEubGVuZ3RoPjApew0KICAgICAgICAgICAgY29uc29sZS5sb2coIi0tLS0tLS3lvIDlp4vlkI7lj7Dkv53lrZjor4TlrqHnu5PmnpwtLS0tLS0tLS0tLS0tLS0tIik7DQogICAgICAgICAgICByZXR1cm4gc2NvcmluZ0ZhY3RvcnMoZGF0YSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgY29uc29sZS5sb2cocmVzcG9uc2UubXNnKTsNCiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLkv53lrZjmiJDlip8iKTsNCiAgICAgICAgICAgICAgICByZXR1cm4geyBjb2RlOiAyMDAsIHN1Y2Nlc3M6IHRydWUgfTsNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcocmVzcG9uc2UubXNnKTsNCiAgICAgICAgICAgICAgICByZXR1cm4geyBjb2RlOiByZXNwb25zZS5jb2RlLCBzdWNjZXNzOiBmYWxzZSB9Ow0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KS5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5L+d5a2Y5aSx6LSlIik7DQogICAgICAgICAgICAgIHJldHVybiB7IGNvZGU6IDAsIHN1Y2Nlc3M6IGZhbHNlIH07DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfWVsc2V7DQogICAgICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSh7IGNvZGU6IDIwMCwgc3VjY2VzczogdHJ1ZSB9KTsgLy8g5rKh5pyJ5pWw5o2u6ZyA6KaB5L+d5a2Y5pe25Lmf6L+U5Zue5oiQ5YqfDQogICAgICAgIH0NCiAgICB9LA0KICAgIHNhdmUoKSB7DQogICAgICBpZiAodGhpcy5zdXBwbGllciA9PSAiIikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeS+m+W6lOWVhiIpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy9jb25zdCBkYXRhID0gdGhpcy5nZW5lcmF0aW5nU2F2ZWREYXRhKCk7DQogICAgICAgIHZhciBkYXRhID0gW107DQogICAgICAgIGZvciAobGV0IGluZGV4ID0gMDsgaW5kZXggPCB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zLmxlbmd0aDsgaW5kZXgrKykgew0KICAgICAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zW2luZGV4XTsNCiAgICAgICAgICBjb25zdCBpdGVtSWQgPSBpdGVtLmVudE1ldGhvZEl0ZW1JZDsNCiAgICAgICAgICAvLyDojrflj5blvZPliY3pobnlr7nlupTnmoTor4TliIbnu5PmnpwNCiAgICAgICAgICBjb25zdCBldmFsdWF0aW9uUmVzdWx0ID0gdGhpcy5kZWZhdWx0UmF0aW5nQXJyYXlbaXRlbUlkXS5zdGF0ZTsNCiAgICAgICAgICBpZiAoZXZhbHVhdGlvblJlc3VsdCA9PT0gbnVsbCB8fCBldmFsdWF0aW9uUmVzdWx0ID09PSAiIikgew0KICAgICAgICAgICAgLy8g5aaC5p6c6K+E5YiG57uT5p6c5Li656m677yM5by55Ye65o+Q56S677yM5o+Q56S65YaF5a655YyF5ZCr6K+l6aG555qEaXRlbU5hbWUNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhg6K+35aGr5YaZ6K+E5YiG6aG577yaJHtpdGVtLml0ZW1OYW1lfSDnmoTor4TliIbnu5PmnpxgKTsNCiAgICAgICAgICAgIHJldHVybjsgLy8g55u05o6l6L+U5Zue77yM5LiN5YaN57un57ut5p6E5bu65pWw5o2u77yM562J5b6F55So5oi35aGr5YaZ5a6M5pW0DQogICAgICAgICAgfQ0KICAgICAgICAgIC8vIOiOt+WPluW9k+WJjemhueWvueW6lOeahOivhOWIhuWkh+azqO+8jOi/m+ihjOmdnuepuuWIpOaWre+8jOS4uuepuuWImei1i+S6iOm7mOiupOWAvO+8iOi/memHjOiuvuS4uuepuuWtl+espuS4su+8iQ0KICAgICAgICAgIGNvbnN0IGV2YWx1YXRpb25SZW1hcmsgPSB0aGlzLmRlZmF1bHRSYXRpbmdBcnJheVtpdGVtSWRdLnJlYXNvbiB8fCAiIjsNCiAgICAgICAgICBkYXRhLnB1c2goew0KICAgICAgICAgICAgc2NvcmluZ01ldGhvZFVpdGVtSWQ6IGl0ZW1JZCwNCiAgICAgICAgICAgIGV4cGVydFJlc3VsdElkOiB0aGlzLmV4cGVydEluZm8ucmVzdWx0SWQsDQogICAgICAgICAgICBlbnRJZDogdGhpcy5zZWxlY3RTdXBwbGllci5iaWRkZXJJZCwNCiAgICAgICAgICAgIGV2YWx1YXRpb25SZXN1bHQ6IGV2YWx1YXRpb25SZXN1bHQsDQogICAgICAgICAgICBldmFsdWF0aW9uUmVtYXJrOiBldmFsdWF0aW9uUmVtYXJrDQogICAgICAgICAgfSk7DQogICAgICAgIH0NCg0KICAgICAgICBzY29yaW5nRmFjdG9ycyhkYXRhKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKHJlc3BvbnNlLm1zZyk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhyZXNwb25zZS5tc2cpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDnlJ/miJDkv53lrZjmlbDmja4NCiAgICBnZW5lcmF0aW5nU2F2ZWREYXRhKCkgew0KICAgICAgdmFyIGRhdGEgPSBbXTsNCiAgICAgIGZvciAobGV0IGluZGV4ID0gMDsgaW5kZXggPCB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zLmxlbmd0aDsgaW5kZXgrKykgew0KICAgICAgICBjb25zdCBpdGVtID0gdGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtc1tpbmRleF07DQogICAgICAgIGNvbnN0IGl0ZW1JZCA9IGl0ZW0uZW50TWV0aG9kSXRlbUlkOw0KICAgICAgICAvLyDojrflj5blvZPliY3pobnlr7nlupTnmoTor4TliIbnu5PmnpwNCiAgICAgICAgY29uc3QgZXZhbHVhdGlvblJlc3VsdCA9IHRoaXMuZGVmYXVsdFJhdGluZ0FycmF5W2l0ZW1JZF0uc3RhdGU7DQogICAgICAgIGlmIChldmFsdWF0aW9uUmVzdWx0ID09PSBudWxsIHx8IGV2YWx1YXRpb25SZXN1bHQgPT09ICIiKSB7DQogICAgICAgICAgLy8g5aaC5p6c6K+E5YiG57uT5p6c5Li656m677yM5by55Ye65o+Q56S677yM5o+Q56S65YaF5a655YyF5ZCr6K+l6aG555qEaXRlbU5hbWUNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoYOivt+Whq+WGmeivhOWIhumhue+8miR7aXRlbS5pdGVtTmFtZX0g55qE6K+E5YiG57uT5p6cYCk7DQogICAgICAgICAgcmV0dXJuOyAvLyDnm7TmjqXov5Tlm57vvIzkuI3lho3nu6fnu63mnoTlu7rmlbDmja7vvIznrYnlvoXnlKjmiLfloavlhpnlrozmlbQNCiAgICAgICAgfQ0KICAgICAgICAvLyDojrflj5blvZPliY3pobnlr7nlupTnmoTor4TliIblpIfms6jvvIzov5vooYzpnZ7nqbrliKTmlq3vvIzkuLrnqbrliJnotYvkuojpu5jorqTlgLzvvIjov5nph4zorr7kuLrnqbrlrZfnrKbkuLLvvIkNCiAgICAgICAgY29uc3QgZXZhbHVhdGlvblJlbWFyayA9IHRoaXMuZGVmYXVsdFJhdGluZ0FycmF5W2l0ZW1JZF0ucmVhc29uIHx8ICIiOw0KICAgICAgICBkYXRhLnB1c2goew0KICAgICAgICAgIHNjb3JpbmdNZXRob2RVaXRlbUlkOiBpdGVtSWQsDQogICAgICAgICAgZXhwZXJ0UmVzdWx0SWQ6IHRoaXMuZXhwZXJ0SW5mby5yZXN1bHRJZCwNCiAgICAgICAgICBlbnRJZDogdGhpcy5zZWxlY3RTdXBwbGllci5iaWRkZXJJZCwNCiAgICAgICAgICBldmFsdWF0aW9uUmVzdWx0OiBldmFsdWF0aW9uUmVzdWx0LA0KICAgICAgICAgIGV2YWx1YXRpb25SZW1hcms6IGV2YWx1YXRpb25SZW1hcmsNCiAgICAgICAgfSk7DQogICAgICB9DQoNCiAgICAgIC8qZm9yIChsZXQgaW5kZXggPSAwOyBpbmRleCA8IHRoaXMuc2NvcmluZ1N5c3RlbS51aXRlbXMubGVuZ3RoOyBpbmRleCsrKSB7DQogICAgICAgIGRhdGEucHVzaCh7DQogICAgICAgICAgc2NvcmluZ01ldGhvZFVpdGVtSWQ6DQogICAgICAgICAgICB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zW2luZGV4XS5lbnRNZXRob2RJdGVtSWQsDQogICAgICAgICAgZXhwZXJ0UmVzdWx0SWQ6IHRoaXMuZXhwZXJ0SW5mby5yZXN1bHRJZCwNCiAgICAgICAgICBlbnRJZDogdGhpcy5zZWxlY3RTdXBwbGllci5iaWRkZXJJZCwNCiAgICAgICAgICBldmFsdWF0aW9uUmVzdWx0Og0KICAgICAgICAgICAgdGhpcy5kZWZhdWx0UmF0aW5nQXJyYXlbDQogICAgICAgICAgICAgIHRoaXMuc2NvcmluZ1N5c3RlbS51aXRlbXNbaW5kZXhdLmVudE1ldGhvZEl0ZW1JZA0KICAgICAgICAgICAgXS5zdGF0ZSwNCiAgICAgICAgICBldmFsdWF0aW9uUmVtYXJrOg0KICAgICAgICAgICAgdGhpcy5kZWZhdWx0UmF0aW5nQXJyYXlbDQogICAgICAgICAgICAgIHRoaXMuc2NvcmluZ1N5c3RlbS51aXRlbXNbaW5kZXhdLmVudE1ldGhvZEl0ZW1JZA0KICAgICAgICAgICAgXS5yZWFzb24sDQogICAgICAgIH0pOw0KICAgICAgfSovDQogICAgICByZXR1cm4gZGF0YTsNCiAgICB9LA0KICAgIHN1Ym1pdCgpIHsNCiAgICAgICAgdGhpcy50bXBTYXZlKCkudGhlbigoc2F2ZVJlc3VsdCkgPT4gew0KICAgICAgICAgIC8vIOajgOafpeS/neWtmOe7k+aenO+8jOWmguaenOagoemqjOWksei0peWImeS4jee7p+e7reaPkOS6pA0KICAgICAgICAgIGlmICghc2F2ZVJlc3VsdCB8fCBzYXZlUmVzdWx0LnN1Y2Nlc3MgPT09IGZhbHNlKSB7DQogICAgICAgICAgICByZXR1cm47IC8vIOagoemqjOWksei0pe+8jOS4jee7p+e7reaPkOS6pOa1geeoiw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGNvbnN0IGRhdGEgPSB7DQogICAgICAgICAgICBwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwNCiAgICAgICAgICAgIGV4cGVydFJlc3VsdElkOiB0aGlzLmV4cGVydEluZm8ucmVzdWx0SWQsDQogICAgICAgICAgICBzY29yaW5nTWV0aG9kSXRlbUlkOiB0aGlzLiRyb3V0ZS5xdWVyeS5zY29yaW5nTWV0aG9kSXRlbUlkLA0KICAgICAgICAgIH07DQogICAgICAgICAgY2hlY2tSZXZpZXdTdW1tYXJ5KGRhdGEpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIC8vIOS/ruaUueS4k+Wutui/m+W6pg0KICAgICAgICAgICAgY29uc3Qgc3RhdHVzID0gew0KICAgICAgICAgICAgICBldmFsRXhwZXJ0U2NvcmVJbmZvSWQ6IEpTT04ucGFyc2UoDQogICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oImV2YWxFeHBlcnRTY29yZUluZm8iKQ0KICAgICAgICAgICAgICApLmV2YWxFeHBlcnRTY29yZUluZm9JZCwNCiAgICAgICAgICAgICAgZXZhbFN0YXRlOiAxLA0KICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIGVkaXRFdmFsRXhwZXJ0U2NvcmVJbmZvKHN0YXR1cykudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaPkOS6pOaIkOWKnyIpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIHRoaXMuJGVtaXQoInNlbmQiLCAidHdvIik7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhyZXNwb25zZS5tc2cpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqDQogICAgICog5pi+56S66YeH6LSt5paH5Lu2UERGDQogICAgICovDQogICAgdmlld1B1cmNoYXNpbmcoKSB7DQogICAgICB0aGlzLmFjdGl2ZUJ1dHRvbiA9ICdwcm9jdXJlbWVudCc7IC8vIOiuvue9ruW9k+WJjea/gOa0u+aMiemSrg0KICAgICAgdGhpcy5kb3VibGUgPSBmYWxzZTsgLy8g5Y2V5paH5Lu25qih5byPDQogICAgICB0aGlzLnJlc3BvbnNlU2hvdyA9IGZhbHNlOyAvLyDkuI3mmL7npLrlk43lupTmlofku7YNCiAgICAgIHRoaXMucHJvY3VyZW1lbnRTaG93ID0gdHJ1ZTsgLy8g5pi+56S66YeH6LSt5paH5Lu2DQoNCiAgICAgIC8vIOWPs+S+p+ivhOWIhumhueaYvuekuuS4uumHh+i0reaWh+S7tueahOivhOWIhumhuQ0KICAgICAgbGV0IHBhZ2VQcm9jdXJlbWVudEFyciA9IFtdOyAvLyDph4fotK3mlofku7bor4TliIbpobnmlbDnu4QNCiAgICAgIGZvciAobGV0IGl0ZW0gaW4gdGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2Upew0KICAgICAgICBwYWdlUHJvY3VyZW1lbnRBcnIucHVzaCh7DQogICAgICAgICAgaXRlbU5hbWU6IGl0ZW0sDQogICAgICAgICAganVtcFRvUGFnZTogdGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2VbaXRlbV0NCiAgICAgICAgfSkNCiAgICAgIH0NCg0KICAgICAgY29uc29sZS5sb2codGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtcyk7DQogICAgICBjb25zb2xlLmxvZyhwYWdlUHJvY3VyZW1lbnRBcnIpDQogICAgICB0aGlzLnBhZ2VQcm9jdXJlbWVudCA9IFtdOw0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zLmxlbmd0aDtpKyspew0KICAgICAgICBmb3IgKGxldCBqID0gMDsgaiA8IHBhZ2VQcm9jdXJlbWVudEFyci5sZW5ndGg7aisrKXsNCiAgICAgICAgICBpZiAodGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtc1tpXS5pdGVtTmFtZSA9PSBwYWdlUHJvY3VyZW1lbnRBcnJbal0uaXRlbU5hbWUpew0KICAgICAgICAgICAgdGhpcy5wYWdlUHJvY3VyZW1lbnQucHVzaCh7Li4udGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtc1tpXSwuLi5wYWdlUHJvY3VyZW1lbnRBcnJbal19KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGNvbnNvbGUubG9nKHRoaXMucGFnZVByb2N1cmVtZW50KQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDot7PovazliLDph4fotK3mlofku7blr7nlupTpobXnoIENCiAgICAgKiBAcGFyYW0ge09iamVjdH0gaXRlbSAtIOivhOWIhumhueWvueixoQ0KICAgICAqLw0KICAgIGp1bXBUb1Byb2N1cmVtZW50UGFnZShpdGVtKSB7DQogICAgICBpZiAoaXRlbS5qdW1wVG9QYWdlICYmIHRoaXMuJHJlZnMucHJvY3VyZW1lbnQpIHsNCiAgICAgICAgdGhpcy4kcmVmcy5wcm9jdXJlbWVudC5za2lwUGFnZShpdGVtLmp1bXBUb1BhZ2UpOw0KICAgICAgfQ0KICAgIH0sDQoJICANCgkgIC8qKg0KCSAgICog5qOA5p+l5piv5ZCm5Y+v5Lul6Lez6L2s6aG16Z2iDQoJICAgKiBAcmV0dXJucyB7Ym9vbGVhbn0g5piv5ZCm5Y+v5Lul6Lez6L2sDQoJICAgKi8NCgkgIGNhbkp1bXBUb1BhZ2UoKSB7DQoJCSAgLy8g5aaC5p6c5Y+q5pi+56S66YeH6LSt5paH5Lu2DQoJCSAgaWYgKHRoaXMucHJvY3VyZW1lbnRTaG93ICYmICF0aGlzLnJlc3BvbnNlU2hvdykgew0KCQkJICByZXR1cm4gdGhpcy5wcm9jdXJlbWVudFBkZlJlbmRlcmVkOw0KCQkgIH0NCgkJICAvLyDlpoLmnpzlj6rmmL7npLrlk43lupTmlofku7YNCgkJICBpZiAodGhpcy5yZXNwb25zZVNob3cgJiYgIXRoaXMucHJvY3VyZW1lbnRTaG93KSB7DQoJCQkgIHJldHVybiB0aGlzLnJlc3BvbnNlUGRmUmVuZGVyZWQ7DQoJCSAgfQ0KCQkgIC8vIOWmguaenOWvueavlOaooeW8j++8iOS4pOS4qumDveaYvuekuu+8iQ0KCQkgIGlmICh0aGlzLnJlc3BvbnNlU2hvdyAmJiB0aGlzLnByb2N1cmVtZW50U2hvdykgew0KCQkJICByZXR1cm4gdGhpcy5yZXNwb25zZVBkZlJlbmRlcmVkICYmIHRoaXMucHJvY3VyZW1lbnRQZGZSZW5kZXJlZDsNCgkJICB9DQoJCSAgcmV0dXJuIGZhbHNlOw0KCSAgfSwNCgkgIC8qKg0KCSAgICog5aSE55CGUERG5riy5p+T54q25oCB5Y+Y5YyWDQoJICAgKiBAcGFyYW0ge2Jvb2xlYW59IGlzUmVuZGVyZWQg5piv5ZCm5riy5p+T5a6M5oiQDQoJICAgKiBAcGFyYW0ge3N0cmluZ30gcGRmVHlwZSBQREbnsbvlnovvvJoncmVzcG9uc2UnIOaIliAncHJvY3VyZW1lbnQnDQoJICAgKi8NCgkgIGhhbmRsZVBkZlJlbmRlclN0YXR1c0NoYW5nZShpc1JlbmRlcmVkLCBwZGZUeXBlKSB7DQoJCSAgaWYgKHBkZlR5cGUgPT09ICdyZXNwb25zZScpIHsNCgkJCSAgdGhpcy5yZXNwb25zZVBkZlJlbmRlcmVkID0gaXNSZW5kZXJlZDsNCgkJICB9IGVsc2UgaWYgKHBkZlR5cGUgPT09ICdwcm9jdXJlbWVudCcpIHsNCgkJCSAgdGhpcy5wcm9jdXJlbWVudFBkZlJlbmRlcmVkID0gaXNSZW5kZXJlZDsNCgkJICB9DQoJCSAgDQoJCSAgaWYgKGlzUmVuZGVyZWQpIHsNCgkJCSAgY29uc29sZS5sb2coYCR7cGRmVHlwZSA9PT0gJ3Jlc3BvbnNlJyA/ICflk43lupQnIDogJ+mHh+i0rSd95paH5Lu25riy5p+T5a6M5oiQ77yM5Y+v5Lul6L+b6KGM6aG16Z2i6Lez6L2sYCk7DQoJCSAgfQ0KCSAgfSwNCgkgIA0KICAgIC8vIOi3s+i9rOWIsOS6jOasoeaKpeS7tw0KICAgIHNlY29uZE9mZmVyKCkgew0KICAgICAgY29uc3QgcXVlcnkgPSB7DQogICAgICAgIHByb2plY3RJZDogdGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkLA0KICAgICAgICB6amhtOiB0aGlzLiRyb3V0ZS5xdWVyeS56amhtLA0KICAgICAgICBzY29yaW5nTWV0aG9kSXRlbUlkOiBKU09OLnBhcnNlKA0KICAgICAgICAgIGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJ0ZW5kZXJPZmZlclNjb3JpbmdNZXRob2RJdGVtcyIpDQogICAgICAgICksDQogICAgICB9Ow0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAiL3NlY29uZE9mZmVyIiwgcXVlcnk6IHF1ZXJ5IH0pOw0KICAgIH0sDQogICAgLy8g6Lez6L2s5Yiw6K+i5qCHDQogICAgYmlkSW5xdWlyeSgpIHsNCiAgICAgIGNvbnN0IHF1ZXJ5ID0gew0KICAgICAgICBwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwNCiAgICAgICAgempobTogdGhpcy4kcm91dGUucXVlcnkuempobSwNCiAgICAgICAgc2NvcmluZ01ldGhvZEl0ZW1JZDogSlNPTi5wYXJzZSgNCiAgICAgICAgICBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgidGVuZGVyT2ZmZXJTY29yaW5nTWV0aG9kSXRlbXMiKQ0KICAgICAgICApLA0KICAgICAgfTsNCiAgICAgIGNvbnNvbGUubG9nKCJxdWVyeSIsIHF1ZXJ5KTsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogIi9iaWRJbnF1aXJ5IiwgcXVlcnk6IHF1ZXJ5IH0pOw0KICAgIH0sDQogICAgLy8g6I635Y+W5Zug57Sg5a+55bqU6aG156CBDQogICAgZ2V0RmFjdG9yc1BhZ2UoKSB7DQogICAgICB0aGlzLmZhY3RvcnNQYWdlID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZW50RG9jUmVzcG9uc2VQYWdlIikpOw0KICAgIH0sDQogICAgDQogICAgLyoqDQogICAgICog6I635Y+W6K+E5YiG6aG555qE5pyA5aSn5YiG5YC8DQogICAgICogQHBhcmFtIHtPYmplY3R9IGl0ZW0gLSDor4TliIbpobnlr7nosaENCiAgICAgKiBAcmV0dXJucyB7bnVtYmVyfSDmnIDlpKfliIblgLwNCiAgICAgKi8NCiAgICBnZXRNYXhTY29yZShpdGVtKSB7DQogICAgICBpZiAoIWl0ZW0pIHJldHVybiAwOw0KICAgICAgDQogICAgICAvLyDlpoLmnpzmnInliIbmlbDmjKHkvY3vvIzkvb/nlKjmjKHkvY3kuK3nmoTmnIDlpKflgLwNCiAgICAgIGlmIChpdGVtLnNjb3JlTGV2ZWwgJiYgaXRlbS5zY29yZUxldmVsLmxlbmd0aCA+IDAgJiYgaXRlbS5zY29yZUxldmVsICE9PSBudWxsICYmIGl0ZW0uc2NvcmVMZXZlbCAhPT0gdW5kZWZpbmVkKSB7DQogICAgICAgIGNvbnN0IHNjb3JlTGV2ZWxzID0gaXRlbS5zY29yZUxldmVsLnNwbGl0KCcsJykubWFwKGxldmVsID0+IHBhcnNlRmxvYXQobGV2ZWwudHJpbSgpKSkuZmlsdGVyKGxldmVsID0+ICFpc05hTihsZXZlbCkpOw0KICAgICAgICBpZiAoc2NvcmVMZXZlbHMubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHJldHVybiBNYXRoLm1heCguLi5zY29yZUxldmVscyk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIA0KICAgICAgLy8g5ZCm5YiZ5L2/55So6YWN572u55qE5pyA5aSn5YiG5YC8DQogICAgICByZXR1cm4gcGFyc2VGbG9hdChpdGVtLnNjb3JlKSB8fCAwOw0KICAgIH0sDQoJICBkb3dubG9hZEZpbGUoaXRlbSl7DQoJCQl0aGlzLiRkb3dubG9hZC56aXAoaXRlbS5maWxlUGF0aCxpdGVtLmZpbGVOYW1lKTsNCgkgIH0sDQoJICANCgkgIC8vID09PT09PT09PT0g5oKs5YGc55u45YWzID09PT09PT09PT0NCgkgIC8qKg0KCSAgICog5pi+56S66K+E5YiG6aG55oKs5rWu5qGGDQoJICAgKiBAcGFyYW0ge09iamVjdH0gZmFjdG9ySXRlbSDor4TliIbpobnlr7nosaENCgkgICAqLw0KCSAgc2hvd0ZhY3RvclRvb2x0aXAoZmFjdG9ySXRlbSkgew0KCQkgIGlmICghZmFjdG9ySXRlbS5pdGVtUmVtYXJrKSByZXR1cm47IC8vIOWmguaenOayoeacieivhOWuoeWGheWuueWImeS4jeaYvuekug0KCQkgIA0KCQkgIC8vIOa4hemZpOS5i+WJjeeahOWumuaXtuWZqA0KCQkgIGlmICh0aGlzLnRvb2x0aXBUaW1lcikgew0KCQkJICBjbGVhclRpbWVvdXQodGhpcy50b29sdGlwVGltZXIpOw0KCQkgIH0NCgkJICANCgkJICAvLyDlu7bov5/mmL7npLrmgqzmta7moYbvvIzpgb/lhY3lv6vpgJ/np7vliqjml7bpopHnuYHmmL7npLoNCgkJICB0aGlzLnRvb2x0aXBUaW1lciA9IHNldFRpbWVvdXQoKCkgPT4gew0KCQkJICB0aGlzLmhvdmVyZWRGYWN0b3JOb2RlID0gZmFjdG9ySXRlbTsNCgkJICB9LCAzMDApOyAvLyAzMDBtc+W7tui/nw0KCSAgfSwNCgkgIA0KCSAgLyoqDQoJICAgKiDpmpDol4/or4TliIbpobnmgqzmta7moYYNCgkgICAqLw0KCSAgaGlkZUZhY3RvclRvb2x0aXAoKSB7DQoJCSAgLy8g5riF6Zmk5a6a5pe25ZmoDQoJCSAgaWYgKHRoaXMudG9vbHRpcFRpbWVyKSB7DQoJCQkgIGNsZWFyVGltZW91dCh0aGlzLnRvb2x0aXBUaW1lcik7DQoJCQkgIHRoaXMudG9vbHRpcFRpbWVyID0gbnVsbDsNCgkJICB9DQoJCSAgDQoJCSAgLy8g5bu26L+f6ZqQ6JeP77yM57uZ55So5oi35pe26Ze056e75Yqo5Yiw5oKs5rWu5qGG5LiKDQoJCSAgc2V0VGltZW91dCgoKSA9PiB7DQoJCQkgIHRoaXMuaG92ZXJlZEZhY3Rvck5vZGUgPSBudWxsOw0KCQkgIH0sIDEwMCk7DQoJICB9LA0KCSAgDQoJICAvKioNCgkgICAqIOa4hemZpOaCrOa1ruahhuWumuaXtuWZqO+8iOW9k+m8oOagh+enu+WKqOWIsOaCrOa1ruahhuS4iuaXtu+8iQ0KCSAgICovDQoJICBjbGVhclRvb2x0aXBUaW1lcigpIHsNCgkJICBpZiAodGhpcy50b29sdGlwVGltZXIpIHsNCgkJCSAgY2xlYXJUaW1lb3V0KHRoaXMudG9vbHRpcFRpbWVyKTsNCgkJCSAgdGhpcy50b29sdGlwVGltZXIgPSBudWxsOw0KCQkgIH0NCgkgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmluaXQoKTsNCiAgICB0aGlzLmdldEZhY3RvcnNQYWdlKCk7DQogIH0sDQoJYmVmb3JlRGVzdHJveSgpIHsNCgkJLy8g5riF55CG5a6a5pe25ZmoDQoJCWlmICh0aGlzLnRvb2x0aXBUaW1lcikgew0KCQkJY2xlYXJUaW1lb3V0KHRoaXMudG9vbHRpcFRpbWVyKTsNCgkJCXRoaXMudG9vbHRpcFRpbWVyID0gbnVsbDsNCgkJfQ0KCX0sDQp9Ow0K"}, {"version": 3, "sources": ["one.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAs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file": "one.vue", "sourceRoot": "src/views/expertReview/technical", "sourcesContent": ["<template>\r\n  <div class=\"technical-review-container\">\r\n    <div class=\"main-content\">\r\n      <div class=\"header-section\">\r\n        <div class=\"title-section\">\r\n          <div class=\"main-title\">技术标评审</div>\r\n          <div class=\"help-section\">\r\n            <div class=\"help-text\">该页面操作说明</div>\r\n            <el-image class=\"help-image\" :src=\"srcList[0]\" :preview-src-list=\"srcList\">\r\n            </el-image>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 文件列表 -->\r\n        <div class=\"file-list-container\">\r\n          <div class=\"file-list-title\">响应文件附件下载</div>\r\n          <el-card\r\n            v-for=\"(item, index) in attachmentsList\"\r\n            :key=\"index\"\r\n            class=\"file-item\"\r\n            shadow=\"hover\"\r\n            @click.native=\"downloadFile(item)\"\r\n          >\r\n            <div class=\"file-item-content\">\r\n              <i class=\"el-icon-document file-icon\"></i>\r\n              <span class=\"file-name\">{{ item.fileName }}</span>\r\n              <i class=\"el-icon-download download-icon\"></i>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <div class=\"action-buttons\">\r\n          <el-button class=\"item-button\" @click=\"bidInquiry\">询标</el-button>\r\n          <el-button class=\"item-button\" v-if=\"expertInfo.expertLeader==1\" @click=\"secondOffer\">发起二次报价</el-button>\r\n          <div class=\"button-group\">\r\n\t          <el-button\r\n\t\t          :class=\"['item-button', activeButton === 'procurement' ? 'technical-blue-btn-active' : 'technical-blue-btn']\"\r\n\t\t          @click=\"viewPurchasing\">采购文件</el-button>\r\n\t          \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'response' ? 'technical-blue-btn-active' : 'technical-blue-btn']\"\r\n              @click=\"showResponseFile()\">响应文件</el-button>\r\n            \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'contrast' ? 'technical-blue-btn-active' : 'technical-blue-btn']\"\r\n              @click=\"fileContrast\">对比</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"content-section\">\r\n        \r\n\r\n        <!-- PDF预览区域 - 保持原始尺寸 -->\r\n        <div class=\"pdf-preview-area\">\r\n          <div\r\n            v-show=\"procurementShow\"\r\n            class=\"pdf-panel procurement-panel\"\r\n            :class=\"{ 'border-left': double }\"\r\n          >\r\n<!--            <pdfView-->\r\n<!--              ref=\"procurement\"-->\r\n<!--              :pdfurl=\"procurementPdf\"-->\r\n<!--              :uni_key=\"'procurement'\"-->\r\n<!--            ></pdfView>-->\r\n\t          \r\n\t          <PdfViewImproved ref=\"procurement\"  :pdfurl=\"procurementPdf\"  :page-height=\"800\" :buffer-size=\"2\" @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"/>\r\n          </div>\r\n\t        \r\n\t        <div\r\n\t\t        v-show=\"responseShow\"\r\n\t\t        class=\"pdf-panel response-panel\"\r\n\t\t        :class=\"{ 'border-right': double }\"\r\n\t        >\r\n\t\t        <!--            <pdfView-->\r\n\t\t        <!--              ref=\"response\"-->\r\n\t\t        <!--              :pdfurl=\"responsePdf\"-->\r\n\t\t        <!--              :uni_key=\"'response'\"-->\r\n\t\t        <!--            ></pdfView>-->\r\n\t\t        \r\n\t\t        <PdfViewImproved ref=\"response\"  :pdfurl=\"responsePdf\"  :page-height=\"800\" :buffer-size=\"2\" @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"/>\r\n\t        </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"divider\">\r\n    </div>\r\n    <div class=\"sidebar\">\r\n      <div class=\"sidebar-header\">\r\n        <el-select\r\n          class=\"supplier-select\"\r\n          v-model=\"supplier\"\r\n          placeholder=\"请选择供应商\"\r\n          @change=\"handleChange\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in options\"\r\n            :key=\"item.bidderName\"\r\n            :label=\"item.bidderName\"\r\n            :value=\"item.bidderName\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </div>\r\n\t    \r\n      <div class=\"sidebar-content\" >\r\n        <!-- 响应文件评分项显示 -->\r\n\t      <template v-if=\"responseShow || double\">\r\n\t\t      <!-- PDF渲染状态提示 -->\r\n\t\t      <div v-if=\"!responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t\t      <i class=\"el-icon-loading\"></i>\r\n\t\t\t      <span>响应文件正在渲染中，请稍候...</span>\r\n\t\t      </div>\r\n\t\t      <div v-else class=\"render-status-tip success\">\r\n\t\t\t      <i class=\"el-icon-success\"></i>\r\n\t\t\t      <span>响应文件渲染完成，可以点击跳转</span>\r\n\t\t      </div>\r\n\t\t      \r\n\t\t      <div\r\n\t\t\t      v-for=\"(item, index) in scoringSystem.uitems\"\r\n\t\t\t      :key=\"'response-' + index\"\r\n\t\t\t      class=\"factor-item\"\r\n\t\t\t      @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t      @mouseleave=\"hideFactorTooltip\"\r\n\t\t      >\r\n\t\t\t      <!-- 悬浮框 -->\r\n\t\t\t      <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t           class=\"factor-tooltip\"\r\n\t\t\t           @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t      <div class=\"tooltip-header\">\r\n\t\t\t\t\t      <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t      <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t      </div>\r\n\t\t\t\t      <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t      </div>\r\n\t\t\t      <div>\r\n\t\t\t\t      <div class=\"factors\">\r\n\t\t\t\t\t      <div class=\"factor-header\">\r\n\t\t\t\t\t\t      <div class=\"factor-name factor-title\"\r\n\t\t\t\t\t\t           :class=\"{ 'disabled': !canJumpToPage() }\"\r\n\t\t\t\t\t\t           @click=\"showInfo(item)\">\r\n\t\t\t\t\t\t\t      {{ item.itemName }}\r\n\t\t\t\t\t\t\t      <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t      \r\n\t\t\t\t\t\t\t      <span class=\"max-score\">\r\n\t\t\t\t\t\t\t        最高{{ getMaxScore(item) }}分\r\n\t\t\t\t\t\t\t      </span>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t\t      <div class=\"factor-input\">\r\n\t\t\t\t\t\t      <div v-if=\"!(item.scoreLevel.length == 0 || item.scoreLevel == null || item.scoreLevel == undefined)\">\r\n\t\t\t\t\t\t\t      <el-radio\r\n\t\t\t\t\t\t\t\t      v-for=\"(score,index) in item.scoreLevel.split(',')\"\r\n\t\t\t\t\t\t\t\t      :key=\"index\"\r\n\t\t\t\t\t\t\t\t      v-model=\"defaultRatingArray[item.entMethodItemId].state\"\r\n\t\t\t\t\t\t\t\t      :label=\"score\"\r\n\t\t\t\t\t\t\t      ><span class=\"score-value\">{{ score }}</span></el-radio>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t\t      <div v-else>\r\n\t\t\t\t\t\t\t      <el-input\r\n\t\t\t\t\t\t\t\t      placeholder=\"请输入分数\"\r\n\t\t\t\t\t\t\t\t      v-model=\"defaultRatingArray[item.entMethodItemId].state\"\r\n\t\t\t\t\t\t\t\t      @input=\"handleScoreInput(item.entMethodItemId, $event)\"\r\n\t\t\t\t\t\t\t\t      @keypress=\"onlyNumber\"\r\n\t\t\t\t\t\t\t\t      type=\"number\"\r\n\t\t\t\t\t\t\t\t      step=\"1\"\r\n\t\t\t\t\t\t\t\t      min=\"0\"\r\n\t\t\t\t\t\t\t      ></el-input>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t      \r\n\t\t\t\t      </div>\r\n\t\t\t      </div>\r\n\t\t      </div>\r\n\t      </template>\r\n\t      \r\n\t      <template v-else-if=\"procurementShow\" >\r\n\t\t      \r\n\t\t      <!-- PDF渲染状态提示 -->\r\n          <div v-if=\"!procurementPdfRendered\" class=\"render-status-tip\">\r\n            <i class=\"el-icon-loading\"></i>\r\n            <span>采购文件正在渲染中，请稍候...</span>\r\n          </div>\r\n          <div v-else class=\"render-status-tip success\">\r\n            <i class=\"el-icon-success\"></i>\r\n            <span>采购文件渲染完成，可以点击跳转</span>\r\n          </div>\r\n\t\t      \r\n\t\t      <!-- 采购文件评分项显示 -->\r\n\t\t      <div\r\n\t\t\t      v-for=\"(item, index) in pageProcurement\"\r\n\t\t\t      :key=\"'procurement-' + index\"\r\n\t\t\t      class=\"factor-item\"\r\n\t\t\t      @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t      @mouseleave=\"hideFactorTooltip\"\r\n\t\t      >\r\n\t\t\t      <!-- 悬浮框 -->\r\n\t\t\t      <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t           class=\"factor-tooltip\"\r\n\t\t\t           @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t      <div class=\"tooltip-header\">\r\n\t\t\t\t\t      <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t      <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t      </div>\r\n\t\t\t\t      <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t      </div>\r\n\t\t\t      <div>\r\n\t\t\t\t      <div class=\"factors\">\r\n\t\t\t\t\t      <div class=\"factor-header\">\r\n\t\t\t\t\t\t      <div class=\"factor-name factor-title\"\r\n\t\t\t\t\t\t           :class=\"{ 'disabled': !canJumpToPage() }\"\r\n\t\t\t\t\t\t           @click=\"jumpToProcurementPage(item)\">\r\n\t\t\t\t\t\t\t      {{ item.itemName }}\r\n\t\t\t\t\t\t\t      \r\n\t\t\t\t\t\t\t      <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t      </div>\r\n\t\t\t      </div>\r\n\t\t      </div>\r\n\t      </template>\r\n\t      \r\n        <div class=\"submit-section\">\r\n          <!-- <div><el-button\r\n              class=\"item-button-little\"\r\n              style=\"background-color:#F5F5F5;color:#176ADB\"\r\n              @click=\"save\"\r\n            >保存</el-button></div> -->\r\n          <div><el-button\r\n              class=\"item-button-little primary-btn\"\r\n              @click=\"submit\"\r\n            >提交</el-button></div>\r\n        </div>\r\n\r\n        <div class=\"review-content\">\r\n          <div class=\"review-title\">评审内容：</div>\r\n          <div class=\"review-text\" v-html=\"selectNode.itemRemark\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  supplierInfo,\r\n  approvalProcess,\r\n  scoringFactors,\r\n  checkReviewSummary,\r\n  filesById,\r\n} from \"@/api/expert/review\";\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\";\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      options: [],\r\n      scoringSystem: [],\r\n      selectNode: {},\r\n      supplier: \"\",\r\n      selectSupplier: {},\r\n      expertInfo: {},\r\n      defaultRatingArray: {},\r\n      file: {},\r\n      responseShow: false,\r\n      procurementShow: false,\r\n      double: false,\r\n      factorList: [],\r\n      entDocResponsePage: {},\r\n      factorsPage: {},\r\n      bidderFactor: {},\r\n\r\n      responsePdf: null,\r\n      procurementPdf: null,\r\n      currentMaxScore: null,\r\n\r\n      // 按钮状态管理\r\n      activeButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n      // 采购文件相关数据\r\n      entDocProcurementPage: {}, // 采购文件页码信息\r\n      pageProcurement: [], // 采购文件的评分项\r\n\t    attachmentsList:[], // 文件列表\r\n\t    \r\n\t    // PDF渲染状态管理\r\n\t    responsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n\t    procurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n      srcList: [\"/evalution/help.jpg\"],\r\n\t    \r\n\t    // 悬停状态管理\r\n\t    hoveredFactorNode: null, // 悬停时的评分项\r\n\t    tooltipTimer: null, // 悬浮框显示定时器\r\n    };\r\n  },\r\n  methods: {\r\n    /**\r\n     * 限制输入框只能输入数字和小数点\r\n     * @param {Event} event - 键盘事件\r\n     */\r\n    onlyNumber(event) {\r\n      // 获取按键的字符码\r\n      const charCode = event.which || event.keyCode;\r\n\r\n      // 允许的字符：数字(48-57)、小数点(46)、退格(8)、删除(46)、Tab(9)、Enter(13)、方向键(37-40)\r\n      if (\r\n        (charCode >= 48 && charCode <= 57) || // 数字 0-9\r\n        charCode === 46 || // 小数点\r\n        charCode === 8 ||  // 退格键\r\n        charCode === 9 ||  // Tab键\r\n        charCode === 13 || // Enter键\r\n        (charCode >= 37 && charCode <= 40) // 方向键\r\n      ) {\r\n        return true;\r\n      }\r\n\r\n      // 阻止其他字符输入\r\n      event.preventDefault();\r\n      return false;\r\n    },\r\n\r\n    /**\r\n     * 处理分数输入，确保只能输入有效的数字\r\n     * @param {string} itemId - 评估项ID\r\n     * @param {string} value - 输入值\r\n     */\r\n    handleScoreInput(itemId, value) {\r\n      // 移除非数字字符（保留小数点）\r\n      let cleanValue = value.replace(/[^\\d.]/g, '');\r\n\r\n      // 确保只有一个小数点\r\n      const parts = cleanValue.split('.');\r\n      if (parts.length > 2) {\r\n        cleanValue = parts[0] + '.' + parts.slice(1).join('');\r\n      }\r\n\r\n      // 限制小数点后最多2位\r\n      if (parts.length === 2 && parts[1].length > 2) {\r\n        cleanValue = parts[0] + '.' + parts[1].substring(0, 2);\r\n      }\r\n\r\n      // 更新值\r\n      this.defaultRatingArray[itemId].state = cleanValue;\r\n\r\n      // 调用原有的验证方法\r\n      this.validateScore(itemId, cleanValue);\r\n    },\r\n\r\n    init() {\r\n      const expertInfo = JSON.parse(localStorage.getItem(\"expertInfo\"));\r\n      this.entDocResponsePage = JSON.parse(\r\n        localStorage.getItem(\"entDocResponsePage\")\r\n      );\r\n      // 初始化采购文件页码信息\r\n      this.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\"));\r\n      supplierInfo({ projectId: this.$route.query.projectId }).then(\r\n        (response) => {\r\n          if (response.code == 200) {\r\n            this.options = response.rows.filter(item => item.isAbandonedBid == 0);\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        }\r\n      );\r\n      approvalProcess(this.$route.query.projectId, expertInfo.resultId).then(\r\n        (response) => {\r\n          if (response.code == 200) {\r\n\t\t\t\t\t\t// 文件列表\r\n\t          this.attachmentsList = response.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n\t\t\t\t\t\t\r\n            this.scoringSystem =\r\n              response.data.scoringMethodUinfo.scoringMethodItems.find(\r\n                (item) => {\r\n                  return (\r\n                    item.scoringMethodItemId ==\r\n                    this.$route.query.scoringMethodItemId\r\n                  );\r\n                }\r\n              );\r\n            localStorage.setItem(\r\n              \"evalProjectEvaluationProcess\",\r\n              JSON.stringify(this.scoringSystem.evalProjectEvaluationProcess)\r\n            );\r\n            // TODO 首先生成长度为this.scoringSystem.length的数组，结构为{state：false，reason：“”}\r\n            this.defaultRatingArray = this.scoringSystem.uitems.reduce(\r\n              (acc, _, index) => {\r\n                acc[this.scoringSystem.uitems[index].entMethodItemId] = {\r\n                  state: null,\r\n                  reason: \"\",\r\n                };\r\n                console.log(\"scoringSystem\", this.scoringSystem);\r\n                return acc;\r\n              },\r\n              {}\r\n            );\r\n          } else {\r\n            this.$messgae.warning(response.msg);\r\n          }\r\n        }\r\n      );\r\n      filesById(this.$route.query.projectId).then((response) => {\r\n        if (response.code == 200) {\r\n          this.file = response.data;\r\n          if (this.file.tenderNoticeFilePath != undefined) {\r\n            this.procurementPdf = this.file.tenderNoticeFilePath;\r\n          }\r\n          // if (this.file.file != undefined) {\r\n          //   this.responsePdf = this.file.file[0];\r\n          // }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      // 初始化专家信息\r\n      this.initExpertInfo();\r\n    },\r\n\r\n    /**\r\n     * 初始化专家信息\r\n     */\r\n    initExpertInfo() {\r\n      try {\r\n        const itemString = localStorage.getItem(\"expertInfo\");\r\n        if (itemString) {\r\n          this.expertInfo = JSON.parse(itemString);\r\n          console.log(\"专家信息已初始化\", this.expertInfo);\r\n        } else {\r\n          console.warn(\"localStorage中未找到expertInfo\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"初始化专家信息失败:\", error);\r\n      }\r\n    },\r\n\r\n    handleChange(value) {\r\n      if(Object.keys(this.selectSupplier).length != 0){\r\n        this.tmpSave();\r\n      }\r\n      this.selectSupplier = this.options.find((item) => {\r\n        return item.bidderName == value;\r\n      });\r\n\r\n      // 根据bidderid获取供应商因素及其对应页码\r\n      this.bidderFactor = this.factorsPage[this.selectSupplier.bidderId];\r\n\r\n      const data = {\r\n        expertResultId: this.expertInfo.resultId,\r\n        projectId: this.$route.query.projectId,\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n      };\r\n      getDetailByPsxx(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.factorList = response.data;\r\n          const factor = this.factorList.find((item) => {\r\n            return item.bidderName == value;\r\n          }).evalExpertEvaluationDetails;\r\n          if (factor != null) {\r\n            factor.map((item) => {\r\n              this.defaultRatingArray[item.scoringMethodUitemId].reason =\r\n                item.evaluationRemark;\r\n              this.defaultRatingArray[item.scoringMethodUitemId].state =\r\n                item.evaluationResult;\r\n            });\r\n          } else {\r\n            Object.keys(this.defaultRatingArray).forEach((key) => {\r\n              this.defaultRatingArray[key].state = null;\r\n              this.defaultRatingArray[key].reason = \"\";\r\n            });\r\n          }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      this.showResponseFile();\r\n      // 重置最大分数值\r\n      // this.currentMaxScore = null;\r\n    },\r\n\t  \r\n\t  \r\n    validateScore(itemId, event) {\r\n      const inputValue = parseFloat(event);\r\n      console.log(\"inputValue\", inputValue);\r\n      \r\n      // 获取当前评分项的最大分值\r\n      const currentItem = this.scoringSystem.uitems.find(item => item.entMethodItemId === itemId);\r\n      let maxScore = null;\r\n      \r\n      if (currentItem) {\r\n        // 如果有分数挡位，使用挡位中的最大值\r\n        if (currentItem.scoreLevel && currentItem.scoreLevel.length > 0) {\r\n          const scoreLevels = currentItem.scoreLevel.split(',').map(item => parseFloat(item.trim())).filter(item => !isNaN(item));\r\n          if (scoreLevels.length > 0) {\r\n            maxScore = Math.max(...scoreLevels);\r\n          }\r\n        } else {\r\n          // 否则使用配置的最大分值\r\n          maxScore = parseFloat(currentItem.score);\r\n        }\r\n      }\r\n      \r\n      console.log(\"maxScore\", maxScore);\r\n\r\n      if (!isNaN(inputValue) && maxScore !== null) {\r\n        if (inputValue > maxScore) {\r\n          this.$message.warning(`输入分数不能超过${maxScore}分，请重新输入`);\r\n          // 将输入值限制为最大分数值\r\n          this.defaultRatingArray[itemId].state = \"\";\r\n        } else if (inputValue < 0) {\r\n          this.$message.warning(\"输入分数不能小于0分\");\r\n          this.defaultRatingArray[itemId].state = \"\";\r\n        }\r\n      }\r\n    },\r\n    showResponseFile() {\r\n      if (Object.keys(this.selectSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        this.activeButton = 'response'; // 设置当前激活按钮\r\n        this.double = false;\r\n        this.procurementShow = false;\r\n        this.responseShow = true;\r\n        this.responsePdf = this.file.file[this.selectSupplier.bidderId];\r\n      }\r\n    },\r\n    fileContrast() {\r\n      if (Object.keys(this.selectSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        this.activeButton = 'contrast'; // 设置当前激活按钮\r\n        this.double = true;\r\n        this.procurementShow = true;\r\n        this.responseShow = true;\r\n        this.responsePdf = this.file.file[this.selectSupplier.bidderId];\r\n      }\r\n    },\r\n    showInfo(item) {\r\n\t    // 检查PDF是否渲染完成\r\n\t    if (!this.canJumpToPage()) {\r\n\t\t    this.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t    return;\r\n\t    }\r\n\t\t\t\r\n      this.selectNode = item;\r\n\r\n      // 如果只显示采购文件，使用采购文件页码信息\r\n      if (this.procurementShow && !this.responseShow) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        if (item.jumpToPage) {\r\n          this.$refs.procurement.skipPage(item.jumpToPage);\r\n        } else if (this.entDocProcurementPage && this.entDocProcurementPage[item.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[item.itemName]);\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 如果显示响应文件或对比模式，需要选择供应商\r\n      if (Object.keys(this.bidderFactor).length != 0) {\r\n        // 跳转到响应文件对应页码\r\n        if (this.responseShow && this.$refs.response) {\r\n\t        if (!this.responsePdfRendered) {\r\n\t\t        this.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t        return;\r\n\t        }\r\n          this.$refs.response.skipPage(\r\n            this.bidderFactor[this.selectNode.itemName]\r\n          );\r\n        }\r\n\r\n        // 跳转到采购文件对应页码\r\n        if (this.procurementShow && this.$refs.procurement) {\r\n\t        if (!this.procurementPdfRendered) {\r\n\t\t        this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t        return;\r\n\t        }\r\n\t\t\t\t\t\r\n          // 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n          if (this.entDocProcurementPage && this.entDocProcurementPage[item.itemName]) {\r\n            this.$refs.procurement.skipPage(this.entDocProcurementPage[item.itemName]);\r\n          } else {\r\n            // 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n            // 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n          }\r\n        }\r\n\r\n        // 获取当前项目的最大分数值，假设scoreLevel中第一个值为最大值（可根据实际规则调整）\r\n        // const maxScore = item.score;\r\n        // console.log(\"此项目最大分值是：\"+maxScore);\r\n        // this.currentMaxScore = maxScore; // 将最大分数值存储到实例变量中，方便后续校验使用\r\n      } else {\r\n        this.$message.warning(\"请先选择供应商\");\r\n      }\r\n    },\r\n    initDefaultRatingArray(){\r\n      Object.keys(this.defaultRatingArray).forEach((key) => {\r\n        this.defaultRatingArray[key].state = null;\r\n        this.defaultRatingArray[key].reason = \"\";\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 校验所有评分项是否填写完整\r\n     * @returns {boolean} 是否全部填写\r\n     */\r\n    validateAllRatings() {\r\n      for (const item of this.scoringSystem.uitems) {\r\n        const state = this.defaultRatingArray[item.entMethodItemId].state;\r\n\r\n        // 评分结果未填写\r\n        if (state === null || state === '' || state === undefined) {\r\n          // this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n          return true;\r\n        }\r\n\r\n        // 对于分数评分，检查是否为有效数值\r\n        if (!item.scoreLevel || item.scoreLevel.length === 0 || item.scoreLevel === null || item.scoreLevel === undefined) {\r\n          const score = parseFloat(state);\r\n          if (isNaN(score) || score < 0) {\r\n            this.$message.warning(`${item.itemName}的评分必须是有效的数值且不能小于0`);\r\n            return false;\r\n          }\r\n          // 检查分数是否超过最大值\r\n          const maxScore = this.getMaxScore(item);\r\n          if (score > maxScore) {\r\n            this.$message.warning(`${item.itemName}的评分不能超过${maxScore}分`);\r\n            return false;\r\n          }\r\n        } else {\r\n          // 对于有挡位的评分，检查是否在允许的挡位范围内\r\n          const scoreLevels = item.scoreLevel.split(',').map(level => level.trim());\r\n          if (!scoreLevels.includes(state.toString())) {\r\n            this.$message.warning(`${item.itemName}的评分必须选择指定的挡位：${item.scoreLevel}`);\r\n            return false;\r\n          }\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n\r\n    tmpSave(){\r\n      console.log(\"-------开始保存评审结果----------------\");\r\n\r\n      // 先校验所有评分项是否填写完整\r\n      if (!this.validateAllRatings()) {\r\n        return Promise.resolve({ code: 0, success: false }); // 校验失败\r\n      }\r\n\r\n      var ratingArray = JSON.parse(JSON.stringify(this.defaultRatingArray));\r\n\r\n      var data = [];\r\n      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        const item = this.scoringSystem.uitems[index];\r\n        const itemId = item.entMethodItemId;\r\n        // 获取当前项对应的评分结果\r\n        const evaluationResult = ratingArray[itemId].state;\r\n        if (evaluationResult === null || evaluationResult === \"\") {\r\n          // 如果评分结果为空，则不保存此条信息\r\n          console.log(\"-------评分结果为空，不保存此条信息----------------\");\r\n          continue;\r\n        }\r\n          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n          const evaluationRemark = ratingArray[itemId].reason || \"\";\r\n          data.push({\r\n            scoringMethodUitemId: itemId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            entId: this.selectSupplier.bidderId,\r\n            evaluationResult: evaluationResult,\r\n            evaluationRemark: evaluationRemark\r\n          });\r\n        }\r\n        if(data.length>0){\r\n            console.log(\"-------开始后台保存评审结果----------------\");\r\n            return scoringFactors(data).then((response) => {\r\n              console.log(response.msg);\r\n              if (response.code == 200) {\r\n                this.$message.success(\"保存成功\");\r\n                return { code: 200, success: true };\r\n              } else {\r\n                this.$message.warning(response.msg);\r\n                return { code: response.code, success: false };\r\n              }\r\n            }).catch((error) => {\r\n              this.$message.error(\"保存失败\");\r\n              return { code: 0, success: false };\r\n            });\r\n        }else{\r\n          return Promise.resolve({ code: 200, success: true }); // 没有数据需要保存时也返回成功\r\n        }\r\n    },\r\n    save() {\r\n      if (this.supplier == \"\") {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        //const data = this.generatingSavedData();\r\n        var data = [];\r\n        for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n          const item = this.scoringSystem.uitems[index];\r\n          const itemId = item.entMethodItemId;\r\n          // 获取当前项对应的评分结果\r\n          const evaluationResult = this.defaultRatingArray[itemId].state;\r\n          if (evaluationResult === null || evaluationResult === \"\") {\r\n            // 如果评分结果为空，弹出提示，提示内容包含该项的itemName\r\n            this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n            return; // 直接返回，不再继续构建数据，等待用户填写完整\r\n          }\r\n          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n          const evaluationRemark = this.defaultRatingArray[itemId].reason || \"\";\r\n          data.push({\r\n            scoringMethodUitemId: itemId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            entId: this.selectSupplier.bidderId,\r\n            evaluationResult: evaluationResult,\r\n            evaluationRemark: evaluationRemark\r\n          });\r\n        }\r\n\r\n        scoringFactors(data).then((response) => {\r\n          if (response.code == 200) {\r\n            this.$message.success(response.msg);\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 生成保存数据\r\n    generatingSavedData() {\r\n      var data = [];\r\n      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        const item = this.scoringSystem.uitems[index];\r\n        const itemId = item.entMethodItemId;\r\n        // 获取当前项对应的评分结果\r\n        const evaluationResult = this.defaultRatingArray[itemId].state;\r\n        if (evaluationResult === null || evaluationResult === \"\") {\r\n          // 如果评分结果为空，弹出提示，提示内容包含该项的itemName\r\n          this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n          return; // 直接返回，不再继续构建数据，等待用户填写完整\r\n        }\r\n        // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n        const evaluationRemark = this.defaultRatingArray[itemId].reason || \"\";\r\n        data.push({\r\n          scoringMethodUitemId: itemId,\r\n          expertResultId: this.expertInfo.resultId,\r\n          entId: this.selectSupplier.bidderId,\r\n          evaluationResult: evaluationResult,\r\n          evaluationRemark: evaluationRemark\r\n        });\r\n      }\r\n\r\n      /*for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        data.push({\r\n          scoringMethodUitemId:\r\n            this.scoringSystem.uitems[index].entMethodItemId,\r\n          expertResultId: this.expertInfo.resultId,\r\n          entId: this.selectSupplier.bidderId,\r\n          evaluationResult:\r\n            this.defaultRatingArray[\r\n              this.scoringSystem.uitems[index].entMethodItemId\r\n            ].state,\r\n          evaluationRemark:\r\n            this.defaultRatingArray[\r\n              this.scoringSystem.uitems[index].entMethodItemId\r\n            ].reason,\r\n        });\r\n      }*/\r\n      return data;\r\n    },\r\n    submit() {\r\n        this.tmpSave().then((saveResult) => {\r\n          // 检查保存结果，如果校验失败则不继续提交\r\n          if (!saveResult || saveResult.success === false) {\r\n            return; // 校验失败，不继续提交流程\r\n          }\r\n\r\n          const data = {\r\n            projectId: this.$route.query.projectId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n          };\r\n          checkReviewSummary(data).then((response) => {\r\n            if (response.code == 200) {\r\n            // 修改专家进度\r\n            const status = {\r\n              evalExpertScoreInfoId: JSON.parse(\r\n                localStorage.getItem(\"evalExpertScoreInfo\")\r\n              ).evalExpertScoreInfoId,\r\n              evalState: 1,\r\n            };\r\n            editEvalExpertScoreInfo(status).then((res) => {\r\n              if (res.code == 200) {\r\n                this.$message.success(\"提交成功\");\r\n              }\r\n            });\r\n            this.$emit(\"send\", \"two\");\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      })\r\n    },\r\n    /**\r\n     * 显示采购文件PDF\r\n     */\r\n    viewPurchasing() {\r\n      this.activeButton = 'procurement'; // 设置当前激活按钮\r\n      this.double = false; // 单文件模式\r\n      this.responseShow = false; // 不显示响应文件\r\n      this.procurementShow = true; // 显示采购文件\r\n\r\n      // 右侧评分项显示为采购文件的评分项\r\n      let pageProcurementArr = []; // 采购文件评分项数组\r\n      for (let item in this.entDocProcurementPage){\r\n        pageProcurementArr.push({\r\n          itemName: item,\r\n          jumpToPage: this.entDocProcurementPage[item]\r\n        })\r\n      }\r\n\r\n      console.log(this.scoringSystem.uitems);\r\n      console.log(pageProcurementArr)\r\n      this.pageProcurement = [];\r\n      for (let i = 0; i < this.scoringSystem.uitems.length;i++){\r\n        for (let j = 0; j < pageProcurementArr.length;j++){\r\n          if (this.scoringSystem.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n            this.pageProcurement.push({...this.scoringSystem.uitems[i],...pageProcurementArr[j]});\r\n          }\r\n        }\r\n      }\r\n      console.log(this.pageProcurement)\r\n    },\r\n\r\n    /**\r\n     * 跳转到采购文件对应页码\r\n     * @param {Object} item - 评分项对象\r\n     */\r\n    jumpToProcurementPage(item) {\r\n      if (item.jumpToPage && this.$refs.procurement) {\r\n        this.$refs.procurement.skipPage(item.jumpToPage);\r\n      }\r\n    },\r\n\t  \r\n\t  /**\r\n\t   * 检查是否可以跳转页面\r\n\t   * @returns {boolean} 是否可以跳转\r\n\t   */\r\n\t  canJumpToPage() {\r\n\t\t  // 如果只显示采购文件\r\n\t\t  if (this.procurementShow && !this.responseShow) {\r\n\t\t\t  return this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  // 如果只显示响应文件\r\n\t\t  if (this.responseShow && !this.procurementShow) {\r\n\t\t\t  return this.responsePdfRendered;\r\n\t\t  }\r\n\t\t  // 如果对比模式（两个都显示）\r\n\t\t  if (this.responseShow && this.procurementShow) {\r\n\t\t\t  return this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  return false;\r\n\t  },\r\n\t  /**\r\n\t   * 处理PDF渲染状态变化\r\n\t   * @param {boolean} isRendered 是否渲染完成\r\n\t   * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t   */\r\n\t  handlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t\t  if (pdfType === 'response') {\r\n\t\t\t  this.responsePdfRendered = isRendered;\r\n\t\t  } else if (pdfType === 'procurement') {\r\n\t\t\t  this.procurementPdfRendered = isRendered;\r\n\t\t  }\r\n\t\t  \r\n\t\t  if (isRendered) {\r\n\t\t\t  console.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t\t  }\r\n\t  },\r\n\t  \r\n    // 跳转到二次报价\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query: query });\r\n    },\r\n    // 跳转到询标\r\n    bidInquiry() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      console.log(\"query\", query);\r\n      this.$router.push({ path: \"/bidInquiry\", query: query });\r\n    },\r\n    // 获取因素对应页码\r\n    getFactorsPage() {\r\n      this.factorsPage = JSON.parse(localStorage.getItem(\"entDocResponsePage\"));\r\n    },\r\n    \r\n    /**\r\n     * 获取评分项的最大分值\r\n     * @param {Object} item - 评分项对象\r\n     * @returns {number} 最大分值\r\n     */\r\n    getMaxScore(item) {\r\n      if (!item) return 0;\r\n      \r\n      // 如果有分数挡位，使用挡位中的最大值\r\n      if (item.scoreLevel && item.scoreLevel.length > 0 && item.scoreLevel !== null && item.scoreLevel !== undefined) {\r\n        const scoreLevels = item.scoreLevel.split(',').map(level => parseFloat(level.trim())).filter(level => !isNaN(level));\r\n        if (scoreLevels.length > 0) {\r\n          return Math.max(...scoreLevels);\r\n        }\r\n      }\r\n      \r\n      // 否则使用配置的最大分值\r\n      return parseFloat(item.score) || 0;\r\n    },\r\n\t  downloadFile(item){\r\n\t\t\tthis.$download.zip(item.filePath,item.fileName);\r\n\t  },\r\n\t  \r\n\t  // ========== 悬停相关 ==========\r\n\t  /**\r\n\t   * 显示评分项悬浮框\r\n\t   * @param {Object} factorItem 评分项对象\r\n\t   */\r\n\t  showFactorTooltip(factorItem) {\r\n\t\t  if (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\t\t  \r\n\t\t  // 清除之前的定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t  this.tooltipTimer = setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = factorItem;\r\n\t\t  }, 300); // 300ms延迟\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 隐藏评分项悬浮框\r\n\t   */\r\n\t  hideFactorTooltip() {\r\n\t\t  // 清除定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t  setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = null;\r\n\t\t  }, 100);\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t   */\r\n\t  clearTooltipTimer() {\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t  }\r\n  },\r\n  mounted() {\r\n    this.init();\r\n    this.getFactorsPage();\r\n  },\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// Main layout containers\r\n.technical-review-container {\r\n  min-height: 57vh;\r\n  display: flex;\r\n}\r\n\r\n.main-content {\r\n  min-height: 57vh;\r\n  width: 79%;\r\n}\r\n\r\n// Header section styles\r\n.header-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n\r\n.title-section {\r\n  display: flex;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333333;\r\n}\r\n\r\n.main-title {\r\n  // Inherits from title-section\r\n}\r\n\r\n.help-section {\r\n  display: grid;\r\n  justify-items: center;\r\n  position: relative;\r\n  bottom: -30px;\r\n}\r\n\r\n.help-text {\r\n  font-size: 12px;\r\n}\r\n\r\n.help-image {\r\n  width: 80px;\r\n  height: 30px;\r\n  margin-right: 20px;\r\n}\r\n\r\n// File list container\r\n.file-list-container {\r\n  border-right: 1px solid #e6e6e6;\r\n  border-left: 1px solid #e6e6e6;\r\n  padding: 10px;\r\n  overflow-y: auto;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\ttransition: all 0.3s ease;\r\n\t::v-deep .el-card__body {\r\n\t\tpadding: 0;\r\n\t}\r\n}\r\n\r\n.file-list-title {\r\n  font-weight: bold;\r\n  margin-bottom: 10px;\r\n  color: #333;\r\n}\r\n\r\n.file-item {\r\n  margin-bottom: 8px;\r\n  cursor: pointer;\r\n}\r\n\r\n.file-item-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 5px;\r\n}\r\n\r\n.file-icon {\r\n  margin-right: 8px;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-name {\r\n  font-size: 12px;\r\n  flex: 1;\r\n  word-break: break-all;\r\n}\r\n\r\n.download-icon {\r\n  margin-left: 8px;\r\n  color: #999;\r\n}\r\n\r\n// Action buttons section\r\n.action-buttons {\r\n  text-align: right;\r\n}\r\n\r\n.button-group {\r\n  margin-top: 20px;\r\n\t.item-button{\r\n\t\tcolor: #fff;\r\n\t}\r\n}\r\n\r\n.primary-btn {\r\n  background-color: #176ADB;\r\n  color: #FFFFFF;\r\n  border: 1px solid #176ADB;\r\n}\r\n\r\n// Content section styles\r\n.content-section {\r\n  display: flex;\r\n  height: 82%;\r\n}\r\n\r\n// PDF preview area\r\n.pdf-preview-area {\r\n  display: flex;\r\n  justify-content: center;\r\n  flex: 1;\r\n}\r\n\r\n.pdf-panel {\r\n  width: 49%;\r\n}\r\n\r\n.response-panel {\r\n  &.border-right {\r\n    border-right: 1px solid #176ADB;\r\n  }\r\n}\r\n\r\n.procurement-panel {\r\n  &.border-left {\r\n    border-left: 1px solid #176ADB;\r\n  }\r\n}\r\n\r\n// Divider styles\r\n.divider {\r\n  min-height: 57vh;\r\n  width: 1%;\r\n  background-color: #F5F5F5;\r\n}\r\n\r\n// Sidebar styles\r\n.sidebar {\r\n  min-height: 57vh;\r\n  width: 20%;\r\n}\r\n\r\n.sidebar-header {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n\r\n.supplier-select {\r\n  width: 100%;\r\n}\r\n\r\n.sidebar-content {\r\n  padding: 15px 20px;\r\n}\r\n\r\n// Factor items styles\r\n.factor-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.factor-header {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  text-align: left;\r\n  width: 98%;\r\n}\r\n\r\n.factor-name {\r\n  cursor: pointer;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 500;\r\n  font-size: 16px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n}\r\n\r\n.max-score {\r\n  font-size: 12px;\r\n  color: red;\r\n}\r\n\r\n.factor-input {\r\n  display: flex;\r\n  width: 100%;\r\n  justify-content: flex-end;\r\n  padding: 10px;\r\n}\r\n\r\n.score-value {\r\n  color: green;\r\n  font-size: 16px;\r\n}\r\n\r\n// Submit section\r\n.submit-section {\r\n  display: flex;\r\n  margin: 32px 0;\r\n  justify-content: space-evenly;\r\n}\r\n\r\n// Review content styles\r\n.review-content {\r\n  text-align: left;\r\n  font-size: 14px;\r\n}\r\n\r\n.review-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 15px;\r\n  color: #176ADB;\r\n  letter-spacing: 0;\r\n}\r\n\r\n.review-text {\r\n  padding: 6px 30px;\r\n}\r\n\r\n// Existing styles\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n\t  transition: all 0.3s ease;\r\n\t  padding: 4px 8px;\r\n\t  border-radius: 4px;\r\n\t  \r\n\t  &:hover {\r\n\t\t  background-color: #f0f8ff;\r\n\t\t  color: #176ADB;\r\n\t\t  transform: translateX(2px);\r\n\t  }\r\n  }\r\n}\r\n.item-button {\r\n  border: 1px solid #979797;\r\n  width: 150px;\r\n  height: 36px;\r\n  margin: 0 10px;\r\n  font-weight: 700;\r\n  font-size: 17px;\r\n  border-radius: 6px;\r\n  color: #333333;\r\n  &:hover {\r\n    color: #333333;\r\n  }\r\n}\r\n.technical-blue-btn {\r\n  background-color: #176ADB !important;\r\n  color: #fff !important;\r\n  border: 1px solid #176ADB !important;\r\n}\r\n.technical-blue-btn-active {\r\n  background-color: #FF6B35 !important;\r\n  color: #fff !important;\r\n  border: 1px solid #FF6B35 !important;\r\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.item-button-little {\r\n  width: 124px;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #fff;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  }\r\n\r\n  ::v-deep .el-card__body {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\t\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\t\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\t\t\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\t\t\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\t\t\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n</style>\r\n\r\n"]}]}