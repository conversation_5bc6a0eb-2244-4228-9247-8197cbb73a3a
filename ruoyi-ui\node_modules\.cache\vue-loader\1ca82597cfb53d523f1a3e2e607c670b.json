{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\compliance\\one.vue?vue&type=style&index=0&id=c4741b56&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\compliance\\one.vue", "mtime": 1753922915380}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmNvbXBsaWFuY2UtbWFpbiB7DQogIG1pbi1oZWlnaHQ6IDU3dmg7DQogIGRpc3BsYXk6IGZsZXg7DQp9DQouY29tcGxpYW5jZS1sZWZ0IHsNCiAgbWluLWhlaWdodDogNTd2aDsNCiAgd2lkdGg6IDc5JTsNCn0NCi5jb21wbGlhbmNlLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICMxNzZBREI7DQogIHBhZGRpbmc6IDE1cHggMjBweDsNCn0NCi5jb21wbGlhbmNlLXRpdGxlLWdyb3VwIHsNCiAgZGlzcGxheTogZmxleDsNCiAgaGVpZ2h0OiAzNnB4Ow0KICBmb250LXdlaWdodDogNzAwOw0KICBmb250LXNpemU6IDI0cHg7DQogIGNvbG9yOiAjMzMzOw0KfQ0KLmNvbXBsaWFuY2UtdGl0bGUgew0KICAvLyBub3RoaW5nIGV4dHJhDQp9DQouY29tcGxpYW5jZS1zdGVwLWltZy1ncm91cCB7DQogIGRpc3BsYXk6IGdyaWQ7DQogIGp1c3RpZnktaXRlbXM6IGNlbnRlcjsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBib3R0b206IC0zMHB4Ow0KfQ0KLmNvbXBsaWFuY2Utc3RlcC10ZXh0IHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KLmNvbXBsaWFuY2Utc3RlcC1pbWcgew0KICB3aWR0aDogODBweDsNCiAgaGVpZ2h0OiAzMHB4Ow0KICBtYXJnaW4tcmlnaHQ6IDIwcHg7DQp9DQouY29tcGxpYW5jZS1oZWFkZXItYnRucyB7DQogIHRleHQtYWxpZ246IHJpZ2h0Ow0KfQ0KLmNvbXBsaWFuY2UtaGVhZGVyLWJ0bnMtYm90dG9tIHsNCiAgbWFyZ2luLXRvcDogMjBweDsNCn0NCi5jb21wbGlhbmNlLWJsdWUtYnRuIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzE3NkFEQiAhaW1wb3J0YW50Ow0KICBjb2xvcjogI2ZmZiAhaW1wb3J0YW50Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjMTc2QURCICFpbXBvcnRhbnQ7DQp9DQouY29tcGxpYW5jZS1ibHVlLWJ0bi1hY3RpdmUgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjRkY2QjM1ICFpbXBvcnRhbnQ7DQogIGNvbG9yOiAjZmZmICFpbXBvcnRhbnQ7DQogIGJvcmRlcjogMXB4IHNvbGlkICNGRjZCMzUgIWltcG9ydGFudDsNCiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMjU1LCAxMDcsIDUzLCAwLjMpICFpbXBvcnRhbnQ7DQp9DQouY29tcGxpYW5jZS1wZGYtZ3JvdXAgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgaGVpZ2h0OiA4MiU7DQp9DQouY29tcGxpYW5jZS1wZGYgew0KICB3aWR0aDogNDklOw0KfQ0KLmNvbXBsaWFuY2UtcGRmLWJvcmRlci1yaWdodCB7DQogIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICMxNzZBREI7DQp9DQouY29tcGxpYW5jZS1wZGYtYm9yZGVyLWxlZnQgew0KICBib3JkZXItbGVmdDogMXB4IHNvbGlkICMxNzZBREI7DQp9DQouY29tcGxpYW5jZS1kaXZpZGVyIHsNCiAgbWluLWhlaWdodDogNTd2aDsNCiAgd2lkdGg6IDElOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjVGNUY1Ow0KfQ0KLmNvbXBsaWFuY2UtcmlnaHQgew0KICBtaW4taGVpZ2h0OiA1N3ZoOw0KICB3aWR0aDogMjAlOw0KfQ0KLmNvbXBsaWFuY2Utc2VsZWN0LWdyb3VwIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMTc2QURCOw0KICBwYWRkaW5nOiAxNXB4IDIwcHg7DQp9DQouY29tcGxpYW5jZS1zZWxlY3Qgew0KICB3aWR0aDogMTAwJTsNCn0NCi5jb21wbGlhbmNlLWZhY3RvcnMtZ3JvdXAgew0KICBwYWRkaW5nOiAxNXB4IDIwcHg7DQp9DQouY29tcGxpYW5jZS1mYWN0b3ItdGl0bGUtZ3JvdXAgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHRleHQtYWxpZ246IGxlZnQ7DQogIHdpZHRoOiA5OCU7DQp9DQouY29tcGxpYW5jZS1mYWN0b3ItdGl0bGUgew0KICBjdXJzb3I6IHBvaW50ZXI7DQogIGZvbnQtZmFtaWx5OiBTb3VyY2VIYW5TYW5zU0MtQm9sZDsNCiAgZm9udC13ZWlnaHQ6IDcwMDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBjb2xvcjogIzMzMzsNCiAgbGV0dGVyLXNwYWNpbmc6IDA7DQp9DQouY29tcGxpYW5jZS1mYWN0b3ItcmFkaW8tZ3JvdXAgew0KICBkaXNwbGF5OiBmbGV4Ow0KICB3aWR0aDogMTAwJTsNCiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsNCn0NCi5jb21wbGlhbmNlLWZhY3Rvci1kaXZpZGVyIHsNCiAgaGVpZ2h0OiAxcHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNEQ0RGRTY7DQogIG1hcmdpbi10b3A6IDEwcHg7DQp9DQouY29tcGxpYW5jZS1zdWJtaXQtZ3JvdXAgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBtYXJnaW46IDMycHggMDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1ldmVubHk7DQp9DQouY29tcGxpYW5jZS1zdWJtaXQtYnRuIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzE3NkFEQjsNCn0NCi5jb21wbGlhbmNlLXJldmlldy1jb250ZW50IHsNCiAgdGV4dC1hbGlnbjogbGVmdDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KLmNvbXBsaWFuY2UtcmV2aWV3LXRpdGxlIHsNCiAgZm9udC1mYW1pbHk6IFNvdXJjZUhhblNhbnNTQy1Cb2xkOw0KICBmb250LXdlaWdodDogNzAwOw0KICBmb250LXNpemU6IDE1cHg7DQogIGNvbG9yOiAjMTc2QURCOw0KICBsZXR0ZXItc3BhY2luZzogMDsNCn0NCi5jb21wbGlhbmNlLXJldmlldy1odG1sIHsNCiAgcGFkZGluZzogNnB4IDMwcHg7DQp9DQouaXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBmb250LXNpemU6IDE4cHg7DQogIG1hcmdpbi1ib3R0b206IDgwcHg7DQogIC5pdGVtLXRpdGxlIHsNCiAgICB3aWR0aDogMTIwcHg7DQogICAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICAgIHRleHQtYWxpZ246IGxlZnQ7DQoJICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KCSAgcGFkZGluZzogNHB4IDhweDsNCgkgIGJvcmRlci1yYWRpdXM6IDRweDsNCgkgIA0KCSAgJjpob3ZlciB7DQoJCSAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjhmZjsNCgkJICBjb2xvcjogIzE3NkFEQjsNCgkJICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMnB4KTsNCgkgIH0NCiAgfQ0KfQ0KLml0ZW0tYnV0dG9uIHsNCiAgYm9yZGVyOiAxcHggc29saWQgIzk3OTc5NzsNCiAgd2lkdGg6IDE1MHB4Ow0KICBoZWlnaHQ6IDM2cHg7DQogIG1hcmdpbjogMCAxMHB4Ow0KICBmb250LXdlaWdodDogNzAwOw0KICBmb250LXNpemU6IDE3cHg7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgY29sb3I6ICMzMzM7DQogICY6aG92ZXIgew0KICAgIGNvbG9yOiAjMzMzOw0KICB9DQp9DQouaXRlbS1idXR0b24tbGl0dGxlIHsNCiAgd2lkdGg6IDEyNHB4Ow0KICBoZWlnaHQ6IDM2cHg7DQogIGZvbnQtd2VpZ2h0OiA3MDA7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgY29sb3I6ICNmZmY7DQogICY6aG92ZXIgew0KICAgIGNvbG9yOiAjZmZmOw0KICB9DQp9DQouZmFjdG9ycyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KLnRleHQgew0KICA6OnYtZGVlcCAuZWwtdGV4dGFyZWFfX2lubmVyIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1Ow0KICAgIGJvcmRlci1yYWRpdXM6IDA7DQogICAgYm9yZGVyOiAxcHggc29saWQgI2Y1ZjVmNTsNCiAgfQ0KfQ0KLmZpbGVMaXN0IHsNCglkaXNwbGF5OiBmbGV4Ow0KCWFsaWduLWl0ZW1zOiBjZW50ZXI7DQoJZ2FwOiAyMHB4Ow0KCWZsZXg6IDE7DQoJZmxleC13cmFwOiB3cmFwOw0KCS5maWxlSXRlbSB7DQoJCXRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQoJCSY6aG92ZXIgew0KCQkJdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOw0KCQkJYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpOw0KCQl9DQoJCQ0KCQk6OnYtZGVlcCAuZWwtY2FyZF9fYm9keSB7DQoJCQlwYWRkaW5nOiAwOw0KCQl9DQoJfQ0KfQ0KDQovLyBQREbmuLLmn5PnirbmgIHmj5DnpLrmoLflvI8NCi5yZW5kZXItc3RhdHVzLXRpcCB7DQoJZGlzcGxheTogZmxleDsNCglhbGlnbi1pdGVtczogY2VudGVyOw0KCXBhZGRpbmc6IDEwcHggMTVweDsNCgltYXJnaW4tYm90dG9tOiAxNXB4Ow0KCWJvcmRlci1yYWRpdXM6IDRweDsNCgliYWNrZ3JvdW5kLWNvbG9yOiAjZmZmN2U2Ow0KCWJvcmRlcjogMXB4IHNvbGlkICNmZmQ1OTE7DQoJY29sb3I6ICNkNDg4MDY7DQoJZm9udC1zaXplOiAxNHB4Ow0KCQ0KCWkgew0KCQltYXJnaW4tcmlnaHQ6IDhweDsNCgkJZm9udC1zaXplOiAxNnB4Ow0KCX0NCgkNCgkmLnN1Y2Nlc3Mgew0KCQliYWNrZ3JvdW5kLWNvbG9yOiAjZjZmZmVkOw0KCQlib3JkZXItY29sb3I6ICNiN2ViOGY7DQoJCWNvbG9yOiAjNTJjNDFhOw0KCX0NCn0NCg0KLy8g56aB55So54q25oCB55qE6K+E5YiG6aG55qCH6aKY5qC35byPDQouZmFjdG9yLXRpdGxlLmRpc2FibGVkIHsNCgljb2xvcjogIzk5OSAhaW1wb3J0YW50Ow0KCWN1cnNvcjogbm90LWFsbG93ZWQgIWltcG9ydGFudDsNCglvcGFjaXR5OiAwLjY7DQoJDQoJJjpob3ZlciB7DQoJCWNvbG9yOiAjOTk5ICFpbXBvcnRhbnQ7DQoJfQ0KfQ0KDQovLyDmgqzmta7moYbmoLflvI8NCi5mYWN0b3ItdG9vbHRpcCB7DQoJcG9zaXRpb246IGFic29sdXRlOw0KCXJpZ2h0OiAxMDAlOyAvKiDmmL7npLrlnKjniLblhYPntKDlt6bkvqcgKi8NCgl0b3A6IDA7DQoJbWFyZ2luLXJpZ2h0OiAxMHB4OyAvKiDkuI7or4TliIbpobnnmoTpl7Tot50gKi8NCgliYWNrZ3JvdW5kOiAjZmZmOw0KCWJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7DQoJYm9yZGVyLXJhZGl1czogOHB4Ow0KCWJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjE1KTsNCgl3aWR0aDogNDAwcHg7DQoJbWF4LWhlaWdodDogMzAwcHg7DQoJb3ZlcmZsb3c6IGhpZGRlbjsNCgl6LWluZGV4OiA5OTk5Ow0KCQ0KCS50b29sdGlwLWhlYWRlciB7DQoJCWRpc3BsYXk6IGZsZXg7DQoJCWp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCgkJYWxpZ24taXRlbXM6IGNlbnRlcjsNCgkJcGFkZGluZzogMTJweCAxNnB4Ow0KCQliYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KCQlib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U0ZTdlZDsNCgkJDQoJCS50b29sdGlwLXRpdGxlIHsNCgkJCWZvbnQtd2VpZ2h0OiA2MDA7DQoJCQlmb250LXNpemU6IDE0cHg7DQoJCQljb2xvcjogIzE3NkFEQjsNCgkJfQ0KCQkNCgkJLnRvb2x0aXAtY2xvc2Ugew0KCQkJY3Vyc29yOiBwb2ludGVyOw0KCQkJY29sb3I6ICM5MDkzOTk7DQoJCQlmb250LXNpemU6IDE0cHg7DQoJCQkNCgkJCSY6aG92ZXIgew0KCQkJCWNvbG9yOiAjMTc2QURCOw0KCQkJfQ0KCQl9DQoJfQ0KCQ0KCS50b29sdGlwLWNvbnRlbnQgew0KCQlwYWRkaW5nOiAxNnB4Ow0KCQlmb250LXNpemU6IDE0cHg7DQoJCWxpbmUtaGVpZ2h0OiAxLjY7DQoJCWNvbG9yOiAjMzMzOw0KCQltYXgtaGVpZ2h0OiAyNDBweDsNCgkJb3ZlcmZsb3cteTogYXV0bzsNCgkJDQoJCS8vIOe+juWMlua7muWKqOadoQ0KCQkmOjotd2Via2l0LXNjcm9sbGJhciB7DQoJCQl3aWR0aDogNnB4Ow0KCQl9DQoJCQ0KCQkmOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7DQoJCQliYWNrZ3JvdW5kOiAjZjFmMWYxOw0KCQkJYm9yZGVyLXJhZGl1czogM3B4Ow0KCQl9DQoJCQ0KCQkmOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7DQoJCQliYWNrZ3JvdW5kOiAjYzFjMWMxOw0KCQkJYm9yZGVyLXJhZGl1czogM3B4Ow0KCQkJDQoJCQkmOmhvdmVyIHsNCgkJCQliYWNrZ3JvdW5kOiAjYThhOGE4Ow0KCQkJfQ0KCQl9DQoJfQ0KfQ0KDQovLyDor4TliIbpobnlrrnlmajnm7jlr7nlrprkvY0NCi5mYWN0b3ItaXRlbSB7DQoJcG9zaXRpb246IHJlbGF0aXZlOw0KfQ0KDQo="}, {"version": 3, "sources": ["one.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA00BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "one.vue", "sourceRoot": "src/views/expertReview/compliance", "sourcesContent": ["<template>\r\n  <!-- 页面主容器，flex布局，分为左中右三部分 -->\r\n  <div class=\"compliance-main\">\r\n    <!-- 左侧内容区，包含标题、操作按钮、PDF预览区 -->\r\n    <div class=\"compliance-left\">\r\n      <!-- 顶部标题和操作按钮区 -->\r\n      <div class=\"compliance-header\">\r\n        <!-- 标题及操作步骤图片 -->\r\n        <div class=\"compliance-title-group\">\r\n          <div class=\"compliance-title\">符合性评审</div> <!-- 页面主标题 -->\r\n          <div class=\"compliance-step-img-group\">\r\n            <div class=\"compliance-step-text\">该页面操作说明</div> <!-- 操作步骤说明文字 -->\r\n            <el-image class=\"compliance-step-img\" :src=\"helpImgList[0]\" :preview-src-list=\"helpImgList\">\r\n            </el-image> <!-- 操作步骤图片，可点击放大 -->\r\n          </div>\r\n        </div>\r\n\t      \r\n\t      <!-- 文件列表 -->\r\n\t      <div class=\"fileList\" style=\"width: 200px; border-right: 1px solid #e6e6e6; border-left: 1px solid #e6e6e6; padding: 10px; overflow-y: auto;\">\r\n\t\t      <div style=\"font-weight: bold; margin-bottom: 10px; color: #333;\">响应文件附件下载</div>\r\n\t\t      <el-card\r\n\t\t\t      v-for=\"(item, index) in attachmentsList\"\r\n\t\t\t      :key=\"index\"\r\n\t\t\t      class=\"fileItem\"\r\n\t\t\t      shadow=\"hover\"\r\n\t\t\t      @click.native=\"downloadFile(item)\"\r\n\t\t\t      style=\"margin-bottom: 8px; cursor: pointer;\"\r\n\t\t      >\r\n\t\t\t      <div style=\"display: flex; align-items: center; padding: 5px;\">\r\n\t\t\t\t      <i class=\"el-icon-document\" style=\"margin-right: 8px; color: #409EFF;\"></i>\r\n\t\t\t\t      <span style=\"font-size: 12px; flex: 1; word-break: break-all;\">{{ item.fileName }}</span>\r\n\t\t\t\t      <i class=\"el-icon-download\" style=\"margin-left: 8px; color: #999;\"></i>\r\n\t\t\t      </div>\r\n\t\t      </el-card>\r\n\t      </div>\r\n\t      \r\n        <!-- 右侧操作按钮区 -->\r\n        <div class=\"compliance-header-btns\">\r\n          <el-button class=\"item-button\" @click=\"bidInquiry\">询标</el-button> <!-- 跳转到询标页面 -->\r\n          <!-- <el-button class=\"item-button\" @click=\"secondOffer\">发起二次报价</el-button> -->\r\n          <div class=\"compliance-header-btns-bottom\">\r\n\t          <el-button\r\n\t\t          :class=\"['item-button', activeButton === 'procurement' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n\t\t          @click=\"viewPurchasing\">采购文件</el-button> <!-- 显示采购文件PDF -->\r\n\t          \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'response' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n              @click=\"showResponseFile\">响应文件</el-button> <!-- 显示响应文件PDF -->\r\n           \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'contrast' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n              @click=\"showFileContrast\">对比</el-button> <!-- 响应文件与采购文件对比 -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- PDF文件预览区，支持单文件和双文件对比 -->\r\n      <div style=\"height:82%\">\r\n        <!-- PDF预览区域 - 保持原始尺寸 -->\r\n        <div class=\"compliance-pdf-group\">\r\n\t        <!-- 采购文件PDF预览 -->\r\n\t        <div v-show=\"isShowProcurement\" :class=\"['compliance-pdf', isDoubleView ? 'compliance-pdf-border-left' : '']\">\r\n\t\t        <!--            <pdfView ref=\"procurement\" :pdfurl=\"procurementPdfUrl\" :uni_key=\"'procurement'\"></pdfView>-->\r\n\t\t        \r\n\t\t        <PdfViewImproved ref=\"procurement\"  :pdfurl=\"procurementPdfUrl\"  :page-height=\"800\"\r\n\t\t                         :buffer-size=\"2\" @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"/>\r\n\t        \r\n\t        </div>\r\n\t        \r\n          <!-- 响应文件PDF预览 -->\r\n          <div v-show=\"isShowResponse\" :class=\"['compliance-pdf', isDoubleView ? 'compliance-pdf-border-right' : '']\">\r\n<!--            <pdfView ref=\"response\" :pdfurl=\"responsePdfUrl\" :uni_key=\"'response'\"></pdfView>-->\r\n\t          \r\n\t          <PdfViewImproved ref=\"response\"  :pdfurl=\"responsePdfUrl\"  :page-height=\"800\" :buffer-size=\"2\"\r\n\t                           @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"/>\r\n\t          \r\n          </div>\r\n          \r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 中间分割线 -->\r\n    <div class=\"compliance-divider\"></div>\r\n    <!-- 右侧评分区 -->\r\n    <div class=\"compliance-right\">\r\n      <!-- 供应商选择下拉框 -->\r\n      <div class=\"compliance-select-group\">\r\n        <el-select class=\"compliance-select\" v-model=\"selectedSupplierName\" placeholder=\"请选择供应商\" @change=\"handleSupplierChange\">\r\n          <el-option v-for=\"item in supplierOptions\" :key=\"item.bidderName\" :label=\"item.bidderName\" :value=\"item.bidderName\">\r\n          </el-option>\r\n        </el-select>\r\n      </div>\r\n      <!-- 评分因子列表及操作区 -->\r\n      <div class=\"compliance-factors-group\" v-if=\"isShowResponse\">\r\n\t      <!-- PDF渲染状态提示 -->\r\n\t      <div v-if=\"!responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t      <i class=\"el-icon-loading\"></i>\r\n\t\t      <span>响应文件正在渲染中，请稍候...</span>\r\n\t      </div>\r\n\t      <div v-else class=\"render-status-tip success\">\r\n\t\t      <i class=\"el-icon-success\"></i>\r\n\t\t      <span>响应文件渲染完成，可以点击跳转</span>\r\n\t      </div>\r\n\t      \r\n        <!-- 判空后再渲染评分因子项列表，防止scoringMethod为null时报错 -->\r\n        <template v-if=\"scoringMethod && scoringMethod.uitems\">\r\n          <div v-for=\"(item, index) in scoringMethod.uitems\" :key=\"index\"\r\n               class=\"factor-item\" style=\"margin-bottom:10px\"\r\n               @mouseenter=\"showFactorTooltip(item)\"\r\n               @mouseleave=\"hideFactorTooltip\" >\r\n\t          <!-- 悬浮框 -->\r\n\t          <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t               class=\"factor-tooltip\"\r\n\t               @mouseenter=\"clearTooltipTimer\"\r\n\t               @mouseleave=\"hideFactorTooltip\">\r\n\t\t          <div class=\"tooltip-header\">\r\n\t\t\t          <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t          <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t          </div>\r\n\t\t          <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t          </div>\r\n\t          \r\n            <div>\r\n              <div class=\"factors\">\r\n                <!-- 评分因子名称，点击可跳转PDF对应页码 -->\r\n                <div class=\"compliance-factor-title-group factor-title\" :class=\"{ 'disabled': !canJumpToPage() }\">\r\n                  <div class=\"compliance-factor-title\" @click=\"handleShowFactorInfo(item)\">\r\n                    {{ item.itemName }}\r\n\t                  <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n                  </div>\r\n                </div>\r\n                <!-- 评分单选按钮（通过/不通过） -->\r\n                <div class=\"compliance-factor-radio-group\">\r\n                  <div>\r\n                    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n                    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- 不通过时填写原因 -->\r\n              <el-input v-if=\"ratingStateMap[item.entMethodItemId].state == 0\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n              </el-input>\r\n              <!-- 系统初验结果展示，绿色为通过，红色为未通过 -->\r\n              <span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n                <i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n                <i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n                {{checkResultNameMap[item.itemName]}}</span>\r\n              <div class=\"compliance-factor-divider\"></div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <!-- 提交按钮区 -->\r\n        <div class=\"compliance-submit-group\">\r\n          <!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"show\">保存</el-button></div> -->\r\n          <div><el-button class=\"item-button-little compliance-submit-btn\" @click=\"submit\">提交</el-button></div>\r\n        </div>\r\n        <!-- 当前选中评分因子的详细说明 -->\r\n        <div class=\"compliance-review-content\">\r\n          <div class=\"compliance-review-title\">评审内容：</div>\r\n          <div class=\"compliance-review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n        </div>\r\n      </div>\r\n\r\n\t    <div class=\"compliance-factors-group\" v-else>\r\n\t\t    <!-- PDF渲染状态提示 -->\r\n\t\t    <div v-if=\"!procurementPdfRendered\" class=\"render-status-tip\">\r\n\t\t\t    <i class=\"el-icon-loading\"></i>\r\n\t\t\t    <span>采购文件正在渲染中，请稍候...</span>\r\n\t\t    </div>\r\n\t\t    <div v-else class=\"render-status-tip success\">\r\n\t\t\t    <i class=\"el-icon-success\"></i>\r\n\t\t\t    <span>采购文件渲染完成，可以点击跳转</span>\r\n\t\t    </div>\r\n\t\t    \r\n\t\t    <!-- 判空后再渲染评分因子项列表，防止scoringMethod为null时报错 -->\r\n\t\t    <template v-if=\"pageProcurement\">\r\n\t\t\t    <div v-for=\"(item, index) in pageProcurement\" :key=\"index\" class=\"factor-item\" style=\"margin-bottom:10px\"\r\n\t\t\t         @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t         @mouseleave=\"hideFactorTooltip\" >\r\n\t\t\t\t    <!-- 悬浮框 -->\r\n\t\t\t\t    <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t\t         class=\"factor-tooltip\"\r\n\t\t\t\t         @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t\t         @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t    <div class=\"tooltip-header\">\r\n\t\t\t\t\t\t    <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t\t    <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t\t    </div>\r\n\t\t\t\t\t    <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t\t    </div>\r\n\t\t\t\t    <div>\r\n\t\t\t\t\t    <div class=\"factors\">\r\n\t\t\t\t\t\t    <!-- 评分因子名称，点击可跳转PDF对应页码 -->\r\n\t\t\t\t\t\t    <div class=\"compliance-factor-title-group factor-title\" :class=\"{ 'disabled': !canJumpToPage() }\">\r\n\t\t\t\t\t\t\t    <div class=\"compliance-factor-title\" @click=\"handleShowFactorInfo(item)\">\r\n\t\t\t\t\t\t\t\t    {{ item.itemName }}\r\n\t\t\t\t\t\t\t\t    <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    <!-- 评分单选按钮（通过/不通过） -->\r\n\t\t\t\t\t\t    <div class=\"compliance-factor-radio-group\">\r\n\t\t\t\t\t\t\t    <div>\r\n\t\t\t\t\t\t\t\t    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n\t\t\t\t\t\t\t\t    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n\t\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t    </div>\r\n\t\t\t\t\t    <!-- 不通过时填写原因 -->\r\n\t\t\t\t\t    <el-input v-if=\"ratingStateMap[item.entMethodItemId].state == 0\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n\t\t\t\t\t    </el-input>\r\n\t\t\t\t\t    <!-- 系统初验结果展示，绿色为通过，红色为未通过 -->\r\n\t\t\t\t\t    <span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n                <i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n                <i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n                {{checkResultNameMap[item.itemName]}}</span>\r\n\t\t\t\t\t    <div class=\"compliance-factor-divider\"></div>\r\n\t\t\t\t    </div>\r\n\t\t\t    </div>\r\n\t\t    </template>\r\n\t\t    <!-- 提交按钮区 -->\r\n\t\t    <div class=\"compliance-submit-group\">\r\n\t\t\t    <!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"show\">保存</el-button></div> -->\r\n\t\t\t    <div><el-button class=\"item-button-little compliance-submit-btn\" @click=\"submit\">提交</el-button></div>\r\n\t\t    </div>\r\n\t\t    <!-- 当前选中评分因子的详细说明 -->\r\n\t\t    <div class=\"compliance-review-content\">\r\n\t\t\t    <div class=\"compliance-review-title\">评审内容：</div>\r\n\t\t\t    <div class=\"compliance-review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n\t\t    </div>\r\n\t    </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 常量定义\r\nconst PASS = '1'; // 通过\r\nconst FAIL = '0'; // 不通过\r\nconst CHECK_PASS = '系统初验通过'; // 系统初验通过文本\r\nconst CHECK_FAIL = '系统初验未通过'; // 系统初验未通过文本\r\n\r\nimport {\r\n  supplierInfo, // 获取供应商信息API\r\n  approvalProcess, // 获取评分方法API\r\n  scoringFactors, // 提交评分因子API\r\n  checkReviewSummary, // 检查评审汇总API\r\n  filesById, // 获取项目相关文件API\r\n} from \"@/api/expert/review\"; // 导入专家评审相关API\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\"; // 获取评分详情API\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\"; // 编辑专家评分状态API\r\nimport { resDocReviewFactorsDecision } from \"@/api/docResponse/entInfo\"; // 获取响应文件评审因子决策API\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      supplierOptions: [], // 供应商下拉选项列表\r\n      scoringMethod: null, // 当前评分方法对象\r\n      selectedFactorNode: {}, // 当前选中的评分因子节点\r\n      selectedSupplierName: '', // 当前选中的供应商名称\r\n      selectedSupplier: {}, // 当前选中的供应商对象\r\n      expertInfo: {}, // 当前专家信息\r\n      ratingStateMap: {}, // 评分项状态映射（key为评分项ID，value为{state, reason}）\r\n      projectFiles: {}, // 项目相关文件对象\r\n      isShowResponse: false, // 是否显示响应文件\r\n      isShowProcurement: false, // 是否显示采购文件\r\n      isDoubleView: false, // 是否双文件对比模式\r\n      factorDetailList: [], // 评分因子详细列表\r\n      entDocResponsePage: null, // 企业响应文件页码信息\r\n      factorsPageMap: null, // 供应商因子页码映射\r\n      supplierFactorPage: null, // 当前供应商因子页码\r\n      responsePdfUrl: null, // 响应文件PDF地址\r\n      procurementPdfUrl: null, // 采购文件PDF地址\r\n\r\n      // 按钮状态管理\r\n      activeButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n\t    entDocProcurementPage: {}, // 采购文件页码信息\r\n      pageProcurement:[], // 采购文件的评分项\r\n      attachmentsList:[], // 文件列表\r\n\t    \r\n\t    // PDF渲染状态管理\r\n\t    responsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n\t    procurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n\t    helpImgList: [\"/evalution/help.jpg\"], // 操作帮助图片列表\r\n      // 评分项名称与后端字段映射\r\n      factorCodeMap: {\r\n        \"特定资格要求\": \"zgzs\",\r\n        \"响应内容\": \"jsplb\",\r\n        \"采购需求\": \"jsplb\",\r\n        \"供货期限\": \"ghqx\",\r\n        \"投标报价\": \"tbbj\"\r\n      },\r\n      checkResult: {}, // 系统初验结果对象\r\n      // 系统初验结果名称映射\r\n      checkResultNameMap: {\r\n        \"符合《中华人民共和国政府采购法》第二十二条规定\": CHECK_PASS,\r\n        \"特定资格要求\": CHECK_PASS,\r\n        \"信用查询\": CHECK_PASS,\r\n        \"响应人名称\": CHECK_PASS,\r\n        \"响应内容\": CHECK_PASS,\r\n        \"采购需求\": CHECK_PASS,\r\n        \"供货期限\": CHECK_PASS,\r\n        \"投标报价\": CHECK_PASS\r\n      },\r\n      // 本地缓存数据\r\n      localExpertInfo: null, // 本地专家信息\r\n      localEntDocResponsePage: null, // 本地响应文件页码\r\n      localFactorsPageMap: null, // 本地因子页码映射\r\n\t    \r\n\t    // 悬停状态管理\r\n\t    hoveredFactorNode: null, // 悬停时的评分项\r\n\t    tooltipTimer: null, // 悬浮框显示定时器\r\n    };\r\n  },\r\n  methods: {\r\n    /**\r\n     * 校验所有评分项是否填写完整\r\n     * @returns {boolean} 是否全部填写\r\n     */\r\n    validateRatings() {\r\n      for (const item of this.scoringMethod.uitems) { // 遍历所有评分项\r\n        const state = this.ratingStateMap[item.entMethodItemId].state; // 获取评分状态\r\n        const reason = this.ratingStateMap[item.entMethodItemId].reason; // 获取评分原因\r\n        // 评分结果未填写\r\n        if (state === null || state === '') {\r\n          // this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`); // 提示未填写\r\n          return true;\r\n        }\r\n        // 不通过但未填写原因\r\n        if (state === FAIL && (!reason || reason.trim() === '')) {\r\n          this.$message.warning(`${item.itemName}评审不通过但未填写备注，不进行保存`); // 提示未填写原因\r\n          return false;\r\n        }\r\n      }\r\n      return true; // 全部填写返回true\r\n    },\r\n    /**\r\n     * 获取系统初验结果（通过/未通过）\r\n     * @param {string} factorName 评分项名称\r\n     * @returns {string} 1-通过 0-未通过\r\n     */\r\n    getCheckResultState(factorName) {\r\n      if (!this.checkResult || Object.keys(this.checkResult).length === 0) return ''; // 没有初验结果直接返回空\r\n      let code = this.factorCodeMap[factorName]; // 获取评分项对应的后端字段\r\n      let check = PASS; // 默认通过\r\n      if (code) {\r\n        check = this.checkResult[code]; // 获取初验结果\r\n        // 投标报价特殊处理\r\n        if (factorName === \"投标报价\" && check === PASS) {\r\n          check = this.checkResult['mxbjb']; // 明细报价表\r\n        }\r\n      }\r\n      // 设置初验结果名称\r\n      if (check === FAIL) {\r\n        this.checkResultNameMap[factorName] = CHECK_FAIL; // 未通过\r\n      } else {\r\n        check = PASS;\r\n        this.checkResultNameMap[factorName] = CHECK_PASS; // 通过\r\n      }\r\n      return check; // 返回初验结果\r\n    },\r\n    /**\r\n     * 重置所有评分项的状态\r\n     */\r\n    resetRatingStateMap() {\r\n      if (!this.scoringMethod) return; // 没有评分方法直接返回\r\n      for (const key of Object.keys(this.ratingStateMap)) { // 遍历所有评分项\r\n        this.ratingStateMap[key].state = null; // 重置状态\r\n        this.ratingStateMap[key].reason = ''; // 重置原因\r\n      }\r\n    },\r\n    /**\r\n     * 临时保存评分结果到后端\r\n     * 校验通过后才会保存\r\n     * @returns {boolean} 保存是否成功\r\n     */\r\n    async saveTempRating() {\r\n      if (!this.validateRatings()) return false; // 校验不通过不保存，返回false\r\n      // 构造提交数据\r\n      const data = this.scoringMethod.uitems.map(item => {\r\n        const itemId = item.entMethodItemId; // 获取评分项ID\r\n        return {\r\n          scoringMethodUitemId: itemId, // 评分项ID\r\n          expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n          entId: this.selectedSupplier.bidderId, // 供应商ID\r\n          evaluationResult: this.ratingStateMap[itemId].state, // 评分结果\r\n          evaluationRemark: this.ratingStateMap[itemId].reason || '' // 评分原因\r\n        };\r\n      }).filter(d => d.evaluationResult !== null && d.evaluationResult !== ''); // 过滤未填写的项\r\n      if (data.length > 0) {\r\n        try {\r\n          const response = await scoringFactors(data); // 提交评分因子\r\n          if (response.code === 200) {\r\n            this.$message.success(\"保存成功\"); // 保存成功提示\r\n            return true; // 保存成功返回true\r\n          } else {\r\n            this.$message.warning(response.msg); // 保存失败提示\r\n            return false; // 保存失败返回false\r\n          }\r\n        } catch (e) {\r\n          this.$message.error(\"保存失败\"); // 异常提示\r\n          return false; // 异常返回false\r\n        }\r\n      }\r\n      return true; // 没有数据需要保存时也返回true\r\n    },\r\n    /**\r\n     * 供应商切换事件，切换时自动保存上一个供应商评分，并并发获取新供应商的评分详情和系统初验\r\n     * @param {string} supplierName 供应商名称\r\n     */\r\n    async handleSupplierChange(supplierName) {\r\n      // 切换前保存上一个供应商评分\r\n      if (Object.keys(this.selectedSupplier).length !== 0) {\r\n        await this.saveTempRating(); // 保存评分\r\n      }\r\n      // 查找当前选中的供应商对象\r\n      this.selectedSupplier = this.supplierOptions.find(item => item.bidderName === supplierName); // 查找供应商\r\n      // 获取当前供应商因子页码\r\n      this.supplierFactorPage = this.factorsPageMap[this.selectedSupplier.bidderId]; // 获取页码\r\n      // 并发获取评分详情和系统初验\r\n      // 使用 Promise.allSettled 让两个请求独立执行，互不影响\r\n      try {\r\n        const [detailResult, checkResult] = await Promise.allSettled([\r\n          getDetailByPsxx({\r\n            expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n            projectId: this.$route.query.projectId, // 项目ID\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项ID\r\n          }),\r\n          resDocReviewFactorsDecision({\r\n            projectId: this.$route.query.projectId, // 项目ID\r\n            entId: this.selectedSupplier.bidderId, // 供应商ID\r\n          })\r\n        ]);\r\n\r\n        // 处理评分详情请求结果\r\n        if (detailResult.status === 'fulfilled') {\r\n          const detailRes = detailResult.value;\r\n          if (detailRes.code === 200) {\r\n            this.factorDetailList = detailRes.data; // 评分详情列表\r\n            const factor = this.factorDetailList.find(item => item.bidderName === supplierName)?.evalExpertEvaluationDetails; // 当前供应商评分详情\r\n            this.resetRatingStateMap(); // 重置评分状态\r\n            if (factor) {\r\n              for (const item of factor) {\r\n                this.ratingStateMap[item.scoringMethodUitemId].reason = item.evaluationRemark; // 设置评分原因\r\n                this.ratingStateMap[item.scoringMethodUitemId].state = item.evaluationResult; // 设置评分结果\r\n              }\r\n            }\r\n          } else {\r\n            this.$message.warning(detailRes.msg); // 评分详情获取失败\r\n          }\r\n        } else {\r\n          console.error(\"获取评分详情失败:\", detailResult.reason);\r\n          this.$message.error(\"获取评分详情失败\"); // 评分详情请求异常\r\n        }\r\n\r\n        // 处理系统初验请求结果\r\n        if (checkResult.status === 'fulfilled') {\r\n          const checkRes = checkResult.value;\r\n          if (checkRes.code === 200) {\r\n            this.checkResult = checkRes.data; // 设置初验结果\r\n          } else {\r\n            console.error(\"获取系统初验结果失败:\", checkRes.msg);\r\n            this.$message.warning(\"获取系统初验结果失败\"); // 系统初验获取失败\r\n          }\r\n        } else {\r\n          console.error(\"系统初验请求失败:\", checkResult.reason);\r\n          this.$message.error(\"系统初验请求失败\"); // 系统初验请求异常\r\n        }\r\n      } catch (e) {\r\n        console.error(\"请求处理异常:\", e);\r\n        this.$message.error(\"获取供应商详情失败\"); // 异常提示\r\n      }\r\n      // 默认显示响应文件\r\n      this.showResponseFile();\r\n    },\r\n    /**\r\n     * 显示响应文件PDF\r\n     */\r\n    showResponseFile() {\r\n      if (!this.selectedSupplier || Object.keys(this.selectedSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n      this.activeButton = 'response'; // 设置当前激活按钮\r\n      this.isDoubleView = false; // 单文件模式\r\n      this.isShowProcurement = false; // 不显示采购文件\r\n      this.isShowResponse = true; // 显示响应文件\r\n      this.responsePdfUrl = this.projectFiles.file[this.selectedSupplier.bidderId]; // 设置响应文件PDF地址\r\n    },\r\n    /**\r\n     * 文件对比（双文件模式）\r\n     */\r\n    showFileContrast() {\r\n      if (!this.selectedSupplier || Object.keys(this.selectedSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n      this.activeButton = 'contrast'; // 设置当前激活按钮\r\n      this.isDoubleView = true; // 双文件模式\r\n      this.isShowProcurement = true; // 显示采购文件\r\n      this.isShowResponse = true; // 显示响应文件\r\n      this.responsePdfUrl = this.projectFiles.file[this.selectedSupplier.bidderId]; // 设置响应文件PDF地址\r\n    },\r\n    /**\r\n     * 点击评分项名称，跳转到对应PDF页码\r\n     * @param {Object} factorItem 当前评分因子项\r\n     */\r\n    handleShowFactorInfo(factorItem) {\r\n\t    // 检查PDF是否渲染完成\r\n\t    if (!this.canJumpToPage()) {\r\n\t\t    this.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t    return;\r\n\t    }\r\n\t\t\t\r\n      this.selectedFactorNode = factorItem; // 设置当前选中因子\r\n\r\n      // 如果只显示采购文件，使用采购文件页码信息\r\n      if (this.isShowProcurement && !this.isShowResponse) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        if (factorItem.jumpToPage) {\r\n          this.$refs.procurement.skipPage(factorItem.jumpToPage); // 采购文件跳页\r\n        } else if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 如果显示响应文件或对比模式，需要选择供应商\r\n      if (!this.supplierFactorPage || Object.keys(this.supplierFactorPage).length === 0) {\r\n        this.$message.warning(\"请先选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n\r\n      // 跳转到响应文件对应页码\r\n      if (this.isShowResponse && this.$refs.response) {\r\n\t      if (!this.responsePdfRendered) {\r\n\t\t      this.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n        this.$refs.response.skipPage(this.supplierFactorPage[this.selectedFactorNode.itemName]); // 响应文件跳页\r\n      }\r\n\r\n      // 跳转到采购文件对应页码\r\n      if (this.isShowProcurement && this.$refs.procurement) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        // 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n        if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n        } else {\r\n          // 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n          // 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n        }\r\n      }\r\n    },\r\n\t  \r\n\t  /**\r\n\t   * 检查是否可以跳转页面\r\n\t   * @returns {boolean} 是否可以跳转\r\n\t   */\r\n\t  canJumpToPage() {\r\n\t\t  // 如果只显示采购文件\r\n\t\t  if (this.isShowProcurement && !this.isShowResponse) {\r\n\t\t\t  return this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  // 如果只显示响应文件\r\n\t\t  if (this.isShowResponse && !this.isShowProcurement) {\r\n\t\t\t  return this.responsePdfRendered;\r\n\t\t  }\r\n\t\t  // 如果对比模式（两个都显示）\r\n\t\t  if (this.isShowResponse && this.isShowProcurement) {\r\n\t\t\t  return this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  return false;\r\n\t  },\r\n\t  /**\r\n\t   * 处理PDF渲染状态变化\r\n\t   * @param {boolean} isRendered 是否渲染完成\r\n\t   * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t   */\r\n\t  handlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t\t  if (pdfType === 'response') {\r\n\t\t\t  this.responsePdfRendered = isRendered;\r\n\t\t  } else if (pdfType === 'procurement') {\r\n\t\t\t  this.procurementPdfRendered = isRendered;\r\n\t\t  }\r\n\t\t  \r\n\t\t  if (isRendered) {\r\n\t\t\t  console.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t\t  }\r\n\t  },\r\n\t  \r\n    /**\r\n     * 提交评分并修改专家进度\r\n     */\r\n    async submit() {\r\n      // 先保存评分，如果保存失败则不继续提交\r\n      const saveResult = await this.saveTempRating();\r\n      if (!saveResult) {\r\n        // 保存失败，不继续提交流程\r\n        return;\r\n      }\r\n\r\n      const data = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项ID\r\n      };\r\n      try {\r\n        const response = await checkReviewSummary(data); // 检查评审汇总\r\n\r\n        if (response.code === 200) {\r\n          // 修改专家进度\r\n          const status = {\r\n            evalExpertScoreInfoId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\")).evalExpertScoreInfoId, // 专家评分信息ID\r\n            evalState: 1, // 进度状态\r\n          };\r\n          const res = await editEvalExpertScoreInfo(status); // 编辑专家评分状态\r\n          if (res.code === 200) {\r\n            this.$message.success(\"提交成功\"); // 提交成功提示\r\n          }\r\n          this.$emit(\"send\", \"two\"); // 发送事件\r\n        } else {\r\n          this.$message.warning(response.msg); // 提交失败提示\r\n        }\r\n      } catch (e) {\r\n        this.$message.error(\"提交失败\"); // 异常提示\r\n      }\r\n    },\r\n    /**\r\n     * 显示采购文件PDF\r\n     */\r\n    viewPurchasing() {\r\n      this.activeButton = 'procurement'; // 设置当前激活按钮\r\n      this.isDoubleView = false; // 单文件模式\r\n      this.isShowResponse = false; // 不显示响应文件\r\n      this.isShowProcurement = true; // 显示采购文件\r\n      // 右侧评分项显示为采购文件的评分项\r\n      let pageProcurementArr = []; // 采购文件评分项数组\r\n\r\n      for (let item in this.entDocProcurementPage){\r\n        pageProcurementArr.push({\r\n          itemName: item,\r\n          jumpToPage: this.entDocProcurementPage[item]\r\n        })\r\n      }\r\n\r\n      console.log(this.scoringMethod.uitems);\r\n      console.log(pageProcurementArr)\r\n      this.pageProcurement = [];\r\n      for (let i = 0; i < this.scoringMethod.uitems.length;i++){\r\n        for (let j = 0; j < pageProcurementArr.length;j++){\r\n          if (this.scoringMethod.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n            this.pageProcurement.push({...this.scoringMethod.uitems[i],...pageProcurementArr[j]});\r\n          }\r\n        }\r\n      }\r\n      console.log(this.pageProcurement)\r\n    },\r\n    /**\r\n     * 跳转到二次报价页面\r\n     */\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        zjhm: this.$route.query.zjhm, // 专家证件号码\r\n        scoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")), // 二次报价评分方法项ID\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query }); // 跳转页面\r\n    },\r\n    /**\r\n     * 跳转到询标页面\r\n     */\r\n    bidInquiry() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        zjhm: this.$route.query.zjhm, // 专家证件号码\r\n        scoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")), // 询标评分方法项ID\r\n      };\r\n      this.$router.push({ path: \"/bidInquiry\", query }); // 跳转页面\r\n    },\r\n    /**\r\n     * 获取因素对应页码（从本地缓存）\r\n     */\r\n    getFactorsPage() {\r\n      this.factorsPageMap = JSON.parse(localStorage.getItem(\"entDocResponsePage\")); // 获取因子页码映射\r\n    },\r\n    /**\r\n     * 初始化专家和本地数据，只在mounted时调用一次\r\n     */\r\n    initLocalData() {\r\n      try {\r\n        this.localExpertInfo = JSON.parse(localStorage.getItem(\"expertInfo\") || \"{}\"); // 获取本地专家信息\r\n        this.localEntDocResponsePage = JSON.parse(localStorage.getItem(\"entDocResponsePage\") || \"{}\"); // 获取本地响应文件页码\r\n        this.localFactorsPageMap = JSON.parse(localStorage.getItem(\"entDocResponsePage\") || \"{}\"); // 获取本地因子页码映射\r\n        this.expertInfo = this.localExpertInfo; // 设置专家信息\r\n        this.entDocResponsePage = this.localEntDocResponsePage; // 设置响应文件页码\r\n        this.factorsPageMap = this.localFactorsPageMap; // 设置因子页码映射\r\n        console.log(\"本地数据已初始化\", { expertInfo: this.expertInfo });\r\n      } catch (error) {\r\n        console.error(\"初始化本地数据失败:\", error);\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 初始化专家信息（用于响应专家信息更新）\r\n     */\r\n    initExpertInfo() {\r\n      try {\r\n        const expertInfoStr = localStorage.getItem(\"expertInfo\");\r\n        if (expertInfoStr) {\r\n          this.localExpertInfo = JSON.parse(expertInfoStr);\r\n          this.expertInfo = this.localExpertInfo;\r\n          console.log(\"专家信息已刷新\", this.expertInfo);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"刷新专家信息失败:\", error);\r\n      }\r\n    },\r\n    /**\r\n     * 页面初始化，加载供应商、评分方法、文件等（并发请求）\r\n     */\r\n    async initPage() {\r\n      this.initLocalData(); // 初始化本地数据\r\n      try {\r\n        // 并发获取供应商、评分方法、文件\r\n        const [supplierRes, approvalRes, filesRes] = await Promise.all([\r\n          supplierInfo({ projectId: this.$route.query.projectId }), // 获取供应商\r\n          approvalProcess(this.$route.query.projectId, this.expertInfo.resultId), // 获取评分方法\r\n          filesById(this.$route.query.projectId) // 获取项目文件\r\n        ]);\r\n        // 处理供应商\r\n        if (supplierRes.code === 200) {\r\n          this.supplierOptions = supplierRes.rows.filter(item => item.isAbandonedBid == 0); // 过滤未弃标供应商\r\n        } else {\r\n          this.$message.warning(supplierRes.msg); // 获取失败提示\r\n        }\r\n        // 处理评分方法\r\n        if (approvalRes.code === 200) {\r\n\t        this.attachmentsList = approvalRes.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n          this.scoringMethod = approvalRes.data.scoringMethodUinfo.scoringMethodItems.find(\r\n            item => item.scoringMethodItemId == this.$route.query.scoringMethodItemId\r\n          ); // 获取当前评分方法\r\n          localStorage.setItem(\"evalProjectEvaluationProcess\", JSON.stringify(this.scoringMethod.evalProjectEvaluationProcess)); // 缓存评分流程\r\n          this.ratingStateMap = this.scoringMethod.uitems.reduce((acc, item) => {\r\n            acc[item.entMethodItemId] = { state: null, reason: '' }; // 初始化评分状态\r\n            return acc;\r\n          }, {});\r\n        } else {\r\n          this.$message.warning(approvalRes.msg); // 获取失败提示\r\n        }\r\n        // 处理文件\r\n        if (filesRes.code === 200) {\r\n          this.projectFiles = filesRes.data; // 设置项目文件\r\n          if (this.projectFiles.tenderNoticeFilePath) {\r\n            this.procurementPdfUrl = this.projectFiles.tenderNoticeFilePath; // 设置采购文件PDF\r\n          }\r\n          // if (this.projectFiles.file) {\r\n          //   this.responsePdfUrl = this.projectFiles.file[0]; // 设置响应文件PDF\r\n          // }\r\n        } else {\r\n          this.$message.warning(filesRes.msg); // 获取失败提示\r\n        }\r\n      } catch (e) {\r\n        this.$message.error(\"页面初始化失败\"); // 异常提示\r\n      }\r\n    },\r\n\t  \r\n\t  downloadFile(item){\r\n\t\t  this.$download.zip(item.filePath,item.fileName);\r\n\t  },\r\n\t  \r\n\t  \r\n\t  // ========== 悬停相关 ==========\r\n\t  /**\r\n\t   * 显示评分项悬浮框\r\n\t   * @param {Object} factorItem 评分项对象\r\n\t   */\r\n\t  showFactorTooltip(factorItem) {\r\n\t\t  if (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\t\t  \r\n\t\t  // 清除之前的定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t  this.tooltipTimer = setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = factorItem;\r\n\t\t  }, 300); // 300ms延迟\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 隐藏评分项悬浮框\r\n\t   */\r\n\t  hideFactorTooltip() {\r\n\t\t  // 清除定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t  setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = null;\r\n\t\t  }, 100);\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t   */\r\n\t  clearTooltipTimer() {\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t  }\r\n  },\r\n  mounted() {\r\n\t  this.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\")); // 初始化采购文件页码信息\r\n    this.initPage(); // 页面挂载时初始化数据\r\n    this.getFactorsPage(); // 获取因子页码\r\n  },\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.compliance-main {\r\n  min-height: 57vh;\r\n  display: flex;\r\n}\r\n.compliance-left {\r\n  min-height: 57vh;\r\n  width: 79%;\r\n}\r\n.compliance-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.compliance-title-group {\r\n  display: flex;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333;\r\n}\r\n.compliance-title {\r\n  // nothing extra\r\n}\r\n.compliance-step-img-group {\r\n  display: grid;\r\n  justify-items: center;\r\n  position: relative;\r\n  bottom: -30px;\r\n}\r\n.compliance-step-text {\r\n  font-size: 12px;\r\n}\r\n.compliance-step-img {\r\n  width: 80px;\r\n  height: 30px;\r\n  margin-right: 20px;\r\n}\r\n.compliance-header-btns {\r\n  text-align: right;\r\n}\r\n.compliance-header-btns-bottom {\r\n  margin-top: 20px;\r\n}\r\n.compliance-blue-btn {\r\n  background-color: #176ADB !important;\r\n  color: #fff !important;\r\n  border: 1px solid #176ADB !important;\r\n}\r\n.compliance-blue-btn-active {\r\n  background-color: #FF6B35 !important;\r\n  color: #fff !important;\r\n  border: 1px solid #FF6B35 !important;\r\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.compliance-pdf-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  height: 82%;\r\n}\r\n.compliance-pdf {\r\n  width: 49%;\r\n}\r\n.compliance-pdf-border-right {\r\n  border-right: 1px solid #176ADB;\r\n}\r\n.compliance-pdf-border-left {\r\n  border-left: 1px solid #176ADB;\r\n}\r\n.compliance-divider {\r\n  min-height: 57vh;\r\n  width: 1%;\r\n  background-color: #F5F5F5;\r\n}\r\n.compliance-right {\r\n  min-height: 57vh;\r\n  width: 20%;\r\n}\r\n.compliance-select-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.compliance-select {\r\n  width: 100%;\r\n}\r\n.compliance-factors-group {\r\n  padding: 15px 20px;\r\n}\r\n.compliance-factor-title-group {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  text-align: left;\r\n  width: 98%;\r\n}\r\n.compliance-factor-title {\r\n  cursor: pointer;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 16px;\r\n  color: #333;\r\n  letter-spacing: 0;\r\n}\r\n.compliance-factor-radio-group {\r\n  display: flex;\r\n  width: 100%;\r\n  justify-content: flex-end;\r\n}\r\n.compliance-factor-divider {\r\n  height: 1px;\r\n  background-color: #DCDFE6;\r\n  margin-top: 10px;\r\n}\r\n.compliance-submit-group {\r\n  display: flex;\r\n  margin: 32px 0;\r\n  justify-content: space-evenly;\r\n}\r\n.compliance-submit-btn {\r\n  background-color: #176ADB;\r\n}\r\n.compliance-review-content {\r\n  text-align: left;\r\n  font-size: 14px;\r\n}\r\n.compliance-review-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 15px;\r\n  color: #176ADB;\r\n  letter-spacing: 0;\r\n}\r\n.compliance-review-html {\r\n  padding: 6px 30px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n\t  transition: all 0.3s ease;\r\n\t  padding: 4px 8px;\r\n\t  border-radius: 4px;\r\n\t  \r\n\t  &:hover {\r\n\t\t  background-color: #f0f8ff;\r\n\t\t  color: #176ADB;\r\n\t\t  transform: translateX(2px);\r\n\t  }\r\n  }\r\n}\r\n.item-button {\r\n  border: 1px solid #979797;\r\n  width: 150px;\r\n  height: 36px;\r\n  margin: 0 10px;\r\n  font-weight: 700;\r\n  font-size: 17px;\r\n  border-radius: 6px;\r\n  color: #333;\r\n  &:hover {\r\n    color: #333;\r\n  }\r\n}\r\n.item-button-little {\r\n  width: 124px;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #fff;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n\t.fileItem {\r\n\t\ttransition: all 0.3s ease;\r\n\t\t&:hover {\r\n\t\t\ttransform: translateY(-2px);\r\n\t\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\t\t}\r\n\t\t\r\n\t\t::v-deep .el-card__body {\r\n\t\t\tpadding: 0;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\t\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\t\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\t\t\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\t\t\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\t\t\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n\r\n</style>\r\n"]}]}