{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business\\one.vue?vue&type=style&index=0&id=769fa3ba&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business\\one.vue", "mtime": 1753923382623}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYnVzaW5lc3MtcmV2aWV3LWNvbnRhaW5lciB7DQogIG1pbi1oZWlnaHQ6IDU3dmg7DQogIGRpc3BsYXk6IGZsZXg7DQp9DQouYnVzaW5lc3MtcmV2aWV3LW1haW4gew0KICBtaW4taGVpZ2h0OiA1N3ZoOw0KICB3aWR0aDogNzklOw0KfQ0KLmJ1c2luZXNzLXJldmlldy1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMTc2QURCOw0KICBwYWRkaW5nOiAxNXB4IDIwcHg7DQp9DQouYnVzaW5lc3MtcmV2aWV3LXRpdGxlLWdyb3VwIHsNCiAgZGlzcGxheTogZmxleDsNCiAgaGVpZ2h0OiAzNnB4Ow0KICBmb250LXdlaWdodDogNzAwOw0KICBmb250LXNpemU6IDI0cHg7DQogIGNvbG9yOiAjMzMzMzMzOw0KfQ0KLmJ1c2luZXNzLXJldmlldy10aXRsZSB7DQogIC8qIOS/neaMgeWOn+agtyAqLw0KfQ0KLmJ1c2luZXNzLXJldmlldy1zdGVwLWdyb3VwIHsNCiAgZGlzcGxheTogZ3JpZDsNCiAganVzdGlmeS1pdGVtczogY2VudGVyOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIGJvdHRvbTogLTMwcHg7DQp9DQouYnVzaW5lc3MtcmV2aWV3LXN0ZXAtdGV4dCB7DQogIGZvbnQtc2l6ZTogMTJweDsNCn0NCi5idXNpbmVzcy1yZXZpZXctc3RlcC1pbWcgew0KICB3aWR0aDogODBweDsNCiAgaGVpZ2h0OiAzMHB4Ow0KICBtYXJnaW4tcmlnaHQ6IDIwcHg7DQp9DQouYnVzaW5lc3MtcmV2aWV3LWhlYWRlci1idG5zIHsNCiAgdGV4dC1hbGlnbjogcmlnaHQ7DQp9DQouYnVzaW5lc3MtcmV2aWV3LWhlYWRlci1idG5zLWdyb3VwIHsNCiAgbWFyZ2luLXRvcDogMjBweDsNCn0NCi5idXNpbmVzcy1yZXZpZXctaGVhZGVyLWJ0bnMtZ3JvdXAgLml0ZW0tYnV0dG9uIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzE3NkFEQjsNCiAgY29sb3I6ICNGRkZGRkY7DQogIGJvcmRlcjogMXB4IHNvbGlkICMxNzZBREI7DQp9DQouYnVzaW5lc3MtcmV2aWV3LXBkZi1ncm91cCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBoZWlnaHQ6IDgyJTsNCn0NCi5idXNpbmVzcy1yZXZpZXctcGRmIHsNCiAgd2lkdGg6IDQ5JTsNCn0NCi5idXNpbmVzcy1yZXZpZXctcGRmLWJvcmRlci1yaWdodCB7DQogIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICMxNzZBREI7DQp9DQouYnVzaW5lc3MtcmV2aWV3LXBkZi1ib3JkZXItbGVmdCB7DQogIGJvcmRlci1sZWZ0OiAxcHggc29saWQgIzE3NkFEQjsNCn0NCi5idXNpbmVzcy1yZXZpZXctZGl2aWRlciB7DQogIG1pbi1oZWlnaHQ6IDU3dmg7DQogIHdpZHRoOiAxJTsNCiAgYmFja2dyb3VuZC1jb2xvcjogI0Y1RjVGNTsNCn0NCi5idXNpbmVzcy1yZXZpZXctc2lkZSB7DQogIG1pbi1oZWlnaHQ6IDU3dmg7DQogIHdpZHRoOiAyMCU7DQp9DQouYnVzaW5lc3MtcmV2aWV3LXNlbGVjdC1ncm91cCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBib3JkZXItYm90dG9tOiAycHggc29saWQgIzE3NkFEQjsNCiAgcGFkZGluZzogMTVweCAyMHB4Ow0KfQ0KLmJ1c2luZXNzLXJldmlldy1zZWxlY3Qgew0KICB3aWR0aDogMTAwJTsNCn0NCi5idXNpbmVzcy1yZXZpZXctc2lkZS1jb250ZW50IHsNCiAgcGFkZGluZzogMTVweCAyMHB4Ow0KfQ0KLmJ1c2luZXNzLXJldmlldy1mYWN0b3ItaXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQouYnVzaW5lc3MtcmV2aWV3LWZhY3Rvci10aXRsZS1ncm91cCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgdGV4dC1hbGlnbjogbGVmdDsNCiAgd2lkdGg6IDk4JTsNCn0NCi5idXNpbmVzcy1yZXZpZXctZmFjdG9yLXRpdGxlIHsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICBmb250LWZhbWlseTogU291cmNlSGFuU2Fuc1NDLUJvbGQ7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgY29sb3I6ICMzMzMzMzM7DQogIGxldHRlci1zcGFjaW5nOiAwOw0KfQ0KLmJ1c2luZXNzLXJldmlldy1mYWN0b3Itc2NvcmUtZ3JvdXAgew0KICBkaXNwbGF5OiBmbGV4Ow0KICB3aWR0aDogMTAwJTsNCiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsNCiAgcGFkZGluZzogMTBweDsNCn0NCi5idXNpbmVzcy1yZXZpZXctZmFjdG9yLXNjb3JlIHsNCiAgY29sb3I6IGdyZWVuOw0KICBmb250LXNpemU6IDE2cHg7DQp9DQouYnVzaW5lc3MtcmV2aWV3LXN1Ym1pdC1ncm91cCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIG1hcmdpbjogMzJweCAwOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWV2ZW5seTsNCn0NCi5idXNpbmVzcy1yZXZpZXctc3VibWl0LWJ0biB7DQogIGJhY2tncm91bmQtY29sb3I6ICMxNzZBREI7DQp9DQouYnVzaW5lc3MtcmV2aWV3LWNvbnRlbnQgew0KICB0ZXh0LWFsaWduOiBsZWZ0Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQouYnVzaW5lc3MtcmV2aWV3LWNvbnRlbnQtdGl0bGUgew0KICBmb250LWZhbWlseTogU291cmNlSGFuU2Fuc1NDLUJvbGQ7DQogIGZvbnQtd2VpZ2h0OiA3MDA7DQogIGZvbnQtc2l6ZTogMTVweDsNCiAgY29sb3I6ICMxNzZBREI7DQogIGxldHRlci1zcGFjaW5nOiAwOw0KfQ0KLmJ1c2luZXNzLXJldmlldy1jb250ZW50LXJlbWFyayB7DQogIHBhZGRpbmc6IDZweCAzMHB4Ow0KfQ0KLml0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBtYXJnaW4tYm90dG9tOiA4MHB4Ow0KICAuaXRlbS10aXRsZSB7DQogICAgd2lkdGg6IDEyMHB4Ow0KICAgIG1hcmdpbi1yaWdodDogMjBweDsNCiAgICB0ZXh0LWFsaWduOiBsZWZ0Ow0KCSAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCgkgIHBhZGRpbmc6IDRweCA4cHg7DQoJICBib3JkZXItcmFkaXVzOiA0cHg7DQoJICANCgkgICY6aG92ZXIgew0KCQkgIGJhY2tncm91bmQtY29sb3I6ICNmMGY4ZmY7DQoJCSAgY29sb3I6ICMxNzZBREI7DQoJCSAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDJweCk7DQoJICB9DQogIH0NCn0NCi5pdGVtLWJ1dHRvbiB7DQogIGJvcmRlcjogMXB4IHNvbGlkICM5Nzk3OTc7DQogIHdpZHRoOiAxNTBweDsNCiAgaGVpZ2h0OiAzNnB4Ow0KICBtYXJnaW46IDAgMTBweDsNCiAgZm9udC13ZWlnaHQ6IDcwMDsNCiAgZm9udC1zaXplOiAxN3B4Ow0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIGNvbG9yOiAjMzMzMzMzOw0KICAmOmhvdmVyIHsNCiAgICBjb2xvcjogIzMzMzMzMzsNCiAgfQ0KfQ0KLmJ1c2luZXNzLWJsdWUtYnRuIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzE3NkFEQiAhaW1wb3J0YW50Ow0KICBjb2xvcjogI2ZmZiAhaW1wb3J0YW50Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjMTc2QURCICFpbXBvcnRhbnQ7DQp9DQouYnVzaW5lc3MtYmx1ZS1idG4tYWN0aXZlIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI0ZGNkIzNSAhaW1wb3J0YW50Ow0KICBjb2xvcjogI2ZmZiAhaW1wb3J0YW50Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjRkY2QjM1ICFpbXBvcnRhbnQ7DQogIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDI1NSwgMTA3LCA1MywgMC4zKSAhaW1wb3J0YW50Ow0KfQ0KLml0ZW0tYnV0dG9uLWxpdHRsZSB7DQogIHdpZHRoOiAxMjRweDsNCiAgaGVpZ2h0OiAzNnB4Ow0KICBmb250LXdlaWdodDogNzAwOw0KICBmb250LXNpemU6IDE4cHg7DQogIGNvbG9yOiAjZmZmOw0KICAmOmhvdmVyIHsNCiAgICBjb2xvcjogI2ZmZjsNCiAgfQ0KfQ0KLmZhY3RvcnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGZsZXgtd3JhcDogd3JhcDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCi50ZXh0IHsNCiAgOjp2LWRlZXAgLmVsLXRleHRhcmVhX19pbm5lciB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsNCiAgICBib3JkZXItcmFkaXVzOiAwOw0KICAgIGJvcmRlcjogMXB4IHNvbGlkICNmNWY1ZjU7DQogIH0NCn0NCg0KLmZpbGVMaXN0IHsNCglkaXNwbGF5OiBmbGV4Ow0KCWFsaWduLWl0ZW1zOiBjZW50ZXI7DQoJZ2FwOiAyMHB4Ow0KCWZsZXg6IDE7DQoJZmxleC13cmFwOiB3cmFwOw0KICAuZmlsZUl0ZW0gew0KICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQogICAgJjpob3ZlciB7DQogICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7DQogICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7DQogICAgfQ0KDQogICAgOjp2LWRlZXAgLmVsLWNhcmRfX2JvZHkgew0KICAgICAgcGFkZGluZzogMDsNCiAgICB9DQogIH0NCn0NCg0KLy8gUERG5riy5p+T54q25oCB5o+Q56S65qC35byPDQoucmVuZGVyLXN0YXR1cy10aXAgew0KCWRpc3BsYXk6IGZsZXg7DQoJYWxpZ24taXRlbXM6IGNlbnRlcjsNCglwYWRkaW5nOiAxMHB4IDE1cHg7DQoJbWFyZ2luLWJvdHRvbTogMTVweDsNCglib3JkZXItcmFkaXVzOiA0cHg7DQoJYmFja2dyb3VuZC1jb2xvcjogI2ZmZjdlNjsNCglib3JkZXI6IDFweCBzb2xpZCAjZmZkNTkxOw0KCWNvbG9yOiAjZDQ4ODA2Ow0KCWZvbnQtc2l6ZTogMTRweDsNCgkNCglpIHsNCgkJbWFyZ2luLXJpZ2h0OiA4cHg7DQoJCWZvbnQtc2l6ZTogMTZweDsNCgl9DQoJDQoJJi5zdWNjZXNzIHsNCgkJYmFja2dyb3VuZC1jb2xvcjogI2Y2ZmZlZDsNCgkJYm9yZGVyLWNvbG9yOiAjYjdlYjhmOw0KCQljb2xvcjogIzUyYzQxYTsNCgl9DQp9DQoNCi8vIOemgeeUqOeKtuaAgeeahOivhOWIhumhueagh+mimOagt+W8jw0KLmZhY3Rvci10aXRsZS5kaXNhYmxlZCB7DQoJY29sb3I6ICM5OTkgIWltcG9ydGFudDsNCgljdXJzb3I6IG5vdC1hbGxvd2VkICFpbXBvcnRhbnQ7DQoJb3BhY2l0eTogMC42Ow0KCQ0KCSY6aG92ZXIgew0KCQljb2xvcjogIzk5OSAhaW1wb3J0YW50Ow0KCX0NCn0NCg0KLy8g5oKs5rWu5qGG5qC35byPDQouZmFjdG9yLXRvb2x0aXAgew0KCXBvc2l0aW9uOiBhYnNvbHV0ZTsNCglyaWdodDogMTAwJTsgLyog5pi+56S65Zyo54i25YWD57Sg5bem5L6nICovDQoJdG9wOiAwOw0KCW1hcmdpbi1yaWdodDogMTBweDsgLyog5LiO6K+E5YiG6aG555qE6Ze06LedICovDQoJYmFja2dyb3VuZDogI2ZmZjsNCglib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOw0KCWJvcmRlci1yYWRpdXM6IDhweDsNCglib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7DQoJd2lkdGg6IDQwMHB4Ow0KCW1heC1oZWlnaHQ6IDMwMHB4Ow0KCW92ZXJmbG93OiBoaWRkZW47DQoJei1pbmRleDogOTk5OTsNCgkNCgkudG9vbHRpcC1oZWFkZXIgew0KCQlkaXNwbGF5OiBmbGV4Ow0KCQlqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQoJCWFsaWduLWl0ZW1zOiBjZW50ZXI7DQoJCXBhZGRpbmc6IDEycHggMTZweDsNCgkJYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCgkJYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNGU3ZWQ7DQoJCQ0KCQkudG9vbHRpcC10aXRsZSB7DQoJCQlmb250LXdlaWdodDogNjAwOw0KCQkJZm9udC1zaXplOiAxNHB4Ow0KCQkJY29sb3I6ICMxNzZBREI7DQoJCX0NCgkJDQoJCS50b29sdGlwLWNsb3NlIHsNCgkJCWN1cnNvcjogcG9pbnRlcjsNCgkJCWNvbG9yOiAjOTA5Mzk5Ow0KCQkJZm9udC1zaXplOiAxNHB4Ow0KCQkJDQoJCQkmOmhvdmVyIHsNCgkJCQljb2xvcjogIzE3NkFEQjsNCgkJCX0NCgkJfQ0KCX0NCgkNCgkudG9vbHRpcC1jb250ZW50IHsNCgkJcGFkZGluZzogMTZweDsNCgkJZm9udC1zaXplOiAxNHB4Ow0KCQlsaW5lLWhlaWdodDogMS42Ow0KCQljb2xvcjogIzMzMzsNCgkJbWF4LWhlaWdodDogMjQwcHg7DQoJCW92ZXJmbG93LXk6IGF1dG87DQoJCQ0KCQkvLyDnvo7ljJbmu5rliqjmnaENCgkJJjo6LXdlYmtpdC1zY3JvbGxiYXIgew0KCQkJd2lkdGg6IDZweDsNCgkJfQ0KCQkNCgkJJjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgew0KCQkJYmFja2dyb3VuZDogI2YxZjFmMTsNCgkJCWJvcmRlci1yYWRpdXM6IDNweDsNCgkJfQ0KCQkNCgkJJjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgew0KCQkJYmFja2dyb3VuZDogI2MxYzFjMTsNCgkJCWJvcmRlci1yYWRpdXM6IDNweDsNCgkJCQ0KCQkJJjpob3ZlciB7DQoJCQkJYmFja2dyb3VuZDogI2E4YThhODsNCgkJCX0NCgkJfQ0KCX0NCn0NCg0KLy8g6K+E5YiG6aG55a655Zmo55u45a+55a6a5L2NDQouZmFjdG9yLWl0ZW0gew0KCXBvc2l0aW9uOiByZWxhdGl2ZTsNCn0NCg=="}, {"version": 3, "sources": ["one.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAq4BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "one.vue", "sourceRoot": "src/views/expertReview/business", "sourcesContent": ["<template>\r\n  <div class=\"business-review-container\">\r\n    <div class=\"business-review-main\">\r\n      <div class=\"business-review-header\">\r\n        <div class=\"business-review-title-group\">\r\n          <div class=\"business-review-title\">商务标评审</div>\r\n          <div class=\"business-review-step-group\">\r\n            <div class=\"business-review-step-text\">该页面操作说明</div>\r\n            <el-image class=\"business-review-step-img\" :src=\"srcList[0]\" :preview-src-list=\"srcList\">\r\n            </el-image>\r\n          </div>\r\n        </div>\r\n\t      \r\n\t      <!-- 文件列表 -->\r\n\t      <div class=\"fileList\" style=\"width: 200px; border-right: 1px solid #e6e6e6; border-left: 1px solid #e6e6e6; padding: 10px; overflow-y: auto;\">\r\n\t\t      <div style=\"font-weight: bold; margin-bottom: 10px; color: #333;\">响应文件附件下载</div>\r\n\t\t      <el-card\r\n\t\t\t      v-for=\"(item, index) in attachmentsList\"\r\n\t\t\t      :key=\"index\"\r\n\t\t\t      class=\"fileItem\"\r\n\t\t\t      shadow=\"hover\"\r\n\t\t\t      @click.native=\"downloadFile(item)\"\r\n\t\t\t      style=\"margin-bottom: 8px; cursor: pointer;\"\r\n\t\t      >\r\n\t\t\t      <div style=\"display: flex; align-items: center; padding: 5px;\">\r\n\t\t\t\t      <i class=\"el-icon-document\" style=\"margin-right: 8px; color: #409EFF;\"></i>\r\n\t\t\t\t      <span style=\"font-size: 12px; flex: 1; word-break: break-all;\">{{ item.fileName }}</span>\r\n\t\t\t\t      <i class=\"el-icon-download\" style=\"margin-left: 8px; color: #999;\"></i>\r\n\t\t\t      </div>\r\n\t\t      </el-card>\r\n\t      </div>\r\n\t      \r\n        <div class=\"business-review-header-btns\">\r\n          <el-button class=\"item-button\" @click=\"bidInquiry\">询标</el-button>\r\n          <el-button class=\"item-button\" v-if=\"expertInfo.expertLeader==1\" @click=\"secondOffer\">发起二次报价</el-button>\r\n          <div class=\"business-review-header-btns-group\">\r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'procurement' ? 'business-blue-btn-active' : 'business-blue-btn']\"\r\n              @click=\"viewPurchasing\">采购文件</el-button>\r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'response' ? 'business-blue-btn-active' : 'business-blue-btn']\"\r\n              @click=\"showResponseFile()\">响应文件</el-button>\r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'contrast' ? 'business-blue-btn-active' : 'business-blue-btn']\"\r\n              @click=\"fileContrast\">对比</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div style=\"height:82%\">\r\n        <!-- PDF预览区域 - 保持原始尺寸 -->\r\n        <div class=\"business-review-pdf-group\">\r\n\t        <div v-show=\"procurementShow\" :class=\"['business-review-pdf', double ? 'business-review-pdf-border-left' : '']\">\r\n\t\t        <!--            <pdfView ref=\"procurement\" :pdfurl=\"procurementPdf\" :uni_key=\"'procurement'\"></pdfView>-->\r\n\t\t        <PdfViewImproved ref=\"procurement\"  :pdfurl=\"procurementPdf\"  :page-height=\"800\" :buffer-size=\"2\"  @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"/>\r\n\t        \r\n\t        </div>\r\n\t        \r\n          <div v-show=\"responseShow\" :class=\"['business-review-pdf', double ? 'business-review-pdf-border-right' : '']\">\r\n<!--            <pdfView ref=\"response\" :pdfurl=\"responsePdf\" :uni_key=\"'response'\"></pdfView>-->\r\n\t          <PdfViewImproved ref=\"response\"  :pdfurl=\"responsePdf\"  :page-height=\"800\" :buffer-size=\"2\" @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"/>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"business-review-divider\"></div>\r\n    <div class=\"business-review-side\">\r\n      <div class=\"business-review-select-group\">\r\n        <el-select class=\"business-review-select\" v-model=\"supplier\" placeholder=\"请选择供应商\" @change=\"handleChange\">\r\n          <el-option v-for=\"item in options\" :key=\"item.bidderName\" :label=\"item.bidderName\" :value=\"item.bidderName\">\r\n          </el-option>\r\n        </el-select>\r\n      </div>\r\n      <div class=\"business-review-side-content\">\r\n        <!-- 响应文件评分项显示 -->\r\n\t      <template v-if=\"responseShow || double\">\r\n\t\t      <!-- PDF渲染状态提示 -->\r\n\t\t      <div v-if=\"!responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t\t      <i class=\"el-icon-loading\"></i>\r\n\t\t\t      <span>响应文件正在渲染中，请稍候...</span>\r\n\t\t      </div>\r\n\t\t      <div v-else class=\"render-status-tip success\">\r\n\t\t\t      <i class=\"el-icon-success\"></i>\r\n\t\t\t      <span>响应文件渲染完成，可以点击跳转</span>\r\n\t\t      </div>\r\n\t\t      <div v-for=\"(item, index) in scoringSystem.uitems\" :key=\"'response-' + index\"\r\n\t\t           class=\"factor-item business-review-factor-item\"\r\n\t\t           @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t      <!-- 悬浮框 -->\r\n\t\t\t      <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t           class=\"factor-tooltip\"\r\n\t\t\t           @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t      <div class=\"tooltip-header\">\r\n\t\t\t\t\t      <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t      <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t      </div>\r\n\t\t\t\t      <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t      </div>\r\n\t\t\t      \r\n\t\t\t      <div>\r\n\t\t\t\t      <div class=\"factors\">\r\n\t\t\t\t\t      <div class=\"business-review-factor-title-group\">\r\n\t\t\t\t\t\t      <div class=\"business-review-factor-title\" @click=\"showInfo(item)\">\r\n\t\t\t\t\t\t\t      {{ item.itemName }}\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t\t      <div class=\"business-review-factor-score-group\">\r\n\t\t\t\t\t\t      <div v-if=\"!(item.scoreLevel.length == 0 || item.scoreLevel == null || item.scoreLevel == undefined)\">\r\n\t\t\t\t\t\t\t      <el-radio v-for=\"(score,index) in item.scoreLevel.split(',')\" :key=\"index\" v-model=\"defaultRatingArray[item.entMethodItemId].state\" :label=\"score\"><span class=\"business-review-factor-score\">{{ score }}</span></el-radio>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t\t      <div v-else>\r\n\t\t\t\t\t\t\t      <el-input\r\n\t\t\t\t\t\t\t\t      placeholder=\"请输入分数\"\r\n\t\t\t\t\t\t\t\t      v-model=\"defaultRatingArray[item.entMethodItemId].state\"\r\n\t\t\t\t\t\t\t\t      @input=\"handleScoreInput(item.entMethodItemId, $event)\"\r\n\t\t\t\t\t\t\t\t      @keypress=\"onlyNumber\"\r\n\t\t\t\t\t\t\t\t      type=\"number\"\r\n\t\t\t\t\t\t\t\t      step=\"0.1\"\r\n\t\t\t\t\t\t\t\t      min=\"0\">\r\n\t\t\t\t\t\t\t      </el-input>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t      </div>\r\n\t\t\t      </div>\r\n\t\t      </div>\r\n\t      </template>\r\n\t      <template v-else-if=\"procurementShow\" >\r\n\t\t      <!-- PDF渲染状态提示 -->\r\n\t\t      <div v-if=\"!procurementPdfRendered\" class=\"render-status-tip\">\r\n\t\t\t      <i class=\"el-icon-loading\"></i>\r\n\t\t\t      <span>采购文件正在渲染中，请稍候...</span>\r\n\t\t      </div>\r\n\t\t      <div v-else class=\"render-status-tip success\">\r\n\t\t\t      <i class=\"el-icon-success\"></i>\r\n\t\t\t      <span>采购文件渲染完成，可以点击跳转</span>\r\n\t\t      </div>\r\n\t\t      \r\n\t\t      <!-- 采购文件评分项显示 -->\r\n\t\t      <div v-for=\"(item, index) in pageProcurement\" :key=\"'procurement-' + index\"\r\n\t\t           class=\"factor-item business-review-factor-item\"\r\n\t\t           @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t      <!-- 悬浮框 -->\r\n\t\t\t      <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t           class=\"factor-tooltip\"\r\n\t\t\t           @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t      <div class=\"tooltip-header\">\r\n\t\t\t\t\t      <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t      <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t      </div>\r\n\t\t\t\t      <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t      </div>\r\n\t\t\t      \r\n\t\t\t      <div>\r\n\t\t\t\t      <div class=\"factors\">\r\n\t\t\t\t\t      <div class=\"business-review-factor-title-group\">\r\n\t\t\t\t\t\t      <div class=\"business-review-factor-title\" @click=\"jumpToProcurementPage(item)\">\r\n\t\t\t\t\t\t\t      {{ item.itemName }}\r\n\t\t\t\t\t\t\t      <span style=\"font-size: 12px;color: red;\"> 最高{{ getMaxScore(item) }}分</span>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t      </div>\r\n\t\t\t      </div>\r\n\t\t      </div>\r\n\t      </template>\r\n       \r\n\t      <div class=\"business-review-submit-group\">\r\n          <!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"save\">保存</el-button></div> -->\r\n          <div><el-button class=\"item-button-little business-review-submit-btn\" @click=\"submit\">提交</el-button></div>\r\n        </div>\r\n\r\n        <div class=\"business-review-content\">\r\n          <div class=\"business-review-content-title\">评审内容：</div>\r\n          <div class=\"business-review-content-remark\" v-html=\"selectNode.itemRemark\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  supplierInfo,\r\n  approvalProcess,\r\n  scoringFactors,\r\n  checkReviewSummary,\r\n  filesById,\r\n} from \"@/api/expert/review\";\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\";\r\nimport { reEvaluate } from \"@/api/expert/review\";\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      options: [],\r\n      scoringSystem: [],\r\n      selectNode: {},\r\n      supplier: \"\",\r\n      selectSupplier: {},\r\n      expertInfo: {},\r\n      defaultRatingArray: {},\r\n      file: {},\r\n      responseShow: false,\r\n      procurementShow: false,\r\n      double: false,\r\n\r\n      entDocResponsePage: {},\r\n      factorsPage: {},\r\n      bidderFactor: {},\r\n      responsePdf: null,\r\n      procurementPdf: null,\r\n\t    currentMaxScore: null,\r\n\r\n      // 按钮状态管理\r\n      activeButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n      // 采购文件相关数据\r\n      entDocProcurementPage: {}, // 采购文件页码信息\r\n      pageProcurement: [], // 采购文件的评分项\r\n      attachmentsList:[], // 文件列表\r\n\t    \r\n\t    // PDF渲染状态管理\r\n\t    responsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n\t    procurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n      srcList: [\"/evalution/help.jpg\"],\r\n\t    \r\n\t    // 悬停状态管理\r\n\t    hoveredFactorNode: null, // 悬停时的评分项\r\n\t    tooltipTimer: null, // 悬浮框显示定时器\r\n    };\r\n  },\r\n  methods: {\r\n    /**\r\n     * 限制输入框只能输入数字和小数点\r\n     * @param {Event} event - 键盘事件\r\n     */\r\n    onlyNumber(event) {\r\n      // 获取按键的字符码\r\n      const charCode = event.which || event.keyCode;\r\n\r\n      // 允许的字符：数字(48-57)、小数点(46)、退格(8)、删除(46)、Tab(9)、Enter(13)、方向键(37-40)\r\n      if (\r\n        (charCode >= 48 && charCode <= 57) || // 数字 0-9\r\n        charCode === 46 || // 小数点\r\n        charCode === 8 ||  // 退格键\r\n        charCode === 9 ||  // Tab键\r\n        charCode === 13 || // Enter键\r\n        (charCode >= 37 && charCode <= 40) // 方向键\r\n      ) {\r\n        return true;\r\n      }\r\n\r\n      // 阻止其他字符输入\r\n      event.preventDefault();\r\n      return false;\r\n    },\r\n\r\n    /**\r\n     * 处理分数输入，确保只能输入有效的数字\r\n     * @param {string} itemId - 评估项ID\r\n     * @param {string} value - 输入值\r\n     */\r\n    handleScoreInput(itemId, value) {\r\n      // 移除非数字字符（保留小数点）\r\n      let cleanValue = value.replace(/[^\\d.]/g, '');\r\n\r\n      // 确保只有一个小数点\r\n      const parts = cleanValue.split('.');\r\n      if (parts.length > 2) {\r\n        cleanValue = parts[0] + '.' + parts.slice(1).join('');\r\n      }\r\n\r\n      // 限制小数点后最多2位\r\n      if (parts.length === 2 && parts[1].length > 2) {\r\n        cleanValue = parts[0] + '.' + parts[1].substring(0, 2);\r\n      }\r\n\r\n      // 更新值\r\n      this.defaultRatingArray[itemId].state = cleanValue;\r\n\r\n      // 调用原有的验证方法\r\n      this.validateScore(itemId, cleanValue);\r\n    },\r\n\r\n    init() {\r\n      const expertInfo = JSON.parse(localStorage.getItem(\"expertInfo\"));\r\n      this.entDocResponsePage = JSON.parse(\r\n        localStorage.getItem(\"entDocResponsePage\")\r\n      );\r\n      // 初始化采购文件页码信息\r\n      this.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\"));\r\n      supplierInfo({ projectId: this.$route.query.projectId, isAbandonedBid: 0 }).then(\r\n        (response) => {\r\n          if (response.code == 200) {\r\n            this.options = response.rows.filter(item => item.isAbandonedBid == 0);\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        }\r\n      );\r\n      approvalProcess(this.$route.query.projectId, expertInfo.resultId).then(\r\n        (response) => {\r\n          if (response.code == 200) {\r\n            // 文件列表\r\n            this.attachmentsList = response.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n\r\n            this.scoringSystem =\r\n              response.data.scoringMethodUinfo.scoringMethodItems.find(\r\n                (item) => {\r\n                  return (\r\n                    item.scoringMethodItemId ==\r\n                    this.$route.query.scoringMethodItemId\r\n                  );\r\n                }\r\n              );\r\n            localStorage.setItem(\r\n              \"evalProjectEvaluationProcess\",\r\n              JSON.stringify(this.scoringSystem.evalProjectEvaluationProcess)\r\n            );\r\n\r\n            this.defaultRatingArray = this.scoringSystem.uitems.reduce(\r\n              (acc, _, index) => {\r\n                acc[this.scoringSystem.uitems[index].entMethodItemId] = {\r\n                  state: null,\r\n                  reason: \"\",\r\n                };\r\n                return acc;\r\n              },\r\n              {}\r\n            );\r\n            console.log(\"this.scoringSystem.items\", this.scoringSystem.uitems);\r\n          } else {\r\n            this.$messgae.warning(response.msg);\r\n          }\r\n        }\r\n      );\r\n      filesById(this.$route.query.projectId).then((response) => {\r\n        if (response.code == 200) {\r\n          this.file = response.data;\r\n          if (this.file.tenderNoticeFilePath != undefined) {\r\n            this.procurementPdf = this.file.tenderNoticeFilePath;\r\n          }\r\n          // if (this.file.file != undefined) {\r\n          //   this.responsePdf = this.file.file[0];\r\n          // }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      // 初始化专家信息\r\n      this.initExpertInfo();\r\n    },\r\n\r\n    /**\r\n     * 初始化专家信息\r\n     */\r\n    initExpertInfo() {\r\n      try {\r\n        const itemString = localStorage.getItem(\"expertInfo\");\r\n        if (itemString) {\r\n          this.expertInfo = JSON.parse(itemString);\r\n          console.log(\"专家信息已初始化\", this.expertInfo);\r\n        } else {\r\n          console.warn(\"localStorage中未找到expertInfo\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"初始化专家信息失败:\", error);\r\n      }\r\n    },\r\n\r\n    handleChange(value) {\r\n      if(Object.keys(this.selectSupplier).length != 0){\r\n        this.tmpSave();\r\n      }\r\n      this.selectSupplier = this.options.find((item) => {\r\n        return item.bidderName == value;\r\n      });\r\n\r\n      // 根据bidderid获取供应商因素及其对应页码\r\n      this.bidderFactor = this.factorsPage[this.selectSupplier.bidderId];\r\n\r\n      const data = {\r\n        expertResultId: this.expertInfo.resultId,\r\n        projectId: this.$route.query.projectId,\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n      };\r\n      getDetailByPsxx(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.factorList = response.data;\r\n          const factor = this.factorList.find((item) => {\r\n            return item.bidderName == value;\r\n          }).evalExpertEvaluationDetails;\r\n          if (factor != null) {\r\n            factor.map((item) => {\r\n              this.defaultRatingArray[item.scoringMethodUitemId].reason =\r\n                item.evaluationRemark;\r\n              this.defaultRatingArray[item.scoringMethodUitemId].state =\r\n                item.evaluationResult;\r\n            });\r\n          } else {\r\n            Object.keys(this.defaultRatingArray).forEach((key) => {\r\n              this.defaultRatingArray[key].state = null;\r\n              this.defaultRatingArray[key].reason = \"\";\r\n            });\r\n          }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      this.showResponseFile();\r\n    },\r\n\t  validateScore(itemId, event) {\r\n\t\t  const inputValue = parseFloat(event);\r\n\t\t  console.log(\"inputValue\", inputValue);\r\n\t\t  \r\n\t\t  // 获取当前评分项的最大分值\r\n\t\t  const currentItem = this.scoringSystem.uitems.find(item => item.entMethodItemId === itemId);\r\n\t\t  let maxScore = null;\r\n\t\t  \r\n\t\t  if (currentItem) {\r\n\t\t\t  // 如果有分数挡位，使用挡位中的最大值\r\n\t\t\t  if (currentItem.scoreLevel && currentItem.scoreLevel.length > 0) {\r\n\t\t\t\t  const scoreLevels = currentItem.scoreLevel.split(',').map(item => parseFloat(item.trim())).filter(item => !isNaN(item));\r\n\t\t\t\t  if (scoreLevels.length > 0) {\r\n\t\t\t\t\t  maxScore = Math.max(...scoreLevels);\r\n\t\t\t\t  }\r\n\t\t\t  } else {\r\n\t\t\t\t  // 否则使用配置的最大分值\r\n\t\t\t\t  maxScore = parseFloat(currentItem.score);\r\n\t\t\t  }\r\n\t\t  }\r\n\t\t  \r\n\t\t  console.log(\"maxScore\", maxScore);\r\n\t\t  \r\n\t\t  if (!isNaN(inputValue) && maxScore !== null) {\r\n\t\t\t  if (inputValue > maxScore) {\r\n\t\t\t\t  this.$message.warning(`输入分数不能超过${maxScore}分，请重新输入`);\r\n\t\t\t\t  // 将输入值限制为最大分数值\r\n\t\t\t\t  this.defaultRatingArray[itemId].state = \"\";\r\n\t\t\t  } else if (inputValue < 0) {\r\n\t\t\t\t  this.$message.warning(\"输入分数不能小于0分\");\r\n\t\t\t\t  this.defaultRatingArray[itemId].state = \"\";\r\n\t\t\t  }\r\n\t\t  }\r\n\t  },\r\n    showResponseFile() {\r\n      if (Object.keys(this.selectSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        this.activeButton = 'response'; // 设置当前激活按钮\r\n        this.double = false;\r\n        this.procurementShow = false;\r\n        this.responseShow = true;\r\n        this.responsePdf = this.file.file[this.selectSupplier.bidderId];\r\n      }\r\n    },\r\n    fileContrast() {\r\n      if (Object.keys(this.selectSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        this.activeButton = 'contrast'; // 设置当前激活按钮\r\n        this.double = true;\r\n        this.procurementShow = true;\r\n        this.responseShow = true;\r\n        this.responsePdf = this.file.file[this.selectSupplier.bidderId];\r\n      }\r\n    },\r\n    showInfo(item) {\r\n\t    // 检查PDF是否渲染完成\r\n\t    if (!this.canJumpToPage()) {\r\n\t\t    this.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t    return;\r\n\t    }\r\n\t\t\t\r\n      this.selectNode = item;\r\n\r\n      // 如果只显示采购文件，使用采购文件页码信息\r\n      if (this.procurementShow && !this.responseShow) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        if (item.jumpToPage) {\r\n          this.$refs.procurement.skipPage(item.jumpToPage);\r\n        } else if (this.entDocProcurementPage && this.entDocProcurementPage[item.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[item.itemName]);\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 如果显示响应文件或对比模式，需要选择供应商\r\n      if (Object.keys(this.bidderFactor).length != 0) {\r\n        // 跳转到响应文件对应页码\r\n        if (this.responseShow && this.$refs.response) {\r\n\t        if (!this.responsePdfRendered) {\r\n\t\t        this.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t        return;\r\n\t        }\r\n\t\t\t\t\t\r\n          this.$refs.response.skipPage(\r\n            this.bidderFactor[this.selectNode.itemName]\r\n          );\r\n        }\r\n\r\n        // 跳转到采购文件对应页码\r\n        if (this.procurementShow && this.$refs.procurement) {\r\n\t        if (!this.procurementPdfRendered) {\r\n\t\t        this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t        return;\r\n\t        }\r\n\t\t\t\t\t\r\n          // 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n          if (this.entDocProcurementPage && this.entDocProcurementPage[item.itemName]) {\r\n            this.$refs.procurement.skipPage(this.entDocProcurementPage[item.itemName]);\r\n          } else {\r\n            // 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n            // 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n          }\r\n        }\r\n      } else {\r\n        this.$message.warning(\"请先选择供应商\");\r\n      }\r\n    },\r\n    initDefaultRatingArray(){\r\n      Object.keys(this.defaultRatingArray).forEach((key) => {\r\n        this.defaultRatingArray[key].state = null;\r\n        this.defaultRatingArray[key].reason = \"\";\r\n      });\r\n    },\r\n    /**\r\n     * 校验所有评分项是否填写完整\r\n     * @returns {boolean} 是否全部填写\r\n     */\r\n    validateAllRatings() {\r\n      for (const item of this.scoringSystem.uitems) {\r\n        const state = this.defaultRatingArray[item.entMethodItemId].state;\r\n\r\n        // 评分结果未填写\r\n        if (state === null || state === '' || state === undefined) {\r\n          // this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n          return true;\r\n        }\r\n\r\n        // 对于分数评分，检查是否为有效数值\r\n        if (!item.scoreLevel || item.scoreLevel.length === 0 || item.scoreLevel === null || item.scoreLevel === undefined) {\r\n          const score = parseFloat(state);\r\n          if (isNaN(score) || score < 0) {\r\n            this.$message.warning(`${item.itemName}的评分必须是有效的数值且不能小于0`);\r\n            return false;\r\n          }\r\n          // 检查分数是否超过最大值\r\n          const maxScore = this.getMaxScore(item);\r\n          if (score > maxScore) {\r\n            this.$message.warning(`${item.itemName}的评分不能超过${maxScore}分`);\r\n            return false;\r\n          }\r\n        } else {\r\n          // 对于有挡位的评分，检查是否在允许的挡位范围内\r\n          const scoreLevels = item.scoreLevel.split(',').map(level => level.trim());\r\n          if (!scoreLevels.includes(state.toString())) {\r\n            this.$message.warning(`${item.itemName}的评分必须选择指定的挡位：${item.scoreLevel}`);\r\n            return false;\r\n          }\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n\r\n    tmpSave(){\r\n      console.log(\"-------开始保存评审结果----------------\");\r\n\r\n      // 先校验所有评分项是否填写完整\r\n      if (!this.validateAllRatings()) {\r\n        return Promise.resolve({ code: 0, success: false }); // 校验失败\r\n      }\r\n\r\n      var ratingArray = JSON.parse(JSON.stringify(this.defaultRatingArray));\r\n\r\n      var data = [];\r\n      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        const item = this.scoringSystem.uitems[index];\r\n        const itemId = item.entMethodItemId;\r\n        // 获取当前项对应的评分结果\r\n        const evaluationResult = ratingArray[itemId].state;\r\n        if (evaluationResult === null || evaluationResult === \"\") {\r\n          // 如果评分结果为空，则不保存此条信息\r\n          console.log(\"-------评分结果为空，不保存此条信息----------------\");\r\n          continue;\r\n        }\r\n          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n          const evaluationRemark = ratingArray[itemId].reason || \"\";\r\n          data.push({\r\n            scoringMethodUitemId: itemId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            entId: this.selectSupplier.bidderId,\r\n            evaluationResult: evaluationResult,\r\n            evaluationRemark: evaluationRemark\r\n          });\r\n        }\r\n        if(data.length>0){\r\n            console.log(\"-------开始后台保存评审结果----------------\");\r\n            return scoringFactors(data).then((response) => {\r\n              console.log(response.msg);\r\n              if (response.code == 200) {\r\n                this.$message.success(\"保存成功\");\r\n                return { code: 200, success: true };\r\n              } else {\r\n                this.$message.warning(response.msg);\r\n                return { code: response.code, success: false };\r\n              }\r\n            }).catch((error) => {\r\n              this.$message.error(\"保存失败\");\r\n              return { code: 0, success: false };\r\n            });\r\n        }else{\r\n          return Promise.resolve({ code: 200, success: true }); // 没有数据需要保存时也返回成功\r\n        }\r\n    },\r\n    save() {\r\n      if (this.supplier == \"\") {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        //const data = this.generatingSavedData();\r\n        var data = [];\r\n        for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n          const item = this.scoringSystem.uitems[index];\r\n          const itemId = item.entMethodItemId;\r\n          // 获取当前项对应的评分结果\r\n          const evaluationResult = this.defaultRatingArray[itemId].state;\r\n          console.log(evaluationResult)\r\n          if (evaluationResult === null || evaluationResult === \"\") {\r\n            // 如果评分结果为空，弹出提示，提示内容包含该项的itemName\r\n            this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n            return; // 直接返回，不再继续构建数据，等待用户填写完整\r\n          }\r\n          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n          const evaluationRemark = this.defaultRatingArray[itemId].reason || \"\";\r\n          data.push({\r\n            scoringMethodUitemId: itemId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            entId: this.selectSupplier.bidderId,\r\n            evaluationResult: evaluationResult,\r\n            evaluationRemark: evaluationRemark\r\n          });\r\n        }\r\n        scoringFactors(data).then((response) => {\r\n          if (response.code == 200) {\r\n            this.$message.success(response.msg);\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 生成保存数据\r\n    generatingSavedData() {\r\n      var data = [];\r\n      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        data.push({\r\n          scoringMethodUitemId:\r\n            this.scoringSystem.uitems[index].entMethodItemId,\r\n          expertResultId: this.expertInfo.resultId,\r\n          entId: this.selectSupplier.bidderId,\r\n          evaluationResult:\r\n            this.defaultRatingArray[\r\n              this.scoringSystem.uitems[index].entMethodItemId\r\n            ].state,\r\n          evaluationRemark:\r\n            this.defaultRatingArray[\r\n              this.scoringSystem.uitems[index].entMethodItemId\r\n            ].reason,\r\n        });\r\n      }\r\n      return data;\r\n    },\r\n    submit() {\r\n        this.tmpSave().then((saveResult) => {\r\n          // 检查保存结果，如果校验失败则不继续提交\r\n          if (!saveResult || saveResult.success === false) {\r\n            return; // 校验失败，不继续提交流程\r\n          }\r\n\r\n          const data = {\r\n            projectId: this.$route.query.projectId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n          };\r\n          checkReviewSummary(data).then((response) => {\r\n            if (response.code == 200) {\r\n            // 修改专家进度\r\n            const status = {\r\n              evalExpertScoreInfoId: JSON.parse(\r\n                localStorage.getItem(\"evalExpertScoreInfo\")\r\n              ).evalExpertScoreInfoId,\r\n              evalState: 1,\r\n            };\r\n            editEvalExpertScoreInfo(status).then((res) => {\r\n              if (res.code == 200) {\r\n                this.$message.success(\"提交成功\");\r\n              }\r\n            });\r\n            this.$emit(\"send\", \"two\");\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      })\r\n    },\r\n    /**\r\n     * 显示采购文件PDF\r\n     */\r\n    viewPurchasing() {\r\n      this.activeButton = 'procurement'; // 设置当前激活按钮\r\n      this.double = false; // 单文件模式\r\n      this.responseShow = false; // 不显示响应文件\r\n      this.procurementShow = true; // 显示采购文件\r\n\r\n      // 右侧评分项显示为采购文件的评分项\r\n      let pageProcurementArr = []; // 采购文件评分项数组\r\n      for (let item in this.entDocProcurementPage){\r\n        pageProcurementArr.push({\r\n          itemName: item,\r\n          jumpToPage: this.entDocProcurementPage[item]\r\n        })\r\n      }\r\n\r\n      console.log(this.scoringSystem.uitems);\r\n      console.log(pageProcurementArr)\r\n      this.pageProcurement = [];\r\n      for (let i = 0; i < this.scoringSystem.uitems.length;i++){\r\n        for (let j = 0; j < pageProcurementArr.length;j++){\r\n          if (this.scoringSystem.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n            this.pageProcurement.push({...this.scoringSystem.uitems[i],...pageProcurementArr[j]});\r\n          }\r\n        }\r\n      }\r\n      console.log(this.pageProcurement)\r\n    },\r\n\r\n    /**\r\n     * 跳转到采购文件对应页码\r\n     * @param {Object} item - 评分项对象\r\n     */\r\n    jumpToProcurementPage(item) {\r\n      if (item.jumpToPage && this.$refs.procurement) {\r\n        this.$refs.procurement.skipPage(item.jumpToPage);\r\n      }\r\n    },\r\n\t  \r\n\t  /**\r\n\t   * 检查是否可以跳转页面\r\n\t   * @returns {boolean} 是否可以跳转\r\n\t   */\r\n\t  canJumpToPage() {\r\n\t\t  // 如果只显示采购文件\r\n\t\t  if (this.procurementShow && !this.responseShow) {\r\n\t\t\t  return this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  // 如果只显示响应文件\r\n\t\t  if (this.responseShow && !this.procurementShow) {\r\n\t\t\t  return this.responsePdfRendered;\r\n\t\t  }\r\n\t\t  // 如果对比模式（两个都显示）\r\n\t\t  if (this.responseShow && this.procurementShow) {\r\n\t\t\t  return this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  return false;\r\n\t  },\r\n\t  /**\r\n\t   * 处理PDF渲染状态变化\r\n\t   * @param {boolean} isRendered 是否渲染完成\r\n\t   * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t   */\r\n\t  handlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t\t  if (pdfType === 'response') {\r\n\t\t\t  this.responsePdfRendered = isRendered;\r\n\t\t  } else if (pdfType === 'procurement') {\r\n\t\t\t  this.procurementPdfRendered = isRendered;\r\n\t\t  }\r\n\t\t  \r\n\t\t  if (isRendered) {\r\n\t\t\t  console.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t\t  }\r\n\t  },\r\n\t  \r\n    // 跳转到二次报价\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query: query });\r\n    },\r\n    // 跳转到询标\r\n    bidInquiry() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      this.$router.push({ path: \"/bidInquiry\", query: query });\r\n    },\r\n    // 获取因素对应页码\r\n    getFactorsPage() {\r\n      this.factorsPage = JSON.parse(localStorage.getItem(\"entDocResponsePage\"));\r\n    },\r\n    downloadFile(item){\r\n      this.$download.zip(item.filePath,item.fileName);\r\n    },\r\n    \r\n    /**\r\n     * 获取评分项的最大分值\r\n     * @param {Object} item - 评分项对象\r\n     * @returns {number} 最大分值\r\n     */\r\n    getMaxScore(item) {\r\n      if (!item) return 0;\r\n      \r\n      // 如果有分数挡位，使用挡位中的最大值\r\n      if (item.scoreLevel && item.scoreLevel.length > 0 && item.scoreLevel !== null && item.scoreLevel !== undefined) {\r\n        const scoreLevels = item.scoreLevel.split(',').map(level => parseFloat(level.trim())).filter(level => !isNaN(level));\r\n        if (scoreLevels.length > 0) {\r\n          return Math.max(...scoreLevels);\r\n        }\r\n      }\r\n      \r\n      // 否则使用配置的最大分值\r\n      return parseFloat(item.score) || 0;\r\n    },\r\n\t  \r\n\t  // ========== 悬停相关 ==========\r\n\t  /**\r\n\t   * 显示评分项悬浮框\r\n\t   * @param {Object} factorItem 评分项对象\r\n\t   */\r\n\t  showFactorTooltip(factorItem) {\r\n\t\t  if (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\t\t  \r\n\t\t  // 清除之前的定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t  this.tooltipTimer = setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = factorItem;\r\n\t\t  }, 300); // 300ms延迟\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 隐藏评分项悬浮框\r\n\t   */\r\n\t  hideFactorTooltip() {\r\n\t\t  // 清除定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t  setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = null;\r\n\t\t  }, 100);\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t   */\r\n\t  clearTooltipTimer() {\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t  }\r\n  },\r\n  mounted() {\r\n    this.init();\r\n    this.getFactorsPage();\r\n  },\r\n\t\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.business-review-container {\r\n  min-height: 57vh;\r\n  display: flex;\r\n}\r\n.business-review-main {\r\n  min-height: 57vh;\r\n  width: 79%;\r\n}\r\n.business-review-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.business-review-title-group {\r\n  display: flex;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333333;\r\n}\r\n.business-review-title {\r\n  /* 保持原样 */\r\n}\r\n.business-review-step-group {\r\n  display: grid;\r\n  justify-items: center;\r\n  position: relative;\r\n  bottom: -30px;\r\n}\r\n.business-review-step-text {\r\n  font-size: 12px;\r\n}\r\n.business-review-step-img {\r\n  width: 80px;\r\n  height: 30px;\r\n  margin-right: 20px;\r\n}\r\n.business-review-header-btns {\r\n  text-align: right;\r\n}\r\n.business-review-header-btns-group {\r\n  margin-top: 20px;\r\n}\r\n.business-review-header-btns-group .item-button {\r\n  background-color: #176ADB;\r\n  color: #FFFFFF;\r\n  border: 1px solid #176ADB;\r\n}\r\n.business-review-pdf-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  height: 82%;\r\n}\r\n.business-review-pdf {\r\n  width: 49%;\r\n}\r\n.business-review-pdf-border-right {\r\n  border-right: 1px solid #176ADB;\r\n}\r\n.business-review-pdf-border-left {\r\n  border-left: 1px solid #176ADB;\r\n}\r\n.business-review-divider {\r\n  min-height: 57vh;\r\n  width: 1%;\r\n  background-color: #F5F5F5;\r\n}\r\n.business-review-side {\r\n  min-height: 57vh;\r\n  width: 20%;\r\n}\r\n.business-review-select-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.business-review-select {\r\n  width: 100%;\r\n}\r\n.business-review-side-content {\r\n  padding: 15px 20px;\r\n}\r\n.business-review-factor-item {\r\n  margin-bottom: 10px;\r\n}\r\n.business-review-factor-title-group {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  text-align: left;\r\n  width: 98%;\r\n}\r\n.business-review-factor-title {\r\n  cursor: pointer;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 500;\r\n  font-size: 16px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n}\r\n.business-review-factor-score-group {\r\n  display: flex;\r\n  width: 100%;\r\n  justify-content: flex-end;\r\n  padding: 10px;\r\n}\r\n.business-review-factor-score {\r\n  color: green;\r\n  font-size: 16px;\r\n}\r\n.business-review-submit-group {\r\n  display: flex;\r\n  margin: 32px 0;\r\n  justify-content: space-evenly;\r\n}\r\n.business-review-submit-btn {\r\n  background-color: #176ADB;\r\n}\r\n.business-review-content {\r\n  text-align: left;\r\n  font-size: 14px;\r\n}\r\n.business-review-content-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 15px;\r\n  color: #176ADB;\r\n  letter-spacing: 0;\r\n}\r\n.business-review-content-remark {\r\n  padding: 6px 30px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n\t  transition: all 0.3s ease;\r\n\t  padding: 4px 8px;\r\n\t  border-radius: 4px;\r\n\t  \r\n\t  &:hover {\r\n\t\t  background-color: #f0f8ff;\r\n\t\t  color: #176ADB;\r\n\t\t  transform: translateX(2px);\r\n\t  }\r\n  }\r\n}\r\n.item-button {\r\n  border: 1px solid #979797;\r\n  width: 150px;\r\n  height: 36px;\r\n  margin: 0 10px;\r\n  font-weight: 700;\r\n  font-size: 17px;\r\n  border-radius: 6px;\r\n  color: #333333;\r\n  &:hover {\r\n    color: #333333;\r\n  }\r\n}\r\n.business-blue-btn {\r\n  background-color: #176ADB !important;\r\n  color: #fff !important;\r\n  border: 1px solid #176ADB !important;\r\n}\r\n.business-blue-btn-active {\r\n  background-color: #FF6B35 !important;\r\n  color: #fff !important;\r\n  border: 1px solid #FF6B35 !important;\r\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.item-button-little {\r\n  width: 124px;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #fff;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n  .fileItem {\r\n    transition: all 0.3s ease;\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    }\r\n\r\n    ::v-deep .el-card__body {\r\n      padding: 0;\r\n    }\r\n  }\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\t\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\t\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\t\t\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\t\t\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\t\t\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n</style>\r\n"]}]}