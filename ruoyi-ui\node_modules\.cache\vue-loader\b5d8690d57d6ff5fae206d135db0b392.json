{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business.vue?vue&type=style&index=0&id=333b0e3a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business.vue", "mtime": 1753923515058}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouaW5mbyB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KLmNvbnRlbnQgewogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgd2lkdGg6IDkwJTsKICBtaW4taGVpZ2h0OiA2NHZoOwogIG1hcmdpbjogMjBweCAwOwp9Ci5pdGVtIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZm9udC1zaXplOiAxOHB4OwogIG1hcmdpbi1ib3R0b206IDgwcHg7CiAgLml0ZW0tdGl0bGUgewogICAgd2lkdGg6IDEyMHB4OwogICAgbWFyZ2luLXJpZ2h0OiAyMHB4OwogICAgdGV4dC1hbGlnbjogbGVmdDsKICB9Cn0KLmxpdHRsZS10aXRsZSB7CiAgY29sb3I6IHJnYmEoODAsIDgwLCA4MCwgMSk7CiAgZm9udC1zaXplOiAxNHB4Owp9Ci5pdGVtLWJ1dHRvbiB7CiAgYm9yZGVyOiAjMzMzIDFweCBzb2xpZDsKICB3aWR0aDogMTU1cHg7CiAgaGVpZ2h0OiA0OHB4OwogIG1hcmdpbjogMjBweCAyOHB4OwogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTUxLCAyNTMsIDI0NiwgMSk7CiAgY29sb3I6IHJnYmEoMCwgMCwgMCwgMSk7CiAgJjpob3ZlciB7CiAgICBjb2xvcjogcmdiYSgwLCAwLCAwLCAxKTsKICB9Cn0KLml0ZW0tYnV0dG9uLWxpdHRsZSB7CiAgYm9yZGVyOiAjMzMzIDFweCBzb2xpZDsKICB3aWR0aDogMTI0cHg7CiAgaGVpZ2h0OiAzMnB4OwogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTUxLCAyNTMsIDI0NiwgMSk7CiAgY29sb3I6IHJnYmEoMCwgMCwgMCwgMSk7CiAgJjpob3ZlciB7CiAgICBjb2xvcjogcmdiYSgwLCAwLCAwLCAxKTsKICB9Cn0KLmZhY3RvcnMgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KLnRpdGxlewoJYmFja2dyb3VuZC1jb2xvcjogI2M4YzljYzsKCXBhZGRpbmc6IDEwcHggNSU7Cn0K"}, {"version": 3, "sources": ["business.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "business.vue", "sourceRoot": "src/views/expertReview", "sourcesContent": ["<template>\n  <div>\n<!--    <BidHeadthree></BidHeadthree>-->\n\t  <div class=\"title\">专家评审系统</div>\n    <div class=\"info\">\n      <div class=\"content\">\n        <one v-if=\"node == 'one'\" @send=\"handleData\"></one>\n        <two v-if=\"node == 'two'\" @send=\"handleData\" :isLeader=\"isLeader\" :finish=\"finish\"></two>\n        <three v-if=\"node == 'three'\" @send=\"handleData\" :finish=\"finish\"></three>\n      </div>\n    </div>\n    <Foot></Foot>\n  </div>\n\n</template>\n\n<script>\nimport one from \"./business/one\";\nimport two from \"./business/two\";\nimport three from \"./business/three\";\nimport { getProject } from \"@/api/tender/project\";\nimport { expertInfoById } from \"@/api/expert/review\";\nimport { getEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\nimport expertReviewWebSocket from \"@/mixins/expertReviewWebSocket\";\n\nexport default {\n  components: { one, two, three },\n  mixins: [expertReviewWebSocket],\n  name: \"qualification\",\n  data() {\n    return {\n      projectName: \"测试项目\",\n      project: {},\n      node: \"one\",\n      finish: false,\n      leader: {},\n      isLeader: false,\n    };\n  },\n  methods: {\n    async init() {\n      try {\n        // 根据项目id查询项目信息\n        const projectResponse = await getProject(this.$route.query.projectId);\n        if (projectResponse.code === 200) {\n          this.project = projectResponse.data;\n        } else {\n          this.$message.warning(projectResponse.msg);\n        }\n\n        // 获取专家信息\n        const expertResponse = await expertInfoById({\n          projectId: this.$route.query.projectId,\n        });\n        if (expertResponse.code === 200) {\n          this.leader = expertResponse.data.find(\n            (item) => item.expertLeader === 1\n          );\n          console.log(\"this.leader\", this.leader);\n\n          if (this.leader && this.leader.zjhm === this.$route.query.zjhm) {\n            this.isLeader = true;\n          }\n        } else {\n          this.$message.warning(expertResponse.msg);\n        }\n\n        // 设置 finish 和 node 的逻辑\n        this.finish = this.$route.query.finish === \"true\";\n        console.log(\"this.finish\", this.finish, \"this.isLeader\", this.isLeader);\n\t      \n\t      // // 判断当前环境\n\t      if (process.env.NODE_ENV === \"development\") {\n\t\t      this.node = \"one\";\n\t\t      return\n\t      }\n\t\t\t\t\n        // 判断是否满足条件\n        if (this.finish && this.isLeader) {\n          this.node = \"three\";\n        } else if (this.finish && !this.isLeader) {\n          this.node = \"two\";\n        } else {\n          this.getEvalExpertStatus();\n        }\n      } catch (error) {\n        console.error(\"Error during API calls:\", error);\n        this.$message.error(\"An error occurred while fetching data.\");\n      }\n    },\n    // 查询专家评审节点信息\n    getEvalExpertStatus() {\n      // 查询专家评审节点信息\n      getEvalExpertScoreInfo({\n        projectEvaluationId: JSON.parse(\n          localStorage.getItem(\"evalProjectEvaluationProcess\")\n        ).projectEvaluationId,\n        expertResultId: JSON.parse(localStorage.getItem(\"expertResultId\")),\n        scoringMethodItemId: JSON.parse(\n          localStorage.getItem(\"evalProjectEvaluationProcess\")\n        ).scoringMethodItemId,\n      }).then((expertStatusResponse) => {\n        if (expertStatusResponse.code == 200) {\n          localStorage.setItem(\n            \"evalExpertScoreInfo\",\n            JSON.stringify(expertStatusResponse.data)\n          );\n          if (expertStatusResponse.data.evalState == 0) {\n            this.node = \"one\";\n          } else if (expertStatusResponse.data.evalState == 1) {\n            this.node = \"two\";\n          } else if (expertStatusResponse.data.evalState == 2) {\n            if(this.isLeader ){\n              this.node = \"three\";\n            }else{\n              this.node = \"two\";\n            }\n          }\n        }\n      });\n    },\n    // 跳转到二次报价\n    secondOffer() {\n      const query = {\n        projectId: this.$route.query.projectId,\n        zjhm: this.$route.query.zjhm,\n        scoringMethodItemId: JSON.parse(\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\n        ),\n      };\n      this.$router.push({ path: \"/secondOffer\", query: query });\n    },\n    handleData(data) {\n      this.node = data;\n    },\n    // 发送消息给所有专家\n    sendMessageToExperts(message) {\n      if (this.reviewWebSocket && this.reviewWebSocket.readyState === WebSocket.OPEN) {\n        this.reviewWebSocket.send(JSON.stringify(message));\n      }\n    },\n  },\n  mounted() {\n    this.init();\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.info {\n  background-color: #f5f5f5;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n.content {\n  background-color: #fff;\n  width: 90%;\n  min-height: 64vh;\n  margin: 20px 0;\n}\n.item {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  font-size: 18px;\n  margin-bottom: 80px;\n  .item-title {\n    width: 120px;\n    margin-right: 20px;\n    text-align: left;\n  }\n}\n.little-title {\n  color: rgba(80, 80, 80, 1);\n  font-size: 14px;\n}\n.item-button {\n  border: #333 1px solid;\n  width: 155px;\n  height: 48px;\n  margin: 20px 28px;\n  background-color: rgba(151, 253, 246, 1);\n  color: rgba(0, 0, 0, 1);\n  &:hover {\n    color: rgba(0, 0, 0, 1);\n  }\n}\n.item-button-little {\n  border: #333 1px solid;\n  width: 124px;\n  height: 32px;\n  background-color: rgba(151, 253, 246, 1);\n  color: rgba(0, 0, 0, 1);\n  &:hover {\n    color: rgba(0, 0, 0, 1);\n  }\n}\n.factors {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.title{\n\tbackground-color: #c8c9cc;\n\tpadding: 10px 5%;\n}\n</style>"]}]}