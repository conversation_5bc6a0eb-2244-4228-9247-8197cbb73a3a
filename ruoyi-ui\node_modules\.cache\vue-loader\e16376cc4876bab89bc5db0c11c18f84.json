{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\technical\\one.vue?vue&type=style&index=0&id=91ddf4be&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\technical\\one.vue", "mtime": 1753924220136}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovLyBNYWluIGxheW91dCBjb250YWluZXJzDQoudGVjaG5pY2FsLXJldmlldy1jb250YWluZXIgew0KICBtaW4taGVpZ2h0OiA1N3ZoOw0KICBkaXNwbGF5OiBmbGV4Ow0KfQ0KDQoubWFpbi1jb250ZW50IHsNCiAgbWluLWhlaWdodDogNTd2aDsNCiAgd2lkdGg6IDc5JTsNCn0NCg0KLy8gSGVhZGVyIHNlY3Rpb24gc3R5bGVzDQouaGVhZGVyLXNlY3Rpb24gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMTc2QURCOw0KICBwYWRkaW5nOiAxNXB4IDIwcHg7DQp9DQoNCi50aXRsZS1zZWN0aW9uIHsNCiAgZGlzcGxheTogZmxleDsNCiAgaGVpZ2h0OiAzNnB4Ow0KICBmb250LXdlaWdodDogNzAwOw0KICBmb250LXNpemU6IDI0cHg7DQogIGNvbG9yOiAjMzMzMzMzOw0KfQ0KDQoubWFpbi10aXRsZSB7DQogIC8vIEluaGVyaXRzIGZyb20gdGl0bGUtc2VjdGlvbg0KfQ0KDQouaGVscC1zZWN0aW9uIHsNCiAgZGlzcGxheTogZ3JpZDsNCiAganVzdGlmeS1pdGVtczogY2VudGVyOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIGJvdHRvbTogLTMwcHg7DQp9DQoNCi5oZWxwLXRleHQgew0KICBmb250LXNpemU6IDEycHg7DQp9DQoNCi5oZWxwLWltYWdlIHsNCiAgd2lkdGg6IDgwcHg7DQogIGhlaWdodDogMzBweDsNCiAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KfQ0KDQovLyBGaWxlIGxpc3QgY29udGFpbmVyDQouZmlsZS1saXN0LWNvbnRhaW5lciB7DQogIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICNlNmU2ZTY7DQogIGJvcmRlci1sZWZ0OiAxcHggc29saWQgI2U2ZTZlNjsNCiAgcGFkZGluZzogMTBweDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCglnYXA6IDIwcHg7DQoJZmxleDogMTsNCglmbGV4LXdyYXA6IHdyYXA7DQoJZGlzcGxheTogZmxleDsNCglhbGlnbi1pdGVtczogY2VudGVyOw0KCXRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQoJOjp2LWRlZXAgLmVsLWNhcmRfX2JvZHkgew0KCQlwYWRkaW5nOiAwOw0KCX0NCn0NCg0KLmZpbGUtbGlzdC10aXRsZSB7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICBjb2xvcjogIzMzMzsNCn0NCg0KLmZpbGUtaXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KfQ0KDQouZmlsZS1pdGVtLWNvbnRlbnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiA1cHg7DQp9DQoNCi5maWxlLWljb24gew0KICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgY29sb3I6ICM0MDlFRkY7DQp9DQoNCi5maWxlLW5hbWUgew0KICBmb250LXNpemU6IDEycHg7DQogIGZsZXg6IDE7DQogIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsNCn0NCg0KLmRvd25sb2FkLWljb24gew0KICBtYXJnaW4tbGVmdDogOHB4Ow0KICBjb2xvcjogIzk5OTsNCn0NCg0KLy8gQWN0aW9uIGJ1dHRvbnMgc2VjdGlvbg0KLmFjdGlvbi1idXR0b25zIHsNCiAgdGV4dC1hbGlnbjogcmlnaHQ7DQp9DQoNCi5idXR0b24tZ3JvdXAgew0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KCS5pdGVtLWJ1dHRvbnsNCgkJY29sb3I6ICNmZmY7DQoJfQ0KfQ0KDQoucHJpbWFyeS1idG4gew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTc2QURCOw0KICBjb2xvcjogI0ZGRkZGRjsNCiAgYm9yZGVyOiAxcHggc29saWQgIzE3NkFEQjsNCn0NCg0KLy8gQ29udGVudCBzZWN0aW9uIHN0eWxlcw0KLmNvbnRlbnQtc2VjdGlvbiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGhlaWdodDogODIlOw0KfQ0KDQovLyBQREYgcHJldmlldyBhcmVhDQoucGRmLXByZXZpZXctYXJlYSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBmbGV4OiAxOw0KfQ0KDQoucGRmLXBhbmVsIHsNCiAgd2lkdGg6IDQ5JTsNCn0NCg0KLnJlc3BvbnNlLXBhbmVsIHsNCiAgJi5ib3JkZXItcmlnaHQgew0KICAgIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICMxNzZBREI7DQogIH0NCn0NCg0KLnByb2N1cmVtZW50LXBhbmVsIHsNCiAgJi5ib3JkZXItbGVmdCB7DQogICAgYm9yZGVyLWxlZnQ6IDFweCBzb2xpZCAjMTc2QURCOw0KICB9DQp9DQoNCi8vIERpdmlkZXIgc3R5bGVzDQouZGl2aWRlciB7DQogIG1pbi1oZWlnaHQ6IDU3dmg7DQogIHdpZHRoOiAxJTsNCiAgYmFja2dyb3VuZC1jb2xvcjogI0Y1RjVGNTsNCn0NCg0KLy8gU2lkZWJhciBzdHlsZXMNCi5zaWRlYmFyIHsNCiAgbWluLWhlaWdodDogNTd2aDsNCiAgd2lkdGg6IDIwJTsNCn0NCg0KLnNpZGViYXItaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMTc2QURCOw0KICBwYWRkaW5nOiAxNXB4IDIwcHg7DQp9DQoNCi5zdXBwbGllci1zZWxlY3Qgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLnNpZGViYXItY29udGVudCB7DQogIHBhZGRpbmc6IDE1cHggMjBweDsNCn0NCg0KLy8gRmFjdG9yIGl0ZW1zIHN0eWxlcw0KLmZhY3Rvci1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLmZhY3Rvci1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHRleHQtYWxpZ246IGxlZnQ7DQogIHdpZHRoOiA5OCU7DQp9DQoNCi5mYWN0b3ItbmFtZSB7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgZm9udC1mYW1pbHk6IFNvdXJjZUhhblNhbnNTQy1Cb2xkOw0KICBmb250LXdlaWdodDogNTAwOw0KICBmb250LXNpemU6IDE2cHg7DQogIGNvbG9yOiAjMzMzMzMzOw0KICBsZXR0ZXItc3BhY2luZzogMDsNCn0NCg0KLm1heC1zY29yZSB7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6IHJlZDsNCn0NCg0KLmZhY3Rvci1pbnB1dCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIHdpZHRoOiAxMDAlOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOw0KICBwYWRkaW5nOiAxMHB4Ow0KfQ0KDQouc2NvcmUtdmFsdWUgew0KICBjb2xvcjogZ3JlZW47DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLy8gU3VibWl0IHNlY3Rpb24NCi5zdWJtaXQtc2VjdGlvbiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIG1hcmdpbjogMzJweCAwOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWV2ZW5seTsNCn0NCg0KLy8gUmV2aWV3IGNvbnRlbnQgc3R5bGVzDQoucmV2aWV3LWNvbnRlbnQgew0KICB0ZXh0LWFsaWduOiBsZWZ0Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5yZXZpZXctdGl0bGUgew0KICBmb250LWZhbWlseTogU291cmNlSGFuU2Fuc1NDLUJvbGQ7DQogIGZvbnQtd2VpZ2h0OiA3MDA7DQogIGZvbnQtc2l6ZTogMTVweDsNCiAgY29sb3I6ICMxNzZBREI7DQogIGxldHRlci1zcGFjaW5nOiAwOw0KfQ0KDQoucmV2aWV3LXRleHQgew0KICBwYWRkaW5nOiA2cHggMzBweDsNCn0NCg0KLy8gRXhpc3Rpbmcgc3R5bGVzDQouaXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBmb250LXNpemU6IDE4cHg7DQogIG1hcmdpbi1ib3R0b206IDgwcHg7DQogIC5pdGVtLXRpdGxlIHsNCiAgICB3aWR0aDogMTIwcHg7DQogICAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICAgIHRleHQtYWxpZ246IGxlZnQ7DQoJICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KCSAgcGFkZGluZzogNHB4IDhweDsNCgkgIGJvcmRlci1yYWRpdXM6IDRweDsNCgkgIA0KCSAgJjpob3ZlciB7DQoJCSAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjhmZjsNCgkJICBjb2xvcjogIzE3NkFEQjsNCgkJICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMnB4KTsNCgkgIH0NCiAgfQ0KfQ0KLml0ZW0tYnV0dG9uIHsNCiAgYm9yZGVyOiAxcHggc29saWQgIzk3OTc5NzsNCiAgd2lkdGg6IDE1MHB4Ow0KICBoZWlnaHQ6IDM2cHg7DQogIG1hcmdpbjogMCAxMHB4Ow0KICBmb250LXdlaWdodDogNzAwOw0KICBmb250LXNpemU6IDE3cHg7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgY29sb3I6ICMzMzMzMzM7DQogICY6aG92ZXIgew0KICAgIGNvbG9yOiAjMzMzMzMzOw0KICB9DQp9DQoudGVjaG5pY2FsLWJsdWUtYnRuIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzE3NkFEQiAhaW1wb3J0YW50Ow0KICBjb2xvcjogI2ZmZiAhaW1wb3J0YW50Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjMTc2QURCICFpbXBvcnRhbnQ7DQp9DQoudGVjaG5pY2FsLWJsdWUtYnRuLWFjdGl2ZSB7DQogIGJhY2tncm91bmQtY29sb3I6ICNGRjZCMzUgIWltcG9ydGFudDsNCiAgY29sb3I6ICNmZmYgIWltcG9ydGFudDsNCiAgYm9yZGVyOiAxcHggc29saWQgI0ZGNkIzNSAhaW1wb3J0YW50Ow0KICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgyNTUsIDEwNywgNTMsIDAuMykgIWltcG9ydGFudDsNCn0NCi5pdGVtLWJ1dHRvbi1saXR0bGUgew0KICB3aWR0aDogMTI0cHg7DQogIGhlaWdodDogMzZweDsNCiAgZm9udC13ZWlnaHQ6IDcwMDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBjb2xvcjogI2ZmZjsNCiAgJjpob3ZlciB7DQogICAgY29sb3I6ICNmZmY7DQogIH0NCn0NCi5mYWN0b3JzIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBmbGV4LXdyYXA6IHdyYXA7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoudGV4dCB7DQogIDo6di1kZWVwIC5lbC10ZXh0YXJlYV9faW5uZXIgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7DQogICAgYm9yZGVyLXJhZGl1czogMDsNCiAgICBib3JkZXI6IDFweCBzb2xpZCAjZjVmNWY1Ow0KICB9DQp9DQoNCi5maWxlTGlzdCB7DQoJZGlzcGxheTogZmxleDsNCglhbGlnbi1pdGVtczogY2VudGVyOw0KCWdhcDogMjBweDsNCglmbGV4OiAxOw0KCWZsZXgtd3JhcDogd3JhcDsNCiAgJjpob3ZlciB7DQogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOw0KICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjE1KTsNCiAgfQ0KDQogIDo6di1kZWVwIC5lbC1jYXJkX19ib2R5IHsNCiAgICBwYWRkaW5nOiAwOw0KICB9DQp9DQoNCi8vIFBERua4suafk+eKtuaAgeaPkOekuuagt+W8jw0KLnJlbmRlci1zdGF0dXMtdGlwIHsNCglkaXNwbGF5OiBmbGV4Ow0KCWFsaWduLWl0ZW1zOiBjZW50ZXI7DQoJcGFkZGluZzogMTBweCAxNXB4Ow0KCW1hcmdpbi1ib3R0b206IDE1cHg7DQoJYm9yZGVyLXJhZGl1czogNHB4Ow0KCWJhY2tncm91bmQtY29sb3I6ICNmZmY3ZTY7DQoJYm9yZGVyOiAxcHggc29saWQgI2ZmZDU5MTsNCgljb2xvcjogI2Q0ODgwNjsNCglmb250LXNpemU6IDE0cHg7DQoJDQoJaSB7DQoJCW1hcmdpbi1yaWdodDogOHB4Ow0KCQlmb250LXNpemU6IDE2cHg7DQoJfQ0KCQ0KCSYuc3VjY2VzcyB7DQoJCWJhY2tncm91bmQtY29sb3I6ICNmNmZmZWQ7DQoJCWJvcmRlci1jb2xvcjogI2I3ZWI4ZjsNCgkJY29sb3I6ICM1MmM0MWE7DQoJfQ0KfQ0KDQovLyDnpoHnlKjnirbmgIHnmoTor4TliIbpobnmoIfpopjmoLflvI8NCi5mYWN0b3ItdGl0bGUuZGlzYWJsZWQgew0KCWNvbG9yOiAjOTk5ICFpbXBvcnRhbnQ7DQoJY3Vyc29yOiBub3QtYWxsb3dlZCAhaW1wb3J0YW50Ow0KCW9wYWNpdHk6IDAuNjsNCgkNCgkmOmhvdmVyIHsNCgkJY29sb3I6ICM5OTkgIWltcG9ydGFudDsNCgl9DQp9DQoNCi8vIOaCrOa1ruahhuagt+W8jw0KLmZhY3Rvci10b29sdGlwIHsNCglwb3NpdGlvbjogYWJzb2x1dGU7DQoJcmlnaHQ6IDEwMCU7IC8qIOaYvuekuuWcqOeItuWFg+e0oOW3puS+pyAqLw0KCXRvcDogMDsNCgltYXJnaW4tcmlnaHQ6IDEwcHg7IC8qIOS4juivhOWIhumhueeahOmXtOi3nSAqLw0KCWJhY2tncm91bmQ6ICNmZmY7DQoJYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsNCglib3JkZXItcmFkaXVzOiA4cHg7DQoJYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpOw0KCXdpZHRoOiA0MDBweDsNCgltYXgtaGVpZ2h0OiAzMDBweDsNCglvdmVyZmxvdzogaGlkZGVuOw0KCXotaW5kZXg6IDk5OTk7DQoJDQoJLnRvb2x0aXAtaGVhZGVyIHsNCgkJZGlzcGxheTogZmxleDsNCgkJanVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KCQlhbGlnbi1pdGVtczogY2VudGVyOw0KCQlwYWRkaW5nOiAxMnB4IDE2cHg7DQoJCWJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQoJCWJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOw0KCQkNCgkJLnRvb2x0aXAtdGl0bGUgew0KCQkJZm9udC13ZWlnaHQ6IDYwMDsNCgkJCWZvbnQtc2l6ZTogMTRweDsNCgkJCWNvbG9yOiAjMTc2QURCOw0KCQl9DQoJCQ0KCQkudG9vbHRpcC1jbG9zZSB7DQoJCQljdXJzb3I6IHBvaW50ZXI7DQoJCQljb2xvcjogIzkwOTM5OTsNCgkJCWZvbnQtc2l6ZTogMTRweDsNCgkJCQ0KCQkJJjpob3ZlciB7DQoJCQkJY29sb3I6ICMxNzZBREI7DQoJCQl9DQoJCX0NCgl9DQoJDQoJLnRvb2x0aXAtY29udGVudCB7DQoJCXBhZGRpbmc6IDE2cHg7DQoJCWZvbnQtc2l6ZTogMTRweDsNCgkJbGluZS1oZWlnaHQ6IDEuNjsNCgkJY29sb3I6ICMzMzM7DQoJCW1heC1oZWlnaHQ6IDI0MHB4Ow0KCQlvdmVyZmxvdy15OiBhdXRvOw0KCQkNCgkJLy8g576O5YyW5rua5Yqo5p2hDQoJCSY6Oi13ZWJraXQtc2Nyb2xsYmFyIHsNCgkJCXdpZHRoOiA2cHg7DQoJCX0NCgkJDQoJCSY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsNCgkJCWJhY2tncm91bmQ6ICNmMWYxZjE7DQoJCQlib3JkZXItcmFkaXVzOiAzcHg7DQoJCX0NCgkJDQoJCSY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsNCgkJCWJhY2tncm91bmQ6ICNjMWMxYzE7DQoJCQlib3JkZXItcmFkaXVzOiAzcHg7DQoJCQkNCgkJCSY6aG92ZXIgew0KCQkJCWJhY2tncm91bmQ6ICNhOGE4YTg7DQoJCQl9DQoJCX0NCgl9DQp9DQoNCi8vIOivhOWIhumhueWuueWZqOebuOWvueWumuS9jQ0KLmZhY3Rvci1pdGVtIHsNCglwb3NpdGlvbjogcmVsYXRpdmU7DQp9DQo="}, {"version": 3, "sources": ["one.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk+BA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "one.vue", "sourceRoot": "src/views/expertReview/technical", "sourcesContent": ["<template>\r\n  <div class=\"technical-review-container\">\r\n    <div class=\"main-content\">\r\n      <div class=\"header-section\">\r\n        <div class=\"title-section\">\r\n          <div class=\"main-title\">技术标评审</div>\r\n          <div class=\"help-section\">\r\n            <div class=\"help-text\">该页面操作说明</div>\r\n            <el-image class=\"help-image\" :src=\"srcList[0]\" :preview-src-list=\"srcList\">\r\n            </el-image>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 文件列表 -->\r\n        <div class=\"file-list-container\">\r\n          <div class=\"file-list-title\">响应文件附件下载</div>\r\n          <el-card\r\n            v-for=\"(item, index) in attachmentsList\"\r\n            :key=\"index\"\r\n            class=\"file-item\"\r\n            shadow=\"hover\"\r\n            @click.native=\"downloadFile(item)\"\r\n          >\r\n            <div class=\"file-item-content\">\r\n              <i class=\"el-icon-document file-icon\"></i>\r\n              <span class=\"file-name\">{{ item.fileName }}</span>\r\n              <i class=\"el-icon-download download-icon\"></i>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <div class=\"action-buttons\">\r\n          <el-button class=\"item-button\" @click=\"bidInquiry\">询标</el-button>\r\n          <el-button class=\"item-button\" v-if=\"expertInfo.expertLeader==1\" @click=\"secondOffer\">发起二次报价</el-button>\r\n          <div class=\"button-group\">\r\n\t          <el-button\r\n\t\t          :class=\"['item-button', activeButton === 'procurement' ? 'technical-blue-btn-active' : 'technical-blue-btn']\"\r\n\t\t          @click=\"viewPurchasing\">采购文件</el-button>\r\n\t          \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'response' ? 'technical-blue-btn-active' : 'technical-blue-btn']\"\r\n              @click=\"showResponseFile()\">响应文件</el-button>\r\n            \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'contrast' ? 'technical-blue-btn-active' : 'technical-blue-btn']\"\r\n              @click=\"fileContrast\">对比</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"content-section\">\r\n        \r\n\r\n        <!-- PDF预览区域 - 保持原始尺寸 -->\r\n        <div class=\"pdf-preview-area\">\r\n          <div\r\n            v-show=\"procurementShow\"\r\n            class=\"pdf-panel procurement-panel\"\r\n            :class=\"{ 'border-left': double }\"\r\n          >\r\n<!--            <pdfView-->\r\n<!--              ref=\"procurement\"-->\r\n<!--              :pdfurl=\"procurementPdf\"-->\r\n<!--              :uni_key=\"'procurement'\"-->\r\n<!--            ></pdfView>-->\r\n\t          \r\n\t          <PdfViewImproved ref=\"procurement\"  :pdfurl=\"procurementPdf\"  :page-height=\"800\" :buffer-size=\"2\" @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"/>\r\n          </div>\r\n\t        \r\n\t        <div\r\n\t\t        v-show=\"responseShow\"\r\n\t\t        class=\"pdf-panel response-panel\"\r\n\t\t        :class=\"{ 'border-right': double }\"\r\n\t        >\r\n\t\t        <!--            <pdfView-->\r\n\t\t        <!--              ref=\"response\"-->\r\n\t\t        <!--              :pdfurl=\"responsePdf\"-->\r\n\t\t        <!--              :uni_key=\"'response'\"-->\r\n\t\t        <!--            ></pdfView>-->\r\n\t\t        \r\n\t\t        <PdfViewImproved ref=\"response\"  :pdfurl=\"responsePdf\"  :page-height=\"800\" :buffer-size=\"2\" @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"/>\r\n\t        </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"divider\">\r\n    </div>\r\n    <div class=\"sidebar\">\r\n      <div class=\"sidebar-header\">\r\n        <el-select\r\n          class=\"supplier-select\"\r\n          v-model=\"supplier\"\r\n          placeholder=\"请选择供应商\"\r\n          @change=\"handleChange\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in options\"\r\n            :key=\"item.bidderName\"\r\n            :label=\"item.bidderName\"\r\n            :value=\"item.bidderName\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </div>\r\n\t    \r\n      <div class=\"sidebar-content\" >\r\n        <!-- 响应文件评分项显示 -->\r\n\t      <template v-if=\"responseShow || double\">\r\n\t\t      <!-- PDF渲染状态提示 -->\r\n\t\t      <div v-if=\"!responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t\t      <i class=\"el-icon-loading\"></i>\r\n\t\t\t      <span>响应文件正在渲染中，请稍候...</span>\r\n\t\t      </div>\r\n\t\t      <div v-else class=\"render-status-tip success\">\r\n\t\t\t      <i class=\"el-icon-success\"></i>\r\n\t\t\t      <span>响应文件渲染完成，可以点击跳转</span>\r\n\t\t      </div>\r\n\t\t      \r\n\t\t      <div\r\n\t\t\t      v-for=\"(item, index) in scoringSystem.uitems\"\r\n\t\t\t      :key=\"'response-' + index\"\r\n\t\t\t      class=\"factor-item\"\r\n\t\t\t      @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t      @mouseleave=\"hideFactorTooltip\"\r\n\t\t      >\r\n\t\t\t      <!-- 悬浮框 -->\r\n\t\t\t      <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t           class=\"factor-tooltip\"\r\n\t\t\t           @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t      <div class=\"tooltip-header\">\r\n\t\t\t\t\t      <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t      <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t      </div>\r\n\t\t\t\t      <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t      </div>\r\n\t\t\t      <div>\r\n\t\t\t\t      <div class=\"factors\">\r\n\t\t\t\t\t      <div class=\"factor-header\">\r\n\t\t\t\t\t\t      <div class=\"factor-name factor-title\"\r\n\t\t\t\t\t\t           :class=\"{ 'disabled': !canJumpToPage() }\"\r\n\t\t\t\t\t\t           @click=\"showInfo(item)\">\r\n\t\t\t\t\t\t\t      {{ item.itemName }}\r\n\t\t\t\t\t\t\t      <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t      \r\n\t\t\t\t\t\t\t      <span class=\"max-score\">\r\n\t\t\t\t\t\t\t        最高{{ getMaxScore(item) }}分\r\n\t\t\t\t\t\t\t      </span>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t\t      <div class=\"factor-input\">\r\n\t\t\t\t\t\t      <div v-if=\"!(item.scoreLevel.length == 0 || item.scoreLevel == null || item.scoreLevel == undefined)\">\r\n\t\t\t\t\t\t\t      <el-radio\r\n\t\t\t\t\t\t\t\t      v-for=\"(score,index) in item.scoreLevel.split(',')\"\r\n\t\t\t\t\t\t\t\t      :key=\"index\"\r\n\t\t\t\t\t\t\t\t      v-model=\"defaultRatingArray[item.entMethodItemId].state\"\r\n\t\t\t\t\t\t\t\t      :label=\"score\"\r\n\t\t\t\t\t\t\t      ><span class=\"score-value\">{{ score }}</span></el-radio>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t\t      <div v-else>\r\n\t\t\t\t\t\t\t      <el-input\r\n\t\t\t\t\t\t\t\t      placeholder=\"请输入分数\"\r\n\t\t\t\t\t\t\t\t      v-model=\"defaultRatingArray[item.entMethodItemId].state\"\r\n\t\t\t\t\t\t\t\t      @input=\"handleScoreInput(item.entMethodItemId, $event)\"\r\n\t\t\t\t\t\t\t\t      @keypress=\"onlyNumber\"\r\n\t\t\t\t\t\t\t\t      type=\"number\"\r\n\t\t\t\t\t\t\t\t      step=\"1\"\r\n\t\t\t\t\t\t\t\t      min=\"0\"\r\n\t\t\t\t\t\t\t      ></el-input>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t      \r\n\t\t\t\t      </div>\r\n\t\t\t      </div>\r\n\t\t      </div>\r\n\t      </template>\r\n\t      \r\n\t      <template v-else-if=\"procurementShow\" >\r\n\t\t      \r\n\t\t      <!-- PDF渲染状态提示 -->\r\n          <div v-if=\"!procurementPdfRendered\" class=\"render-status-tip\">\r\n            <i class=\"el-icon-loading\"></i>\r\n            <span>采购文件正在渲染中，请稍候...</span>\r\n          </div>\r\n          <div v-else class=\"render-status-tip success\">\r\n            <i class=\"el-icon-success\"></i>\r\n            <span>采购文件渲染完成，可以点击跳转</span>\r\n          </div>\r\n\t\t      \r\n\t\t      <!-- 采购文件评分项显示 -->\r\n\t\t      <div\r\n\t\t\t      v-for=\"(item, index) in pageProcurement\"\r\n\t\t\t      :key=\"'procurement-' + index\"\r\n\t\t\t      class=\"factor-item\"\r\n\t\t\t      @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t      @mouseleave=\"hideFactorTooltip\"\r\n\t\t      >\r\n\t\t\t      <!-- 悬浮框 -->\r\n\t\t\t      <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t           class=\"factor-tooltip\"\r\n\t\t\t           @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t      <div class=\"tooltip-header\">\r\n\t\t\t\t\t      <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t      <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t      </div>\r\n\t\t\t\t      <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t      </div>\r\n\t\t\t      <div>\r\n\t\t\t\t      <div class=\"factors\">\r\n\t\t\t\t\t      <div class=\"factor-header\">\r\n\t\t\t\t\t\t      <div class=\"factor-name factor-title\"\r\n\t\t\t\t\t\t           :class=\"{ 'disabled': !canJumpToPage() }\"\r\n\t\t\t\t\t\t           @click=\"jumpToProcurementPage(item)\">\r\n\t\t\t\t\t\t\t      {{ item.itemName }}\r\n\t\t\t\t\t\t\t      \r\n\t\t\t\t\t\t\t      <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t      </div>\r\n\t\t\t      </div>\r\n\t\t      </div>\r\n\t      </template>\r\n\t      \r\n        <div class=\"submit-section\">\r\n          <!-- <div><el-button\r\n              class=\"item-button-little\"\r\n              style=\"background-color:#F5F5F5;color:#176ADB\"\r\n              @click=\"save\"\r\n            >保存</el-button></div> -->\r\n          <div><el-button\r\n              class=\"item-button-little primary-btn\"\r\n              @click=\"submit\"\r\n            >提交</el-button></div>\r\n        </div>\r\n\r\n        <div class=\"review-content\">\r\n          <div class=\"review-title\">评审内容：</div>\r\n          <div class=\"review-text\" v-html=\"selectNode.itemRemark\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  supplierInfo,\r\n  approvalProcess,\r\n  scoringFactors,\r\n  checkReviewSummary,\r\n  filesById,\r\n} from \"@/api/expert/review\";\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\";\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      options: [],\r\n      scoringSystem: [],\r\n      selectNode: {},\r\n      supplier: \"\",\r\n      selectSupplier: {},\r\n      expertInfo: {},\r\n      defaultRatingArray: {},\r\n      file: {},\r\n      responseShow: false,\r\n      procurementShow: false,\r\n      double: false,\r\n      factorList: [],\r\n      entDocResponsePage: {},\r\n      factorsPage: {},\r\n      bidderFactor: {},\r\n\r\n      responsePdf: null,\r\n      procurementPdf: null,\r\n      currentMaxScore: null,\r\n\r\n      // 按钮状态管理\r\n      activeButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n      // 采购文件相关数据\r\n      entDocProcurementPage: {}, // 采购文件页码信息\r\n      pageProcurement: [], // 采购文件的评分项\r\n\t    attachmentsList:[], // 文件列表\r\n\t    \r\n\t    // PDF渲染状态管理\r\n\t    responsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n\t    procurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n      srcList: [\"/evalution/help.jpg\"],\r\n\t    \r\n\t    // 悬停状态管理\r\n\t    hoveredFactorNode: null, // 悬停时的评分项\r\n\t    tooltipTimer: null, // 悬浮框显示定时器\r\n    };\r\n  },\r\n  methods: {\r\n    /**\r\n     * 限制输入框只能输入数字和小数点\r\n     * @param {Event} event - 键盘事件\r\n     */\r\n    onlyNumber(event) {\r\n      // 获取按键的字符码\r\n      const charCode = event.which || event.keyCode;\r\n\r\n      // 允许的字符：数字(48-57)、小数点(46)、退格(8)、删除(46)、Tab(9)、Enter(13)、方向键(37-40)\r\n      if (\r\n        (charCode >= 48 && charCode <= 57) || // 数字 0-9\r\n        charCode === 46 || // 小数点\r\n        charCode === 8 ||  // 退格键\r\n        charCode === 9 ||  // Tab键\r\n        charCode === 13 || // Enter键\r\n        (charCode >= 37 && charCode <= 40) // 方向键\r\n      ) {\r\n        return true;\r\n      }\r\n\r\n      // 阻止其他字符输入\r\n      event.preventDefault();\r\n      return false;\r\n    },\r\n\r\n    /**\r\n     * 处理分数输入，确保只能输入有效的数字\r\n     * @param {string} itemId - 评估项ID\r\n     * @param {string} value - 输入值\r\n     */\r\n    handleScoreInput(itemId, value) {\r\n      // 移除非数字字符（保留小数点）\r\n      let cleanValue = value.replace(/[^\\d.]/g, '');\r\n\r\n      // 确保只有一个小数点\r\n      const parts = cleanValue.split('.');\r\n      if (parts.length > 2) {\r\n        cleanValue = parts[0] + '.' + parts.slice(1).join('');\r\n      }\r\n\r\n      // 限制小数点后最多2位\r\n      if (parts.length === 2 && parts[1].length > 2) {\r\n        cleanValue = parts[0] + '.' + parts[1].substring(0, 2);\r\n      }\r\n\r\n      // 更新值\r\n      this.defaultRatingArray[itemId].state = cleanValue;\r\n\r\n      // 调用原有的验证方法\r\n      this.validateScore(itemId, cleanValue);\r\n    },\r\n\r\n    init() {\r\n      const expertInfo = JSON.parse(localStorage.getItem(\"expertInfo\"));\r\n      this.entDocResponsePage = JSON.parse(\r\n        localStorage.getItem(\"entDocResponsePage\")\r\n      );\r\n      // 初始化采购文件页码信息\r\n      this.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\"));\r\n      supplierInfo({ projectId: this.$route.query.projectId }).then(\r\n        (response) => {\r\n          if (response.code == 200) {\r\n            this.options = response.rows.filter(item => item.isAbandonedBid == 0);\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        }\r\n      );\r\n      approvalProcess(this.$route.query.projectId, expertInfo.resultId).then(\r\n        (response) => {\r\n          if (response.code == 200) {\r\n\t\t\t\t\t\t// 文件列表\r\n\t          this.attachmentsList = response.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n\t\t\t\t\t\t\r\n            this.scoringSystem =\r\n              response.data.scoringMethodUinfo.scoringMethodItems.find(\r\n                (item) => {\r\n                  return (\r\n                    item.scoringMethodItemId ==\r\n                    this.$route.query.scoringMethodItemId\r\n                  );\r\n                }\r\n              );\r\n            localStorage.setItem(\r\n              \"evalProjectEvaluationProcess\",\r\n              JSON.stringify(this.scoringSystem.evalProjectEvaluationProcess)\r\n            );\r\n            // TODO 首先生成长度为this.scoringSystem.length的数组，结构为{state：false，reason：“”}\r\n            this.defaultRatingArray = this.scoringSystem.uitems.reduce(\r\n              (acc, _, index) => {\r\n                acc[this.scoringSystem.uitems[index].entMethodItemId] = {\r\n                  state: null,\r\n                  reason: \"\",\r\n                };\r\n                console.log(\"scoringSystem\", this.scoringSystem);\r\n                return acc;\r\n              },\r\n              {}\r\n            );\r\n          } else {\r\n            this.$messgae.warning(response.msg);\r\n          }\r\n        }\r\n      );\r\n      filesById(this.$route.query.projectId).then((response) => {\r\n        if (response.code == 200) {\r\n          this.file = response.data;\r\n          if (this.file.tenderNoticeFilePath != undefined) {\r\n            this.procurementPdf = this.file.tenderNoticeFilePath;\r\n          }\r\n          // if (this.file.file != undefined) {\r\n          //   this.responsePdf = this.file.file[0];\r\n          // }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      // 初始化专家信息\r\n      this.initExpertInfo();\r\n    },\r\n\r\n    /**\r\n     * 初始化专家信息\r\n     */\r\n    initExpertInfo() {\r\n      try {\r\n        const itemString = localStorage.getItem(\"expertInfo\");\r\n        if (itemString) {\r\n          this.expertInfo = JSON.parse(itemString);\r\n          console.log(\"专家信息已初始化\", this.expertInfo);\r\n        } else {\r\n          console.warn(\"localStorage中未找到expertInfo\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"初始化专家信息失败:\", error);\r\n      }\r\n    },\r\n\r\n    handleChange(value) {\r\n      if(Object.keys(this.selectSupplier).length != 0){\r\n        this.tmpSave();\r\n      }\r\n      this.selectSupplier = this.options.find((item) => {\r\n        return item.bidderName == value;\r\n      });\r\n\r\n      // 根据bidderid获取供应商因素及其对应页码\r\n      this.bidderFactor = this.factorsPage[this.selectSupplier.bidderId];\r\n\r\n      const data = {\r\n        expertResultId: this.expertInfo.resultId,\r\n        projectId: this.$route.query.projectId,\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n      };\r\n      getDetailByPsxx(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.factorList = response.data;\r\n          const factor = this.factorList.find((item) => {\r\n            return item.bidderName == value;\r\n          }).evalExpertEvaluationDetails;\r\n          if (factor != null) {\r\n            factor.map((item) => {\r\n              this.defaultRatingArray[item.scoringMethodUitemId].reason =\r\n                item.evaluationRemark;\r\n              this.defaultRatingArray[item.scoringMethodUitemId].state =\r\n                item.evaluationResult;\r\n            });\r\n          } else {\r\n            Object.keys(this.defaultRatingArray).forEach((key) => {\r\n              this.defaultRatingArray[key].state = null;\r\n              this.defaultRatingArray[key].reason = \"\";\r\n            });\r\n          }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      this.showResponseFile();\r\n      // 重置最大分数值\r\n      // this.currentMaxScore = null;\r\n    },\r\n\t  \r\n\t  \r\n    validateScore(itemId, event) {\r\n      const inputValue = parseFloat(event);\r\n      console.log(\"inputValue\", inputValue);\r\n      \r\n      // 获取当前评分项的最大分值\r\n      const currentItem = this.scoringSystem.uitems.find(item => item.entMethodItemId === itemId);\r\n      let maxScore = null;\r\n      \r\n      if (currentItem) {\r\n        // 如果有分数挡位，使用挡位中的最大值\r\n        if (currentItem.scoreLevel && currentItem.scoreLevel.length > 0) {\r\n          const scoreLevels = currentItem.scoreLevel.split(',').map(item => parseFloat(item.trim())).filter(item => !isNaN(item));\r\n          if (scoreLevels.length > 0) {\r\n            maxScore = Math.max(...scoreLevels);\r\n          }\r\n        } else {\r\n          // 否则使用配置的最大分值\r\n          maxScore = parseFloat(currentItem.score);\r\n        }\r\n      }\r\n      \r\n      console.log(\"maxScore\", maxScore);\r\n\r\n      if (!isNaN(inputValue) && maxScore !== null) {\r\n        if (inputValue > maxScore) {\r\n          this.$message.warning(`输入分数不能超过${maxScore}分，请重新输入`);\r\n          // 将输入值限制为最大分数值\r\n          this.defaultRatingArray[itemId].state = \"\";\r\n        } else if (inputValue < 0) {\r\n          this.$message.warning(\"输入分数不能小于0分\");\r\n          this.defaultRatingArray[itemId].state = \"\";\r\n        }\r\n      }\r\n    },\r\n    showResponseFile() {\r\n      if (Object.keys(this.selectSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        this.activeButton = 'response'; // 设置当前激活按钮\r\n        this.double = false;\r\n        this.procurementShow = false;\r\n        this.responseShow = true;\r\n        this.responsePdf = this.file.file[this.selectSupplier.bidderId];\r\n      }\r\n    },\r\n    fileContrast() {\r\n      if (Object.keys(this.selectSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        this.activeButton = 'contrast'; // 设置当前激活按钮\r\n        this.double = true;\r\n        this.procurementShow = true;\r\n        this.responseShow = true;\r\n        this.responsePdf = this.file.file[this.selectSupplier.bidderId];\r\n      }\r\n    },\r\n    showInfo(item) {\r\n\t    // 检查PDF是否渲染完成\r\n\t    if (!this.canJumpToPage()) {\r\n\t\t    this.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t    return;\r\n\t    }\r\n\t\t\t\r\n      this.selectNode = item;\r\n\r\n      // 如果只显示采购文件，使用采购文件页码信息\r\n      if (this.procurementShow && !this.responseShow) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        if (item.jumpToPage) {\r\n          this.$refs.procurement.skipPage(item.jumpToPage);\r\n        } else if (this.entDocProcurementPage && this.entDocProcurementPage[item.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[item.itemName]);\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 如果显示响应文件或对比模式，需要选择供应商\r\n      if (Object.keys(this.bidderFactor).length != 0) {\r\n        // 跳转到响应文件对应页码\r\n        if (this.responseShow && this.$refs.response) {\r\n\t        if (!this.responsePdfRendered) {\r\n\t\t        this.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t        return;\r\n\t        }\r\n          this.$refs.response.skipPage(\r\n            this.bidderFactor[this.selectNode.itemName]\r\n          );\r\n        }\r\n\r\n        // 跳转到采购文件对应页码\r\n        if (this.procurementShow && this.$refs.procurement) {\r\n\t        if (!this.procurementPdfRendered) {\r\n\t\t        this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t        return;\r\n\t        }\r\n\t\t\t\t\t\r\n          // 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n          if (this.entDocProcurementPage && this.entDocProcurementPage[item.itemName]) {\r\n            this.$refs.procurement.skipPage(this.entDocProcurementPage[item.itemName]);\r\n          } else {\r\n            // 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n            // 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n          }\r\n        }\r\n\r\n        // 获取当前项目的最大分数值，假设scoreLevel中第一个值为最大值（可根据实际规则调整）\r\n        // const maxScore = item.score;\r\n        // console.log(\"此项目最大分值是：\"+maxScore);\r\n        // this.currentMaxScore = maxScore; // 将最大分数值存储到实例变量中，方便后续校验使用\r\n      } else {\r\n        this.$message.warning(\"请先选择供应商\");\r\n      }\r\n    },\r\n    initDefaultRatingArray(){\r\n      Object.keys(this.defaultRatingArray).forEach((key) => {\r\n        this.defaultRatingArray[key].state = null;\r\n        this.defaultRatingArray[key].reason = \"\";\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 校验所有评分项是否填写完整\r\n     * @returns {boolean} 是否全部填写\r\n     */\r\n    validateAllRatings() {\r\n      for (const item of this.scoringSystem.uitems) {\r\n        const state = this.defaultRatingArray[item.entMethodItemId].state;\r\n\r\n        // 评分结果未填写\r\n        if (state === null || state === '' || state === undefined) {\r\n          // this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n          return true;\r\n        }\r\n\r\n        // 对于分数评分，检查是否为有效数值\r\n        if (!item.scoreLevel || item.scoreLevel.length === 0 || item.scoreLevel === null || item.scoreLevel === undefined) {\r\n          const score = parseFloat(state);\r\n          if (isNaN(score) || score < 0) {\r\n            this.$message.warning(`${item.itemName}的评分必须是有效的数值且不能小于0`);\r\n            return false;\r\n          }\r\n          // 检查分数是否超过最大值\r\n          const maxScore = this.getMaxScore(item);\r\n          if (score > maxScore) {\r\n            this.$message.warning(`${item.itemName}的评分不能超过${maxScore}分`);\r\n            return false;\r\n          }\r\n        } else {\r\n          // 对于有挡位的评分，检查是否在允许的挡位范围内\r\n          const scoreLevels = item.scoreLevel.split(',').map(level => level.trim());\r\n          if (!scoreLevels.includes(state.toString())) {\r\n            this.$message.warning(`${item.itemName}的评分必须选择指定的挡位：${item.scoreLevel}`);\r\n            return false;\r\n          }\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n\r\n    tmpSave(){\r\n      console.log(\"-------开始保存评审结果----------------\");\r\n\r\n      // 先校验所有评分项是否填写完整\r\n      if (!this.validateAllRatings()) {\r\n        return Promise.resolve({ code: 0, success: false }); // 校验失败\r\n      }\r\n\r\n      var ratingArray = JSON.parse(JSON.stringify(this.defaultRatingArray));\r\n\r\n      var data = [];\r\n      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        const item = this.scoringSystem.uitems[index];\r\n        const itemId = item.entMethodItemId;\r\n        // 获取当前项对应的评分结果\r\n        const evaluationResult = ratingArray[itemId].state;\r\n        if (evaluationResult === null || evaluationResult === \"\") {\r\n          // 如果评分结果为空，则不保存此条信息\r\n          console.log(\"-------评分结果为空，不保存此条信息----------------\");\r\n          continue;\r\n        }\r\n          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n          const evaluationRemark = ratingArray[itemId].reason || \"\";\r\n          data.push({\r\n            scoringMethodUitemId: itemId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            entId: this.selectSupplier.bidderId,\r\n            evaluationResult: evaluationResult,\r\n            evaluationRemark: evaluationRemark\r\n          });\r\n        }\r\n        if(data.length>0){\r\n            console.log(\"-------开始后台保存评审结果----------------\");\r\n            return scoringFactors(data).then((response) => {\r\n              console.log(response.msg);\r\n              if (response.code == 200) {\r\n                this.$message.success(\"保存成功\");\r\n                return { code: 200, success: true };\r\n              } else {\r\n                this.$message.warning(response.msg);\r\n                return { code: response.code, success: false };\r\n              }\r\n            }).catch((error) => {\r\n              this.$message.error(\"保存失败\");\r\n              return { code: 0, success: false };\r\n            });\r\n        }else{\r\n          return Promise.resolve({ code: 200, success: true }); // 没有数据需要保存时也返回成功\r\n        }\r\n    },\r\n    save() {\r\n      if (this.supplier == \"\") {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        //const data = this.generatingSavedData();\r\n        var data = [];\r\n        for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n          const item = this.scoringSystem.uitems[index];\r\n          const itemId = item.entMethodItemId;\r\n          // 获取当前项对应的评分结果\r\n          const evaluationResult = this.defaultRatingArray[itemId].state;\r\n          if (evaluationResult === null || evaluationResult === \"\") {\r\n            // 如果评分结果为空，弹出提示，提示内容包含该项的itemName\r\n            this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n            return; // 直接返回，不再继续构建数据，等待用户填写完整\r\n          }\r\n          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n          const evaluationRemark = this.defaultRatingArray[itemId].reason || \"\";\r\n          data.push({\r\n            scoringMethodUitemId: itemId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            entId: this.selectSupplier.bidderId,\r\n            evaluationResult: evaluationResult,\r\n            evaluationRemark: evaluationRemark\r\n          });\r\n        }\r\n\r\n        scoringFactors(data).then((response) => {\r\n          if (response.code == 200) {\r\n            this.$message.success(response.msg);\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 生成保存数据\r\n    generatingSavedData() {\r\n      var data = [];\r\n      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        const item = this.scoringSystem.uitems[index];\r\n        const itemId = item.entMethodItemId;\r\n        // 获取当前项对应的评分结果\r\n        const evaluationResult = this.defaultRatingArray[itemId].state;\r\n        if (evaluationResult === null || evaluationResult === \"\") {\r\n          // 如果评分结果为空，弹出提示，提示内容包含该项的itemName\r\n          this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n          return; // 直接返回，不再继续构建数据，等待用户填写完整\r\n        }\r\n        // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n        const evaluationRemark = this.defaultRatingArray[itemId].reason || \"\";\r\n        data.push({\r\n          scoringMethodUitemId: itemId,\r\n          expertResultId: this.expertInfo.resultId,\r\n          entId: this.selectSupplier.bidderId,\r\n          evaluationResult: evaluationResult,\r\n          evaluationRemark: evaluationRemark\r\n        });\r\n      }\r\n\r\n      /*for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        data.push({\r\n          scoringMethodUitemId:\r\n            this.scoringSystem.uitems[index].entMethodItemId,\r\n          expertResultId: this.expertInfo.resultId,\r\n          entId: this.selectSupplier.bidderId,\r\n          evaluationResult:\r\n            this.defaultRatingArray[\r\n              this.scoringSystem.uitems[index].entMethodItemId\r\n            ].state,\r\n          evaluationRemark:\r\n            this.defaultRatingArray[\r\n              this.scoringSystem.uitems[index].entMethodItemId\r\n            ].reason,\r\n        });\r\n      }*/\r\n      return data;\r\n    },\r\n    submit() {\r\n        this.tmpSave().then((saveResult) => {\r\n          // 检查保存结果，如果校验失败则不继续提交\r\n          if (!saveResult || saveResult.success === false) {\r\n            return; // 校验失败，不继续提交流程\r\n          }\r\n\r\n          const data = {\r\n            projectId: this.$route.query.projectId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n          };\r\n          checkReviewSummary(data).then((response) => {\r\n            if (response.code == 200) {\r\n            // 修改专家进度\r\n            const status = {\r\n              evalExpertScoreInfoId: JSON.parse(\r\n                localStorage.getItem(\"evalExpertScoreInfo\")\r\n              ).evalExpertScoreInfoId,\r\n              evalState: 1,\r\n            };\r\n            editEvalExpertScoreInfo(status).then((res) => {\r\n              if (res.code == 200) {\r\n                this.$message.success(\"提交成功\");\r\n              }\r\n            });\r\n            this.$emit(\"send\", \"two\");\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      })\r\n    },\r\n    /**\r\n     * 显示采购文件PDF\r\n     */\r\n    viewPurchasing() {\r\n      this.activeButton = 'procurement'; // 设置当前激活按钮\r\n      this.double = false; // 单文件模式\r\n      this.responseShow = false; // 不显示响应文件\r\n      this.procurementShow = true; // 显示采购文件\r\n\r\n      // 右侧评分项显示为采购文件的评分项\r\n      let pageProcurementArr = []; // 采购文件评分项数组\r\n      for (let item in this.entDocProcurementPage){\r\n        pageProcurementArr.push({\r\n          itemName: item,\r\n          jumpToPage: this.entDocProcurementPage[item]\r\n        })\r\n      }\r\n\r\n      console.log(this.scoringSystem.uitems);\r\n      console.log(pageProcurementArr)\r\n      this.pageProcurement = [];\r\n      for (let i = 0; i < this.scoringSystem.uitems.length;i++){\r\n        for (let j = 0; j < pageProcurementArr.length;j++){\r\n          if (this.scoringSystem.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n            this.pageProcurement.push({...this.scoringSystem.uitems[i],...pageProcurementArr[j]});\r\n          }\r\n        }\r\n      }\r\n      console.log(this.pageProcurement)\r\n    },\r\n\r\n    /**\r\n     * 跳转到采购文件对应页码\r\n     * @param {Object} item - 评分项对象\r\n     */\r\n    jumpToProcurementPage(item) {\r\n      if (item.jumpToPage && this.$refs.procurement) {\r\n        this.$refs.procurement.skipPage(item.jumpToPage);\r\n      }\r\n    },\r\n\t  \r\n\t  /**\r\n\t   * 检查是否可以跳转页面\r\n\t   * @returns {boolean} 是否可以跳转\r\n\t   */\r\n\t  canJumpToPage() {\r\n\t\t  // 如果只显示采购文件\r\n\t\t  if (this.procurementShow && !this.responseShow) {\r\n\t\t\t  return this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  // 如果只显示响应文件\r\n\t\t  if (this.responseShow && !this.procurementShow) {\r\n\t\t\t  return this.responsePdfRendered;\r\n\t\t  }\r\n\t\t  // 如果对比模式（两个都显示）\r\n\t\t  if (this.responseShow && this.procurementShow) {\r\n\t\t\t  return this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  return false;\r\n\t  },\r\n\t  /**\r\n\t   * 处理PDF渲染状态变化\r\n\t   * @param {boolean} isRendered 是否渲染完成\r\n\t   * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t   */\r\n\t  handlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t\t  if (pdfType === 'response') {\r\n\t\t\t  this.responsePdfRendered = isRendered;\r\n\t\t  } else if (pdfType === 'procurement') {\r\n\t\t\t  this.procurementPdfRendered = isRendered;\r\n\t\t  }\r\n\t\t  \r\n\t\t  if (isRendered) {\r\n\t\t\t  console.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t\t  }\r\n\t  },\r\n\t  \r\n    // 跳转到二次报价\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query: query });\r\n    },\r\n    // 跳转到询标\r\n    bidInquiry() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      console.log(\"query\", query);\r\n      this.$router.push({ path: \"/bidInquiry\", query: query });\r\n    },\r\n    // 获取因素对应页码\r\n    getFactorsPage() {\r\n      this.factorsPage = JSON.parse(localStorage.getItem(\"entDocResponsePage\"));\r\n    },\r\n    \r\n    /**\r\n     * 获取评分项的最大分值\r\n     * @param {Object} item - 评分项对象\r\n     * @returns {number} 最大分值\r\n     */\r\n    getMaxScore(item) {\r\n      if (!item) return 0;\r\n      \r\n      // 如果有分数挡位，使用挡位中的最大值\r\n      if (item.scoreLevel && item.scoreLevel.length > 0 && item.scoreLevel !== null && item.scoreLevel !== undefined) {\r\n        const scoreLevels = item.scoreLevel.split(',').map(level => parseFloat(level.trim())).filter(level => !isNaN(level));\r\n        if (scoreLevels.length > 0) {\r\n          return Math.max(...scoreLevels);\r\n        }\r\n      }\r\n      \r\n      // 否则使用配置的最大分值\r\n      return parseFloat(item.score) || 0;\r\n    },\r\n\t  downloadFile(item){\r\n\t\t\tthis.$download.zip(item.filePath,item.fileName);\r\n\t  },\r\n\t  \r\n\t  // ========== 悬停相关 ==========\r\n\t  /**\r\n\t   * 显示评分项悬浮框\r\n\t   * @param {Object} factorItem 评分项对象\r\n\t   */\r\n\t  showFactorTooltip(factorItem) {\r\n\t\t  if (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\t\t  \r\n\t\t  // 清除之前的定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t  this.tooltipTimer = setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = factorItem;\r\n\t\t  }, 300); // 300ms延迟\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 隐藏评分项悬浮框\r\n\t   */\r\n\t  hideFactorTooltip() {\r\n\t\t  // 清除定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t  setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = null;\r\n\t\t  }, 100);\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t   */\r\n\t  clearTooltipTimer() {\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t  }\r\n  },\r\n  mounted() {\r\n    this.init();\r\n    this.getFactorsPage();\r\n  },\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// Main layout containers\r\n.technical-review-container {\r\n  min-height: 57vh;\r\n  display: flex;\r\n}\r\n\r\n.main-content {\r\n  min-height: 57vh;\r\n  width: 79%;\r\n}\r\n\r\n// Header section styles\r\n.header-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n\r\n.title-section {\r\n  display: flex;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333333;\r\n}\r\n\r\n.main-title {\r\n  // Inherits from title-section\r\n}\r\n\r\n.help-section {\r\n  display: grid;\r\n  justify-items: center;\r\n  position: relative;\r\n  bottom: -30px;\r\n}\r\n\r\n.help-text {\r\n  font-size: 12px;\r\n}\r\n\r\n.help-image {\r\n  width: 80px;\r\n  height: 30px;\r\n  margin-right: 20px;\r\n}\r\n\r\n// File list container\r\n.file-list-container {\r\n  border-right: 1px solid #e6e6e6;\r\n  border-left: 1px solid #e6e6e6;\r\n  padding: 10px;\r\n  overflow-y: auto;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\ttransition: all 0.3s ease;\r\n\t::v-deep .el-card__body {\r\n\t\tpadding: 0;\r\n\t}\r\n}\r\n\r\n.file-list-title {\r\n  font-weight: bold;\r\n  margin-bottom: 10px;\r\n  color: #333;\r\n}\r\n\r\n.file-item {\r\n  margin-bottom: 8px;\r\n  cursor: pointer;\r\n}\r\n\r\n.file-item-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 5px;\r\n}\r\n\r\n.file-icon {\r\n  margin-right: 8px;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-name {\r\n  font-size: 12px;\r\n  flex: 1;\r\n  word-break: break-all;\r\n}\r\n\r\n.download-icon {\r\n  margin-left: 8px;\r\n  color: #999;\r\n}\r\n\r\n// Action buttons section\r\n.action-buttons {\r\n  text-align: right;\r\n}\r\n\r\n.button-group {\r\n  margin-top: 20px;\r\n\t.item-button{\r\n\t\tcolor: #fff;\r\n\t}\r\n}\r\n\r\n.primary-btn {\r\n  background-color: #176ADB;\r\n  color: #FFFFFF;\r\n  border: 1px solid #176ADB;\r\n}\r\n\r\n// Content section styles\r\n.content-section {\r\n  display: flex;\r\n  height: 82%;\r\n}\r\n\r\n// PDF preview area\r\n.pdf-preview-area {\r\n  display: flex;\r\n  justify-content: center;\r\n  flex: 1;\r\n}\r\n\r\n.pdf-panel {\r\n  width: 49%;\r\n}\r\n\r\n.response-panel {\r\n  &.border-right {\r\n    border-right: 1px solid #176ADB;\r\n  }\r\n}\r\n\r\n.procurement-panel {\r\n  &.border-left {\r\n    border-left: 1px solid #176ADB;\r\n  }\r\n}\r\n\r\n// Divider styles\r\n.divider {\r\n  min-height: 57vh;\r\n  width: 1%;\r\n  background-color: #F5F5F5;\r\n}\r\n\r\n// Sidebar styles\r\n.sidebar {\r\n  min-height: 57vh;\r\n  width: 20%;\r\n}\r\n\r\n.sidebar-header {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n\r\n.supplier-select {\r\n  width: 100%;\r\n}\r\n\r\n.sidebar-content {\r\n  padding: 15px 20px;\r\n}\r\n\r\n// Factor items styles\r\n.factor-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.factor-header {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  text-align: left;\r\n  width: 98%;\r\n}\r\n\r\n.factor-name {\r\n  cursor: pointer;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 500;\r\n  font-size: 16px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n}\r\n\r\n.max-score {\r\n  font-size: 12px;\r\n  color: red;\r\n}\r\n\r\n.factor-input {\r\n  display: flex;\r\n  width: 100%;\r\n  justify-content: flex-end;\r\n  padding: 10px;\r\n}\r\n\r\n.score-value {\r\n  color: green;\r\n  font-size: 16px;\r\n}\r\n\r\n// Submit section\r\n.submit-section {\r\n  display: flex;\r\n  margin: 32px 0;\r\n  justify-content: space-evenly;\r\n}\r\n\r\n// Review content styles\r\n.review-content {\r\n  text-align: left;\r\n  font-size: 14px;\r\n}\r\n\r\n.review-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 15px;\r\n  color: #176ADB;\r\n  letter-spacing: 0;\r\n}\r\n\r\n.review-text {\r\n  padding: 6px 30px;\r\n}\r\n\r\n// Existing styles\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n\t  transition: all 0.3s ease;\r\n\t  padding: 4px 8px;\r\n\t  border-radius: 4px;\r\n\t  \r\n\t  &:hover {\r\n\t\t  background-color: #f0f8ff;\r\n\t\t  color: #176ADB;\r\n\t\t  transform: translateX(2px);\r\n\t  }\r\n  }\r\n}\r\n.item-button {\r\n  border: 1px solid #979797;\r\n  width: 150px;\r\n  height: 36px;\r\n  margin: 0 10px;\r\n  font-weight: 700;\r\n  font-size: 17px;\r\n  border-radius: 6px;\r\n  color: #333333;\r\n  &:hover {\r\n    color: #333333;\r\n  }\r\n}\r\n.technical-blue-btn {\r\n  background-color: #176ADB !important;\r\n  color: #fff !important;\r\n  border: 1px solid #176ADB !important;\r\n}\r\n.technical-blue-btn-active {\r\n  background-color: #FF6B35 !important;\r\n  color: #fff !important;\r\n  border: 1px solid #FF6B35 !important;\r\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.item-button-little {\r\n  width: 124px;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #fff;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  }\r\n\r\n  ::v-deep .el-card__body {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\t\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\t\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\t\t\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\t\t\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\t\t\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n</style>\r\n\r\n"]}]}