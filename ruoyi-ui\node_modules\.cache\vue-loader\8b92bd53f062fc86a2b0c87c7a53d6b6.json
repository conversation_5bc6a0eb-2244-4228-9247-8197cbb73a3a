{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification\\one.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification\\one.vue", "mtime": 1753926699532}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KCXN1cHBsaWVySW5mbywNCglhcHByb3ZhbFByb2Nlc3MsDQoJc2NvcmluZ0ZhY3RvcnMsDQoJY2hlY2tSZXZpZXdTdW1tYXJ5LA0KCWZpbGVzQnlJZCwNCn0gZnJvbSAiQC9hcGkvZXhwZXJ0L3JldmlldyI7DQppbXBvcnQgeyBnZXREZXRhaWxCeVBzeHggfSBmcm9tICJAL2FwaS9ldmFsdWF0aW9uL2RldGFpbC8iOw0KaW1wb3J0IHsgZWRpdEV2YWxFeHBlcnRTY29yZUluZm8gfSBmcm9tICJAL2FwaS9ldmFsdWF0aW9uL2V4cGVydFN0YXR1cyI7DQppbXBvcnQgeyByZXNEb2NSZXZpZXdGYWN0b3JzRGVjaXNpb24gfSBmcm9tICJAL2FwaS9kb2NSZXNwb25zZS9lbnRJbmZvIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KCWRhdGEoKSB7DQoJCXJldHVybiB7DQoJCQlzdXBwbGllck9wdGlvbnM6IFtdLCAvLyDkvpvlupTllYbkuIvmi4npgInpobkNCgkJCXNjb3JpbmdTeXN0ZW06IFtdLCAvLyDlvZPliY3or4TliIbkvZPns7sNCgkJCXNlbGVjdGVkRmFjdG9yTm9kZToge30sIC8vIOW9k+WJjemAieS4reivhOWIhumhuQ0KCQkJc2VsZWN0ZWRTdXBwbGllck5hbWU6ICIiLCAvLyDlvZPliY3pgInkuK3kvpvlupTllYblkI3np7ANCgkJCXNlbGVjdGVkU3VwcGxpZXI6IHt9LCAvLyDlvZPliY3pgInkuK3kvpvlupTllYblr7nosaENCgkJCWV4cGVydEluZm86IHt9LCAvLyDkuJPlrrbkv6Hmga8NCgkJCXJhdGluZ1N0YXRlTWFwOiB7fSwgLy8g6K+E5YiG6aG554q25oCB5LiO5Y6f5ZugDQoJCQlmaWxlSW5mbzoge30sIC8vIOaWh+S7tuS/oeaBrw0KCQkJaXNSZXNwb25zZVZpc2libGU6IGZhbHNlLCAvLyDmmK/lkKbmmL7npLrlk43lupTmlofku7YNCgkJCWlzUHJvY3VyZW1lbnRWaXNpYmxlOiBmYWxzZSwgLy8g5piv5ZCm5pi+56S66YeH6LSt5paH5Lu2DQoJCQlpc0RvdWJsZVZpZXc6IGZhbHNlLCAvLyDmmK/lkKblr7nmr5TmmL7npLoNCgkJCQ0KCQkJZW50RG9jUmVzcG9uc2VQYWdlOiB7fSwgLy8g5ZON5bqU5paH5Lu26aG156CB5L+h5oGvDQoJCQkNCgkJCWZhY3RvckRldGFpbExpc3Q6IFtdLCAvLyDor4TliIbpobnor6bmg4UNCgkJCWZhY3RvcnNQYWdlTWFwOiB7fSwgLy8g6K+E5YiG6aG56aG156CB5L+h5oGvDQoJCQkNCgkJCWVudERvY1Byb2N1cmVtZW50UGFnZToge30sIC8vIOmHh+i0reaWh+S7tumhteeggeS/oeaBrw0KCQkJcGFnZVByb2N1cmVtZW50OltdLCAvLyDph4fotK3mlofku7bnmoTor4TliIbpobkNCgkJCWF0dGFjaG1lbnRzTGlzdDogW10sIC8vIOaWh+S7tuWIl+ihqA0KCQkJDQoJCQlzdXBwbGllckZhY3RvclBhZ2VNYXA6IHt9LCAvLyDlvZPliY3kvpvlupTllYbor4TliIbpobnpobXnoIENCgkJCXJlc3BvbnNlUGRmVXJsOiBudWxsLCAvLyDlk43lupTmlofku7ZQREbot6/lvoQNCgkJCXByb2N1cmVtZW50UGRmVXJsOiBudWxsLCAvLyDph4fotK3mlofku7ZQREbot6/lvoQNCg0KCQkJLy8g5oyJ6ZKu54q25oCB566h55CGDQoJCQlhY3RpdmVCdXR0b246ICdyZXNwb25zZScsIC8vIOW9k+WJjea/gOa0u+eahOaMiemSru+8midyZXNwb25zZSfjgIEncHJvY3VyZW1lbnQn44CBJ2NvbnRyYXN0Jw0KDQoJCQkvLyBQREbmuLLmn5PnirbmgIHnrqHnkIYNCgkJCXJlc3BvbnNlUGRmUmVuZGVyZWQ6IGZhbHNlLCAvLyDlk43lupTmlofku7ZQREbmmK/lkKbmuLLmn5PlrozmiJANCgkJCXByb2N1cmVtZW50UGRmUmVuZGVyZWQ6IGZhbHNlLCAvLyDph4fotK3mlofku7ZQREbmmK/lkKbmuLLmn5PlrozmiJANCg0KCQkJaGVscEltYWdlTGlzdDogWyIvZXZhbHV0aW9uL2hlbHAuanBnIl0sIC8vIOatpemqpOWbvueJhw0KCQkJZmFjdG9yS2V5TWFwOiB7IC8vIOivhOWIhumhueS4juWQjuerr+Wtl+auteaYoOWwhA0KCQkJCSLnibnlrprotYTmoLzopoHmsYIiOiAiemd6cyIsDQoJCQkJIuWTjeW6lOWGheWuuSI6ICJqc3BsYiIsDQoJCQkJIumHh+i0remcgOaxgiI6ICJqc3BsYiIsDQoJCQkJIuS+m+i0p+acn+mZkCI6ICJnaHF4IiwNCgkJCQki5oqV5qCH5oql5Lu3IjogInRiYmoiDQoJCQl9LA0KCQkJY2hlY2tSZXN1bHQ6IHt9LCAvLyDns7vnu5/liJ3pqoznu5PmnpwNCgkJCWNoZWNrUmVzdWx0TGFiZWw6IHsgLy8g57O757uf5Yid6aqM57uT5p6c5ZCN56ewDQoJCQkJIuespuWQiOOAiuS4reWNjuS6uuawkeWFseWSjOWbveaUv+W6nOmHh+i0reazleOAi+esrOS6jOWNgeS6jOadoeinhOWumiI6ICLns7vnu5/liJ3pqozpgJrov4ciLA0KCQkJCSLnibnlrprotYTmoLzopoHmsYIiOiAi57O757uf5Yid6aqM6YCa6L+HIiwNCgkJCQki5L+h55So5p+l6K+iIjogIuezu+e7n+WInemqjOmAmui/hyIsDQoJCQkJIuWTjeW6lOS6uuWQjeensCI6ICLns7vnu5/liJ3pqozpgJrov4ciLA0KCQkJCSLlk43lupTlhoXlrrkiOiAi57O757uf5Yid6aqM6YCa6L+HIiwNCgkJCQki6YeH6LSt6ZyA5rGCIjogIuezu+e7n+WInemqjOmAmui/hyIsDQoJCQkJIuS+m+i0p+acn+mZkCI6ICLns7vnu5/liJ3pqozpgJrov4ciLA0KCQkJCSLmipXmoIfmiqXku7ciOiAi57O757uf5Yid6aqM6YCa6L+HIg0KCQkJfSwNCg0KCQkJLy8g5oKs5YGc54q25oCB566h55CGDQoJCQlob3ZlcmVkRmFjdG9yTm9kZTogbnVsbCwgLy8g5oKs5YGc5pe255qE6K+E5YiG6aG5DQoJCQl0b29sdGlwVGltZXI6IG51bGwsIC8vIOaCrOa1ruahhuaYvuekuuWumuaXtuWZqA0KCQl9Ow0KCX0sDQoNCgltZXRob2RzOiB7DQoJCS8vID09PT09PT09PT0g6K+E5YiG55u45YWzID09PT09PT09PT0NCgkJLyoqDQoJCSAqIOezu+e7n+WInemqjOe7k+aenOWIpOaWrQ0KCQkgKiBAcGFyYW0ge3N0cmluZ30gZmFjdG9yTmFtZSDor4TliIbpobnlkI3np7ANCgkJICogQHJldHVybnMge3N0cmluZ30gMS3pgJrov4cgMC3mnKrpgJrov4cNCgkJICovDQoJCWdldENoZWNrUmVzdWx0U3RhdGUoZmFjdG9yTmFtZSkgew0KCQkJaWYgKCF0aGlzLmNoZWNrUmVzdWx0IHx8IE9iamVjdC5rZXlzKHRoaXMuY2hlY2tSZXN1bHQpLmxlbmd0aCA9PT0gMCkgcmV0dXJuICIiOyAvLyDlpoLmnpzmsqHmnInns7vnu5/liJ3pqoznu5PmnpzvvIzliJnov5Tlm57nqboNCgkJCWxldCBzdGF0ZSA9ICIxIjsNCgkJCWNvbnN0IGtleSA9IHRoaXMuZmFjdG9yS2V5TWFwW2ZhY3Rvck5hbWVdOw0KCQkJaWYgKGtleSkgew0KCQkJCXN0YXRlID0gdGhpcy5jaGVja1Jlc3VsdFtrZXldOw0KCQkJCWlmIChmYWN0b3JOYW1lID09PSAi5oqV5qCH5oql5Lu3IiAmJiBzdGF0ZSA9PT0gIjEiKSB7DQoJCQkJCXN0YXRlID0gdGhpcy5jaGVja1Jlc3VsdFsibXhiamIiXTsNCgkJCQl9DQoJCQl9DQoJCQlpZiAoc3RhdGUgPT09ICIwIikgew0KCQkJCXRoaXMuY2hlY2tSZXN1bHRMYWJlbFtmYWN0b3JOYW1lXSA9ICLns7vnu5/liJ3pqozmnKrpgJrov4ciOw0KCQkJfSBlbHNlIHsNCgkJCQlzdGF0ZSA9ICIxIjsNCgkJCQl0aGlzLmNoZWNrUmVzdWx0TGFiZWxbZmFjdG9yTmFtZV0gPSAi57O757uf5Yid6aqM6YCa6L+HIjsNCgkJCX0NCgkJCXJldHVybiBzdGF0ZTsNCgkJfSwNCg0KCQkvKioNCgkJICog5qCh6aqM5omA5pyJ6K+E5YiG6aG55piv5ZCm5aGr5YaZ5a6M5pW0DQoJCSAqIEByZXR1cm5zIHtib29sZWFufSDmmK/lkKblhajpg6jloavlhpkNCgkJICovDQoJCXZhbGlkYXRlQWxsUmF0aW5ncygpIHsNCgkJCWZvciAoY29uc3QgaXRlbSBvZiB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zKSB7DQoJCQkJY29uc3Qgc3RhdGUgPSB0aGlzLnJhdGluZ1N0YXRlTWFwW2l0ZW0uZW50TWV0aG9kSXRlbUlkXS5zdGF0ZTsNCgkJCQljb25zdCByZWFzb24gPSB0aGlzLnJhdGluZ1N0YXRlTWFwW2l0ZW0uZW50TWV0aG9kSXRlbUlkXS5yZWFzb247DQoJCQkJLy8g6K+E5YiG57uT5p6c5pyq5aGr5YaZDQoJCQkJaWYgKHN0YXRlID09PSBudWxsIHx8IHN0YXRlID09PSAnJykgew0KCQkJCQkvLyB0aGlzLiRtZXNzYWdlLndhcm5pbmcoYOivt+Whq+WGmeivhOWIhumhue+8miR7aXRlbS5pdGVtTmFtZX0g55qE6K+E5YiG57uT5p6cYCk7DQoJCQkJCXJldHVybiB0cnVlOw0KCQkJCX0NCgkJCQkvLyDkuI3pgJrov4fkvYbmnKrloavlhpnljp/lm6ANCgkJCQlpZiAoc3RhdGUgPT09ICIwIiAmJiAoIXJlYXNvbiB8fCByZWFzb24udHJpbSgpID09PSAnJykpIHsNCgkJCQkJdGhpcy4kbWVzc2FnZS53YXJuaW5nKGAke2l0ZW0uaXRlbU5hbWV96K+E5a6h5LiN6YCa6L+H5L2G5pyq5aGr5YaZ5aSH5rOo77yM5LiN6L+b6KGM5L+d5a2YYCk7DQoJCQkJCXJldHVybiBmYWxzZTsNCgkJCQl9DQoJCQl9DQoJCQlyZXR1cm4gdHJ1ZTsNCgkJfSwNCg0KCQkvLyDnlJ/miJDkv53lrZjmlbDmja4NCgkJZ2VuZXJhdGVTYXZlRGF0YSgpIHsNCgkJCWNvbnN0IHJhdGluZ0NvcHkgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMucmF0aW5nU3RhdGVNYXApKTsgLy8g6K+E5YiG6aG554q25oCBDQoJCQljb25zdCBkYXRhID0gW107IC8vIOS/neWtmOaVsOaNrg0KCQkJZm9yIChjb25zdCBpdGVtIG9mIHRoaXMuc2NvcmluZ1N5c3RlbS51aXRlbXMpIHsgLy8g6YGN5Y6G6K+E5YiG6aG5DQoJCQkJY29uc3QgaXRlbUlkID0gaXRlbS5lbnRNZXRob2RJdGVtSWQ7IC8vIOivhOWIhumhuUlEDQoJCQkJY29uc3QgZXZhbHVhdGlvblJlc3VsdCA9IHJhdGluZ0NvcHlbaXRlbUlkXS5zdGF0ZTsgLy8g6K+E5YiG6aG554q25oCBDQoJCQkJaWYgKGV2YWx1YXRpb25SZXN1bHQgPT09IG51bGwgfHwgZXZhbHVhdGlvblJlc3VsdCA9PT0gIiIpIGNvbnRpbnVlOyAvLyDlpoLmnpzor4TliIbpobnnirbmgIHkuLrnqbrvvIzliJnot7Pov4cNCgkJCQkvLyDms6jmhI/vvJrkuI3pgJrov4fljp/lm6DnmoTmoKHpqozlt7Lnu4/lnKh2YWxpZGF0ZUFsbFJhdGluZ3PkuK3lpITnkIbvvIzov5nph4zlj6rpnIDopoHmnoTlu7rmlbDmja4NCgkJCQljb25zdCBldmFsdWF0aW9uUmVtYXJrID0gcmF0aW5nQ29weVtpdGVtSWRdLnJlYXNvbiB8fCAiIjsgLy8g6K+E5YiG6aG55aSH5rOoDQoJCQkJZGF0YS5wdXNoKHsNCgkJCQkJc2NvcmluZ01ldGhvZFVpdGVtSWQ6IGl0ZW1JZCwgLy8g6K+E5YiG6aG5SUQNCgkJCQkJZXhwZXJ0UmVzdWx0SWQ6IHRoaXMuZXhwZXJ0SW5mby5yZXN1bHRJZCwgLy8g5LiT5a62SUQNCgkJCQkJZW50SWQ6IHRoaXMuc2VsZWN0ZWRTdXBwbGllci5iaWRkZXJJZCwgLy8g5L6b5bqU5ZWGSUQNCgkJCQkJZXZhbHVhdGlvblJlc3VsdCwgLy8g6K+E5YiG6aG554q25oCBDQoJCQkJCWV2YWx1YXRpb25SZW1hcmsgLy8g6K+E5YiG6aG55aSH5rOoDQoJCQkJfSk7DQoJCQl9DQoJCQlyZXR1cm4gZGF0YTsgLy8g6L+U5Zue5L+d5a2Y5pWw5o2uDQoJCX0sDQoNCgkJLyoqDQoJCSAqIOS4tOaXtuS/neWtmOivhOWIhue7k+aenA0KCQkgKiBAcmV0dXJucyB7UHJvbWlzZX0NCgkJICovDQoJCXNhdmVSYXRpbmdUZW1wKCkgew0KCQkJLy8g5YWI5qCh6aqM5omA5pyJ6K+E5YiG6aG55piv5ZCm5aGr5YaZ5a6M5pW0DQoJCQlpZiAoIXRoaXMudmFsaWRhdGVBbGxSYXRpbmdzKCkpIHsNCgkJCQlyZXR1cm4gUHJvbWlzZS5yZXNvbHZlKHsgY29kZTogMCwgc3VjY2VzczogZmFsc2UgfSk7IC8vIOagoemqjOWksei0pQ0KCQkJfQ0KDQoJCQljb25zdCBkYXRhID0gdGhpcy5nZW5lcmF0ZVNhdmVEYXRhKCk7IC8vIOeUn+aIkOS/neWtmOaVsOaNrg0KCQkJaWYgKGRhdGEubGVuZ3RoID4gMCkgew0KCQkJCXJldHVybiBzY29yaW5nRmFjdG9ycyhkYXRhKS50aGVuKHJlc3BvbnNlID0+IHsNCgkJCQkJaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KCQkJCQkJdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLkv53lrZjmiJDlip8iKTsNCgkJCQkJCXJldHVybiB7IGNvZGU6IDIwMCwgc3VjY2VzczogdHJ1ZSB9Ow0KCQkJCQl9IGVsc2Ugew0KCQkJCQkJdGhpcy4kbWVzc2FnZS53YXJuaW5nKHJlc3BvbnNlLm1zZyk7DQoJCQkJCQlyZXR1cm4geyBjb2RlOiByZXNwb25zZS5jb2RlLCBzdWNjZXNzOiBmYWxzZSB9Ow0KCQkJCQl9DQoJCQkJfSkuY2F0Y2goKGVycm9yKSA9PiB7DQoJCQkJCXRoaXMuJG1lc3NhZ2UuZXJyb3IoIuS/neWtmOWksei0pSIpOw0KCQkJCQlyZXR1cm4geyBjb2RlOiAwLCBzdWNjZXNzOiBmYWxzZSB9Ow0KCQkJCX0pOw0KCQkJfSBlbHNlIHsNCgkJCQlyZXR1cm4gUHJvbWlzZS5yZXNvbHZlKHsgY29kZTogMjAwLCBzdWNjZXNzOiB0cnVlIH0pOyAvLyDmsqHmnInmlbDmja7pnIDopoHkv53lrZjml7bkuZ/ov5Tlm57miJDlip8NCgkJCX0NCgkJfSwNCg0KCQkvKioNCgkJICog5o+Q5Lqk6K+E5YiG57uT5p6cDQoJCSAqLw0KCQlzdWJtaXRSYXRpbmcoKSB7DQoJCQl0aGlzLnNhdmVSYXRpbmdUZW1wKCkudGhlbigoc2F2ZVJlc3VsdCkgPT4gew0KCQkJCS8vIOajgOafpeS/neWtmOe7k+aenO+8jOWmguaenOagoemqjOWksei0peWImeS4jee7p+e7reaPkOS6pA0KCQkJCWlmICghc2F2ZVJlc3VsdCB8fCBzYXZlUmVzdWx0LnN1Y2Nlc3MgPT09IGZhbHNlKSB7DQoJCQkJCXJldHVybjsgLy8g5qCh6aqM5aSx6LSl77yM5LiN57un57ut5o+Q5Lqk5rWB56iLDQoJCQkJfQ0KCQkJCXRoaXMuY2hlY2tBbmRTdWJtaXRSZXZpZXcoKTsNCgkJCX0pOw0KCQl9LA0KDQoJCS8vIOajgOafpeW5tuaPkOS6pOivhOWuoeaxh+aAuw0KCQljaGVja0FuZFN1Ym1pdFJldmlldygpIHsNCgkJCQljb25zdCBkYXRhID0gew0KCQkJCQlwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwNCgkJCQkJZXhwZXJ0UmVzdWx0SWQ6IHRoaXMuZXhwZXJ0SW5mby5yZXN1bHRJZCwNCgkJCQkJc2NvcmluZ01ldGhvZEl0ZW1JZDogdGhpcy4kcm91dGUucXVlcnkuc2NvcmluZ01ldGhvZEl0ZW1JZCwNCgkJCQl9Ow0KCQkJCWNoZWNrUmV2aWV3U3VtbWFyeShkYXRhKS50aGVuKChyZXNwb25zZSkgPT4gew0KCQkJCQlpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQoJCQkJCQl0aGlzLnVwZGF0ZUV4cGVydFNjb3JlU3RhdHVzKCk7IC8vIOS/ruaUueS4k+Wutui/m+W6pueKtuaAgQ0KCQkJCQkJdGhpcy4kZW1pdCgic2VuZCIsICJ0d28iKTsgLy8g6Lez6L2s6Iez5LqM5qyh5oql5Lu3DQoJCQkJCX0gZWxzZSB7DQoJCQkJCQl0aGlzLiRtZXNzYWdlLndhcm5pbmcocmVzcG9uc2UubXNnKTsNCgkJCQkJfQ0KCQkJfSk7DQoJCX0sDQoNCgkJLy8g5L+u5pS55LiT5a626L+b5bqm54q25oCBDQoJCXVwZGF0ZUV4cGVydFNjb3JlU3RhdHVzKCkgew0KCQkJY29uc3Qgc3RhdHVzID0gew0KCQkJCWV2YWxFeHBlcnRTY29yZUluZm9JZDogSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZXZhbEV4cGVydFNjb3JlSW5mbyIpKS5ldmFsRXhwZXJ0U2NvcmVJbmZvSWQsDQoJCQkJZXZhbFN0YXRlOiAxLA0KCQkJfTsNCgkJCWVkaXRFdmFsRXhwZXJ0U2NvcmVJbmZvKHN0YXR1cykudGhlbigocmVzKSA9PiB7DQoJCQkJaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCgkJCQkJdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmj5DkuqTmiJDlip8iKTsNCgkJCQl9DQoJCQl9KTsNCgkJfSwNCg0KCQkvLyA9PT09PT09PT09IOWIneWni+WMluebuOWFsyA9PT09PT09PT09DQoJCS8qKg0KCQkgKiDliJ3lp4vljJbpobXpnaLmlbDmja4NCgkJICovDQoJCWluaXRQYWdlKCkgew0KCQkJdGhpcy5pbml0RXhwZXJ0SW5mbygpOy8vIOWIneWni+WMluS4k+WutuS/oeaBrw0KCQkJdGhpcy5pbml0RW50RG9jUmVzcG9uc2VQYWdlKCk7IC8vIOWIneWni+WMluWTjeW6lOaWh+S7tumhteeggeS/oeaBrw0KCQkJdGhpcy5pbml0RW50RG9jUHJvY3VyZW1lbnRQYWdlKCk7IC8vIOWIneWni+WMlumHh+i0reaWh+S7tumhteeggeS/oeaBrw0KCQkJdGhpcy5sb2FkU3VwcGxpZXJPcHRpb25zKCk7Ly8g5Yqg6L295L6b5bqU5ZWG5LiL5ouJ6YCJ6aG5DQoJCQl0aGlzLmxvYWRTY29yaW5nU3lzdGVtKCk7IC8vIOWKoOi9veivhOWIhuS9k+ezuw0KCQkJdGhpcy5sb2FkRmlsZXMoKTsgLy8g5Yqg6L295paH5Lu25L+h5oGvDQoJCX0sDQoJCS8vIOWIneWni+WMluS4k+WutuS/oeaBrw0KCQlpbml0RXhwZXJ0SW5mbygpIHsNCgkJCXRyeSB7DQoJCQkJY29uc3QgZXhwZXJ0SW5mb1N0ciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJleHBlcnRJbmZvIik7DQoJCQkJaWYgKGV4cGVydEluZm9TdHIpIHsNCgkJCQkJdGhpcy5leHBlcnRJbmZvID0gSlNPTi5wYXJzZShleHBlcnRJbmZvU3RyKTsNCgkJCQkJY29uc29sZS5sb2coIuS4k+WutuS/oeaBr+W3suWIneWni+WMliIsIHRoaXMuZXhwZXJ0SW5mbyk7DQoJCQkJfSBlbHNlIHsNCgkJCQkJY29uc29sZS53YXJuKCJsb2NhbFN0b3JhZ2XkuK3mnKrmib7liLBleHBlcnRJbmZvIik7DQoJCQkJfQ0KCQkJfSBjYXRjaCAoZXJyb3IpIHsNCgkJCQljb25zb2xlLmVycm9yKCLliJ3lp4vljJbkuJPlrrbkv6Hmga/lpLHotKU6IiwgZXJyb3IpOw0KCQkJfQ0KCQl9LA0KCQkvLyDliJ3lp4vljJblk43lupTmlofku7bpobXnoIHkv6Hmga8NCgkJaW5pdEVudERvY1Jlc3BvbnNlUGFnZSgpIHsNCgkJCXRoaXMuZW50RG9jUmVzcG9uc2VQYWdlID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZW50RG9jUmVzcG9uc2VQYWdlIikpOw0KCQl9LA0KCQkvLyDliJ3lp4vljJbph4fotK3mlofku7bpobXnoIHkv6Hmga8NCgkJaW5pdEVudERvY1Byb2N1cmVtZW50UGFnZSgpIHsNCgkJCXRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZW50RG9jUHJvY3VyZW1lbnRQYWdlIikpOw0KCQl9LA0KCQkvLyDliqDovb3kvpvlupTllYbkuIvmi4npgInpobkNCgkJbG9hZFN1cHBsaWVyT3B0aW9ucygpIHsNCgkJCXN1cHBsaWVySW5mbyh7IHByb2plY3RJZDogdGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQoJCQkJaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KCQkJCQl0aGlzLnN1cHBsaWVyT3B0aW9ucyA9IHJlc3BvbnNlLnJvd3MuZmlsdGVyKGl0ZW0gPT4gaXRlbS5pc0FiYW5kb25lZEJpZCA9PT0gMCk7IC8vIOi/h+a7pOaOieiiq+aUvuW8g+eahOaKleaghw0KCQkJCQljb25zb2xlLmxvZyh0aGlzLnN1cHBsaWVyT3B0aW9ucyk7DQoJCQkJfSBlbHNlIHsNCgkJCQkJdGhpcy4kbWVzc2FnZS53YXJuaW5nKHJlc3BvbnNlLm1zZyk7DQoJCQkJfQ0KCQkJfSk7DQoJCX0sDQoJCS8vIOWKoOi9veivhOWIhuS9k+ezu+WPiuWIneWni+WMluivhOWIhumhueeKtuaAgQ0KCQlsb2FkU2NvcmluZ1N5c3RlbSgpIHsNCgkJCWFwcHJvdmFsUHJvY2Vzcyh0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQsIHRoaXMuZXhwZXJ0SW5mby5yZXN1bHRJZCkudGhlbigocmVzcG9uc2UpID0+IHsNCgkJCQlpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQoJCQkJCS8vIOaWh+S7tuWIl+ihqA0KCQkJCQl0aGlzLmF0dGFjaG1lbnRzTGlzdCA9IHJlc3BvbnNlLmRhdGEuYnVzaVRlbmRlck5vdGljZS5hdHRhY2htZW50cy5maWx0ZXIoaXRlbSA9PiBpdGVtLmZpbGVUeXBlID09ICIwIik7DQoNCgkJCQkJdGhpcy5zY29yaW5nU3lzdGVtID0gcmVzcG9uc2UuZGF0YS5zY29yaW5nTWV0aG9kVWluZm8uc2NvcmluZ01ldGhvZEl0ZW1zLmZpbmQoDQoJCQkJCQlpdGVtID0+IGl0ZW0uc2NvcmluZ01ldGhvZEl0ZW1JZCA9PSB0aGlzLiRyb3V0ZS5xdWVyeS5zY29yaW5nTWV0aG9kSXRlbUlkDQoJCQkJCSk7IC8vIOiOt+WPluW9k+WJjeivhOWIhumhuQ0KCQkJCQlsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgNCgkJCQkJCSJldmFsUHJvamVjdEV2YWx1YXRpb25Qcm9jZXNzIiwNCgkJCQkJCUpTT04uc3RyaW5naWZ5KHRoaXMuc2NvcmluZ1N5c3RlbS5ldmFsUHJvamVjdEV2YWx1YXRpb25Qcm9jZXNzKQ0KCQkJCQkpOyAvLyDkv53lrZjor4TliIbkvZPns7sNCgkJCQkJY29uc29sZS5sb2codGhpcy5zY29yaW5nU3lzdGVtKTsNCgkJCQkJdGhpcy5pbml0UmF0aW5nU3RhdGVNYXBCeVN5c3RlbSgpOyAvLyDliJ3lp4vljJbor4TliIbpobnnirbmgIENCgkJCQl9IGVsc2Ugew0KCQkJCQl0aGlzLiRtZXNzYWdlLndhcm5pbmcocmVzcG9uc2UubXNnKTsNCgkJCQl9DQoJCQl9KTsNCgkJfSwNCgkJLy8g5Yid5aeL5YyW6K+E5YiG6aG554q25oCB77yI5qC55o2u6K+E5YiG5L2T57O777yJDQoJCWluaXRSYXRpbmdTdGF0ZU1hcEJ5U3lzdGVtKCkgew0KCQkJdGhpcy5yYXRpbmdTdGF0ZU1hcCA9IHRoaXMuc2NvcmluZ1N5c3RlbS51aXRlbXMucmVkdWNlKChhY2MsIGl0ZW0pID0+IHsNCgkJCQlhY2NbaXRlbS5lbnRNZXRob2RJdGVtSWRdID0geyBzdGF0ZTogbnVsbCwgcmVhc29uOiAiIiB9Ow0KCQkJCXJldHVybiBhY2M7DQoJCQl9LCB7fSk7IC8vIOWIneWni+WMluivhOWIhumhueeKtuaAgQ0KCQl9LA0KCQkvLyDliqDovb3mlofku7bkv6Hmga8NCgkJbG9hZEZpbGVzKCkgew0KCQkJZmlsZXNCeUlkKHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCkudGhlbigocmVzcG9uc2UpID0+IHsNCgkJCQlpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQoJCQkJCXRoaXMuZmlsZUluZm8gPSByZXNwb25zZS5kYXRhOyAvLyDmlofku7bkv6Hmga8NCgkJCQkJaWYgKHRoaXMuZmlsZUluZm8udGVuZGVyTm90aWNlRmlsZVBhdGgpIHsNCgkJCQkJCXRoaXMucHJvY3VyZW1lbnRQZGZVcmwgPSB0aGlzLmZpbGVJbmZvLnRlbmRlck5vdGljZUZpbGVQYXRoOyAvLyDph4fotK3mlofku7YNCgkJCQkJfQ0KCQkJCQkvLyDkuI3oh6rliqjorr7nva7lk43lupTmlofku7ZVUkzvvIzlj6rlnKjpnIDopoHml7bmiY3orr7nva4NCgkJCQkJLy8gaWYgKHRoaXMuZmlsZUluZm8uZmlsZSkgew0KCQkJCQkvLyAJdGhpcy5yZXNwb25zZVBkZlVybCA9IHRoaXMuZmlsZUluZm8uZmlsZVswXTsgLy8g5ZON5bqU5paH5Lu2DQoJCQkJCS8vIH0NCgkJCQl9IGVsc2Ugew0KCQkJCQl0aGlzLiRtZXNzYWdlLndhcm5pbmcocmVzcG9uc2UubXNnKTsNCgkJCQl9DQoJCQl9KTsNCgkJfSwNCg0KCQkvLyA9PT09PT09PT09IOS+m+W6lOWVhuebuOWFsyA9PT09PT09PT09DQoJCS8qKg0KCQkgKiDkvpvlupTllYbliIfmjaLml7blpITnkIYNCgkJICogQHBhcmFtIHtzdHJpbmd9IHN1cHBsaWVyTmFtZSDkvpvlupTllYblkI3np7ANCgkJICovDQoJCWhhbmRsZVN1cHBsaWVyQ2hhbmdlKHN1cHBsaWVyTmFtZSkgew0KCQkJdGhpcy5pc1Jlc3BvbnNlVmlzaWJsZSA9IHRydWUNCgkJCWlmIChPYmplY3Qua2V5cyh0aGlzLnNlbGVjdGVkU3VwcGxpZXIpLmxlbmd0aCAhPT0gMCkgew0KCQkJCXRoaXMuc2F2ZVJhdGluZ1RlbXAoKTsNCgkJCX0NCgkJCXRoaXMuc2VsZWN0ZWRTdXBwbGllciA9IHRoaXMuc3VwcGxpZXJPcHRpb25zLmZpbmQoaXRlbSA9PiBpdGVtLmJpZGRlck5hbWUgPT09IHN1cHBsaWVyTmFtZSkgfHwge307IC8vIOiOt+WPluW9k+WJjeS+m+W6lOWVhg0KCQkJdGhpcy5zdXBwbGllckZhY3RvclBhZ2VNYXAgPSB0aGlzLmZhY3RvcnNQYWdlTWFwW3RoaXMuc2VsZWN0ZWRTdXBwbGllci5iaWRkZXJJZF0gfHwge307IC8vIOiOt+WPluW9k+WJjeS+m+W6lOWVhuivhOWIhumhuemhteeggQ0KCQkJdGhpcy5sb2FkU3VwcGxpZXJGYWN0b3JEZXRhaWwoc3VwcGxpZXJOYW1lKTsgLy8g5Yqg6L295b2T5YmN5L6b5bqU5ZWG6K+E5YiG6aG56K+m5oOFDQoJCQl0aGlzLmxvYWRTdXBwbGllckNoZWNrUmVzdWx0KCk7IC8vIOWKoOi9veW9k+WJjeS+m+W6lOWVhuezu+e7n+WInemqjOe7k+aenA0KCQkJdGhpcy5zaG93UmVzcG9uc2VGaWxlKCk7IC8vIOaYvuekuuWTjeW6lOaWh+S7tg0KCQl9LA0KCQkvLyDliqDovb3lvZPliY3kvpvlupTllYbor4TliIbpobnor6bmg4UNCgkJbG9hZFN1cHBsaWVyRmFjdG9yRGV0YWlsKGJpZGRlck5hbWUpIHsNCgkJCXRoaXMuY2xlYXJSYXRpbmdTdGF0ZU1hcCgpOw0KCQkJDQoJCQljb25zdCBkZXRhaWxEYXRhID0gew0KCQkJCWV4cGVydFJlc3VsdElkOiB0aGlzLmV4cGVydEluZm8ucmVzdWx0SWQsDQoJCQkJcHJvamVjdElkOiB0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQsDQoJCQkJc2NvcmluZ01ldGhvZEl0ZW1JZDogdGhpcy4kcm91dGUucXVlcnkuc2NvcmluZ01ldGhvZEl0ZW1JZCwNCgkJCX07DQoJCQlnZXREZXRhaWxCeVBzeHgoZGV0YWlsRGF0YSkudGhlbigocmVzcG9uc2UpID0+IHsNCgkJCQlpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQoJCQkJCXRoaXMuZmFjdG9yRGV0YWlsTGlzdCA9IHJlc3BvbnNlLmRhdGE7DQoJCQkJCWNvbnN0IGZhY3RvciA9IHRoaXMuZmFjdG9yRGV0YWlsTGlzdC5maW5kKGl0ZW0gPT4gaXRlbS5iaWRkZXJOYW1lID09PSBiaWRkZXJOYW1lKT8uZXZhbEV4cGVydEV2YWx1YXRpb25EZXRhaWxzOw0KCQkJCQlpZiAoZmFjdG9yKSB7DQoJCQkJCQl0aGlzLnNldFJhdGluZ1N0YXRlTWFwQnlGYWN0b3IoZmFjdG9yKTsNCgkJCQkJfSBlbHNlIHsNCgkJCQkJCXRoaXMuY2xlYXJSYXRpbmdTdGF0ZU1hcCgpOw0KCQkJCQl9DQoJCQkJfSBlbHNlIHsNCgkJCQkJdGhpcy4kbWVzc2FnZS53YXJuaW5nKHJlc3BvbnNlLm1zZyk7DQoJCQkJfQ0KCQkJfSk7DQoJCX0sDQoJCS8vIOagueaNruivhOWIhuivpuaDheiuvue9ruivhOWIhumhueeKtuaAgQ0KCQlzZXRSYXRpbmdTdGF0ZU1hcEJ5RmFjdG9yKGZhY3Rvcikgew0KCQkJZmFjdG9yLmZvckVhY2goaXRlbSA9PiB7DQoJCQkJdGhpcy5yYXRpbmdTdGF0ZU1hcFtpdGVtLnNjb3JpbmdNZXRob2RVaXRlbUlkXS5yZWFzb24gPSBpdGVtLmV2YWx1YXRpb25SZW1hcms7DQoJCQkJdGhpcy5yYXRpbmdTdGF0ZU1hcFtpdGVtLnNjb3JpbmdNZXRob2RVaXRlbUlkXS5zdGF0ZSA9IGl0ZW0uZXZhbHVhdGlvblJlc3VsdDsNCgkJCX0pOw0KCQl9LA0KCQkvLyDliqDovb3lvZPliY3kvpvlupTllYbns7vnu5/liJ3pqoznu5PmnpwNCgkJbG9hZFN1cHBsaWVyQ2hlY2tSZXN1bHQoKSB7DQoJCQljb25zdCByZXZpZXdEYXRhID0gew0KCQkJCXByb2plY3RJZDogdGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkLA0KCQkJCWVudElkOiB0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVySWQsDQoJCQl9Ow0KCQkJcmVzRG9jUmV2aWV3RmFjdG9yc0RlY2lzaW9uKHJldmlld0RhdGEpLnRoZW4oKHJlcykgPT4gew0KCQkJCWlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQoJCQkJCXRoaXMuY2hlY2tSZXN1bHQgPSByZXMuZGF0YTsNCgkJCQl9DQoJCQl9KTsNCgkJfSwNCgkJLy8g5Yid5aeL5YyW6K+E5YiG6aG554q25oCB77yI5riF56m677yJDQoJCWNsZWFyUmF0aW5nU3RhdGVNYXAoKSB7DQoJCQlPYmplY3Qua2V5cyh0aGlzLnJhdGluZ1N0YXRlTWFwKS5mb3JFYWNoKGtleSA9PiB7DQoJCQkJdGhpcy5yYXRpbmdTdGF0ZU1hcFtrZXldLnN0YXRlID0gbnVsbDsNCgkJCQl0aGlzLnJhdGluZ1N0YXRlTWFwW2tleV0ucmVhc29uID0gIiI7DQoJCQl9KTsNCgkJfSwNCg0KCQkvLyA9PT09PT09PT09IOaWh+S7tuebuOWFsyA9PT09PT09PT09DQoJCS8qKg0KCQkgKiDmmL7npLrlk43lupTmlofku7YNCgkJICovDQoJCXNob3dSZXNwb25zZUZpbGUoKSB7DQoJCQlpZiAoT2JqZWN0LmtleXModGhpcy5zZWxlY3RlZFN1cHBsaWVyKS5sZW5ndGggPT09IDApIHsNCgkJCQl0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeS+m+W6lOWVhiIpOw0KCQkJfSBlbHNlIHsNCgkJCQl0aGlzLmFjdGl2ZUJ1dHRvbiA9ICdyZXNwb25zZSc7IC8vIOiuvue9ruW9k+WJjea/gOa0u+aMiemSrg0KCQkJCXRoaXMuaXNEb3VibGVWaWV3ID0gZmFsc2U7IC8vIOWFs+mXreWvueavlA0KCQkJCXRoaXMuaXNQcm9jdXJlbWVudFZpc2libGUgPSBmYWxzZTsgLy8g5YWz6Zet6YeH6LSt5paH5Lu2DQoJCQkJdGhpcy5pc1Jlc3BvbnNlVmlzaWJsZSA9IHRydWU7IC8vIOaYvuekuuWTjeW6lOaWh+S7tg0KCQkJCQ0KCQkJCS8vIPCfjq8g5Y+q5Zyo55So5oi354K55Ye75pe25omN6K6+572u5ZON5bqU5paH5Lu2VVJM77yM5byA5aeL5riy5p+TDQoJCQkJdGhpcy5yZXNwb25zZVBkZlVybCA9IHRoaXMuZmlsZUluZm8uZmlsZVt0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVySWRdOyAvLyDlk43lupTmlofku7YNCgkJCQkNCgkJCQkvLyDlj7Pkvqfor4TliIbpobnmmL7npLrkuLrlk43lupTmlofku7bnmoTor4TliIbpobkNCgkJCQlpZiAoT2JqZWN0LmtleXModGhpcy5zZWxlY3RlZFN1cHBsaWVyKS5sZW5ndGggIT09IDApIHsNCgkJCQkJdGhpcy5zYXZlUmF0aW5nVGVtcCgpOw0KCQkJCX0NCgkJCQl0aGlzLnNlbGVjdGVkU3VwcGxpZXIgPSB0aGlzLnN1cHBsaWVyT3B0aW9ucy5maW5kKGl0ZW0gPT4gaXRlbS5iaWRkZXJOYW1lID09PSB0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVyTmFtZSkgfHwge307IC8vIOiOt+WPluW9k+WJjeS+m+W6lOWVhg0KCQkJCXRoaXMuc3VwcGxpZXJGYWN0b3JQYWdlTWFwID0gdGhpcy5mYWN0b3JzUGFnZU1hcFt0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVySWRdIHx8IHt9OyAvLyDojrflj5blvZPliY3kvpvlupTllYbor4TliIbpobnpobXnoIENCgkJCQl0aGlzLmxvYWRTdXBwbGllckZhY3RvckRldGFpbCh0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVyTmFtZSk7IC8vIOWKoOi9veW9k+WJjeS+m+W6lOWVhuivhOWIhumhueivpuaDhQ0KCQkJCXRoaXMubG9hZFN1cHBsaWVyQ2hlY2tSZXN1bHQoKTsgLy8g5Yqg6L295b2T5YmN5L6b5bqU5ZWG57O757uf5Yid6aqM57uT5p6cDQoJCQl9DQoJCX0sDQoJCS8qKg0KCQkgKiDmlofku7blr7nmr5TmmL7npLoNCgkJICovDQoJCXNob3dGaWxlQ29udHJhc3QoKSB7DQoJCQlpZiAoT2JqZWN0LmtleXModGhpcy5zZWxlY3RlZFN1cHBsaWVyKS5sZW5ndGggPT09IDApIHsNCgkJCQl0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeS+m+W6lOWVhiIpOw0KCQkJfSBlbHNlIHsNCgkJCQl0aGlzLmFjdGl2ZUJ1dHRvbiA9ICdjb250cmFzdCc7IC8vIOiuvue9ruW9k+WJjea/gOa0u+aMiemSrg0KCQkJCXRoaXMuaXNEb3VibGVWaWV3ID0gdHJ1ZTsNCgkJCQl0aGlzLmlzUHJvY3VyZW1lbnRWaXNpYmxlID0gdHJ1ZTsNCgkJCQl0aGlzLmlzUmVzcG9uc2VWaXNpYmxlID0gdHJ1ZTsNCgkJCQkNCgkJCQkvLyDwn46vIOWPquWcqOeUqOaIt+eCueWHu+WvueavlOaXtuaJjeiuvue9ruWTjeW6lOaWh+S7tlVSTO+8jOW8gOWni+a4suafkw0KCQkJCXRoaXMucmVzcG9uc2VQZGZVcmwgPSB0aGlzLmZpbGVJbmZvLmZpbGVbdGhpcy5zZWxlY3RlZFN1cHBsaWVyLmJpZGRlcklkXTsgLy8g5ZON5bqU5paH5Lu2DQoJCQl9DQoJCX0sDQoJCS8qKg0KCQkgKiDmn6XnnIvph4fotK3mlofku7YNCgkJICovDQoJCXNob3dQcm9jdXJlbWVudEZpbGUoKSB7DQoJCQl0aGlzLmFjdGl2ZUJ1dHRvbiA9ICdwcm9jdXJlbWVudCc7IC8vIOiuvue9ruW9k+WJjea/gOa0u+aMiemSrg0KCQkJdGhpcy5pc0RvdWJsZVZpZXcgPSBmYWxzZTsNCgkJCXRoaXMuaXNSZXNwb25zZVZpc2libGUgPSBmYWxzZTsNCgkJCXRoaXMuaXNQcm9jdXJlbWVudFZpc2libGUgPSB0cnVlOw0KCQkJLy8g5Y+z5L6n6K+E5YiG6aG55pi+56S65Li66YeH6LSt5paH5Lu255qE6K+E5YiG6aG5DQoJCQlsZXQgcGFnZVByb2N1cmVtZW50QXJyID0gW107DQoJCQlmb3IgKGxldCBpdGVtIGluIHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlKXsNCgkJCQlwYWdlUHJvY3VyZW1lbnRBcnIucHVzaCh7DQoJCQkJCWl0ZW1OYW1lOiBpdGVtLA0KCQkJCQlqdW1wVG9QYWdlOiB0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZVtpdGVtXQ0KCQkJCX0pDQoJCQl9DQoJCQkNCgkJCWNvbnNvbGUubG9nKHRoaXMuc2NvcmluZ1N5c3RlbS51aXRlbXMpOw0KCQkJY29uc29sZS5sb2cocGFnZVByb2N1cmVtZW50QXJyKQ0KCQkJdGhpcy5wYWdlUHJvY3VyZW1lbnQgPSBbXTsNCgkJCWZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtcy5sZW5ndGg7aSsrKXsNCgkJCQlmb3IgKGxldCBqID0gMDsgaiA8IHBhZ2VQcm9jdXJlbWVudEFyci5sZW5ndGg7aisrKXsNCgkJCQkJaWYgKHRoaXMuc2NvcmluZ1N5c3RlbS51aXRlbXNbaV0uaXRlbU5hbWUgPT0gcGFnZVByb2N1cmVtZW50QXJyW2pdLml0ZW1OYW1lKXsNCgkJCQkJCXRoaXMucGFnZVByb2N1cmVtZW50LnB1c2goey4uLnRoaXMuc2NvcmluZ1N5c3RlbS51aXRlbXNbaV0sLi4ucGFnZVByb2N1cmVtZW50QXJyW2pdfSk7DQoJCQkJCX0NCgkJCQl9DQoJCQl9DQoJCQljb25zb2xlLmxvZyh0aGlzLnBhZ2VQcm9jdXJlbWVudCkNCgkJfSwNCg0KCQkvLyA9PT09PT09PT09IOmhtemdoui3s+i9rOebuOWFsyA9PT09PT09PT09DQoJCS8qKg0KCQkgKiDot7PovazliLDor4TliIbpobnlr7nlupTpobXnoIENCgkJICogQHBhcmFtIHtPYmplY3R9IGZhY3Rvckl0ZW0g6K+E5YiG6aG55a+56LGhDQoJCSAqLw0KCQlqdW1wVG9GYWN0b3JQYWdlKGZhY3Rvckl0ZW0pIHsNCgkJCS8vIOajgOafpVBERuaYr+WQpua4suafk+WujOaIkA0KCQkJaWYgKCF0aGlzLmNhbkp1bXBUb1BhZ2UoKSkgew0KCQkJCXRoaXMuJG1lc3NhZ2Uud2FybmluZygiUERG6aG16Z2i5q2j5Zyo5riy5p+T5Lit77yM6K+356iN5YCZ5YaN6K+VIik7DQoJCQkJcmV0dXJuOw0KCQkJfQ0KDQoJCQl0aGlzLnNlbGVjdGVkRmFjdG9yTm9kZSA9IGZhY3Rvckl0ZW07IC8vIOiuvue9ruW9k+WJjemAieS4reWboOWtkA0KDQoJCQkvLyDlpoLmnpzlj6rmmL7npLrph4fotK3mlofku7bvvIzkvb/nlKjph4fotK3mlofku7bpobXnoIHkv6Hmga8NCgkJCWlmICh0aGlzLmlzUHJvY3VyZW1lbnRWaXNpYmxlICYmICF0aGlzLmlzUmVzcG9uc2VWaXNpYmxlKSB7DQoJCQkJaWYgKCF0aGlzLnByb2N1cmVtZW50UGRmUmVuZGVyZWQpIHsNCgkJCQkJdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLph4fotK3mlofku7bmraPlnKjmuLLmn5PkuK3vvIzor7fnqI3lgJnlho3or5UiKTsNCgkJCQkJcmV0dXJuOw0KCQkJCX0NCgkJCQlpZiAoZmFjdG9ySXRlbS5qdW1wVG9QYWdlKSB7DQoJCQkJCXRoaXMuJHJlZnMucHJvY3VyZW1lbnQuc2tpcFBhZ2UoZmFjdG9ySXRlbS5qdW1wVG9QYWdlKTsgLy8g6YeH6LSt5paH5Lu26Lez6aG1DQoJCQkJfSBlbHNlIGlmICh0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZSAmJiB0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZVtmYWN0b3JJdGVtLml0ZW1OYW1lXSkgew0KCQkJCQl0aGlzLiRyZWZzLnByb2N1cmVtZW50LnNraXBQYWdlKHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlW2ZhY3Rvckl0ZW0uaXRlbU5hbWVdKTsgLy8g6YeH6LSt5paH5Lu26Lez6aG1DQoJCQkJfQ0KCQkJCXJldHVybjsNCgkJCX0NCg0KCQkJLy8g5aaC5p6c5pi+56S65ZON5bqU5paH5Lu25oiW5a+55q+U5qih5byP77yM6ZyA6KaB6YCJ5oup5L6b5bqU5ZWGDQoJCQlpZiAoIXRoaXMuc3VwcGxpZXJGYWN0b3JQYWdlTWFwIHx8IE9iamVjdC5rZXlzKHRoaXMuc3VwcGxpZXJGYWN0b3JQYWdlTWFwKS5sZW5ndGggPT09IDApIHsNCgkJCQl0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+WFiOmAieaLqeS+m+W6lOWVhiIpOyAvLyDmnKrpgInkvpvlupTllYbmj5DnpLoNCgkJCQlyZXR1cm47DQoJCQl9DQoNCgkJCS8vIOi3s+i9rOWIsOWTjeW6lOaWh+S7tuWvueW6lOmhteeggQ0KCQkJaWYgKHRoaXMuaXNSZXNwb25zZVZpc2libGUgJiYgdGhpcy4kcmVmcy5yZXNwb25zZSkgew0KCQkJCWlmICghdGhpcy5yZXNwb25zZVBkZlJlbmRlcmVkKSB7DQoJCQkJCXRoaXMuJG1lc3NhZ2Uud2FybmluZygi5ZON5bqU5paH5Lu25q2j5Zyo5riy5p+T5Lit77yM6K+356iN5YCZ5YaN6K+VIik7DQoJCQkJCXJldHVybjsNCgkJCQl9DQoJCQkJdGhpcy4kcmVmcy5yZXNwb25zZS5za2lwUGFnZSh0aGlzLnN1cHBsaWVyRmFjdG9yUGFnZU1hcFt0aGlzLnNlbGVjdGVkRmFjdG9yTm9kZS5pdGVtTmFtZV0pOyAvLyDlk43lupTmlofku7bot7PpobUNCgkJCX0NCg0KCQkJLy8g6Lez6L2s5Yiw6YeH6LSt5paH5Lu25a+55bqU6aG156CBDQoJCQlpZiAodGhpcy5pc1Byb2N1cmVtZW50VmlzaWJsZSAmJiB0aGlzLiRyZWZzLnByb2N1cmVtZW50KSB7DQoJCQkJaWYgKCF0aGlzLnByb2N1cmVtZW50UGRmUmVuZGVyZWQpIHsNCgkJCQkJdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLph4fotK3mlofku7bmraPlnKjmuLLmn5PkuK3vvIzor7fnqI3lgJnlho3or5UiKTsNCgkJCQkJcmV0dXJuOw0KCQkJCX0NCgkJCQkvLyDlnKjlr7nmr5TmqKHlvI/kuIvvvIzph4fotK3mlofku7blupTor6Xot7PovazliLDph4fotK3mlofku7bnmoTlr7nlupTpobXnoIHvvIzogIzkuI3mmK/kvpvlupTllYbnmoTpobXnoIENCgkJCQlpZiAodGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2UgJiYgdGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2VbZmFjdG9ySXRlbS5pdGVtTmFtZV0pIHsNCgkJCQkJdGhpcy4kcmVmcy5wcm9jdXJlbWVudC5za2lwUGFnZSh0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZVtmYWN0b3JJdGVtLml0ZW1OYW1lXSk7IC8vIOmHh+i0reaWh+S7tui3s+mhtQ0KCQkJCX0gZWxzZSB7DQoJCQkJCS8vIOWcqOWvueavlOaooeW8j+S4i++8jOWmguaenOayoeaciemHh+i0reaWh+S7tumhteeggeS/oeaBr++8jOWImeWPqui3s+i9rOWTjeW6lOaWh+S7tueahOmhteegge+8jOS4jei3s+i9rOmHh+i0reaWh+S7tg0KCQkJCQkvLyDov5nmoLflj6/ku6Xpgb/lhY3ph4fotK3mlofku7blkozlk43lupTmlofku7bmmL7npLrkuI3lkIznmoTlhoXlrrnpgKDmiJDmt7fmt4YNCgkJCQl9DQoJCQl9DQoJCX0sDQoNCgkJLyoqDQoJCSAqIOajgOafpeaYr+WQpuWPr+S7pei3s+i9rOmhtemdog0KCQkgKiBAcmV0dXJucyB7Ym9vbGVhbn0g5piv5ZCm5Y+v5Lul6Lez6L2sDQoJCSAqLw0KCQljYW5KdW1wVG9QYWdlKCkgew0KCQkJLy8g5aaC5p6c5Y+q5pi+56S66YeH6LSt5paH5Lu2DQoJCQlpZiAodGhpcy5pc1Byb2N1cmVtZW50VmlzaWJsZSAmJiAhdGhpcy5pc1Jlc3BvbnNlVmlzaWJsZSkgew0KCQkJCXJldHVybiB0aGlzLnByb2N1cmVtZW50UGRmUmVuZGVyZWQ7DQoJCQl9DQoJCQkvLyDlpoLmnpzlj6rmmL7npLrlk43lupTmlofku7YNCgkJCWlmICh0aGlzLmlzUmVzcG9uc2VWaXNpYmxlICYmICF0aGlzLmlzUHJvY3VyZW1lbnRWaXNpYmxlKSB7DQoJCQkJcmV0dXJuIHRoaXMucmVzcG9uc2VQZGZSZW5kZXJlZDsNCgkJCX0NCgkJCS8vIOWmguaenOWvueavlOaooeW8j++8iOS4pOS4qumDveaYvuekuu+8iQ0KCQkJaWYgKHRoaXMuaXNSZXNwb25zZVZpc2libGUgJiYgdGhpcy5pc1Byb2N1cmVtZW50VmlzaWJsZSkgew0KCQkJCXJldHVybiB0aGlzLnJlc3BvbnNlUGRmUmVuZGVyZWQgJiYgdGhpcy5wcm9jdXJlbWVudFBkZlJlbmRlcmVkOw0KCQkJfQ0KCQkJcmV0dXJuIGZhbHNlOw0KCQl9LA0KDQoJCS8qKg0KCQkgKiDlpITnkIZQREbmuLLmn5PnirbmgIHlj5jljJYNCgkJICogQHBhcmFtIHtib29sZWFufSBpc1JlbmRlcmVkIOaYr+WQpua4suafk+WujOaIkA0KCQkgKiBAcGFyYW0ge3N0cmluZ30gcGRmVHlwZSBQREbnsbvlnovvvJoncmVzcG9uc2UnIOaIliAncHJvY3VyZW1lbnQnDQoJCSAqLw0KCQloYW5kbGVQZGZSZW5kZXJTdGF0dXNDaGFuZ2UoaXNSZW5kZXJlZCwgcGRmVHlwZSkgew0KCQkJaWYgKHBkZlR5cGUgPT09ICdyZXNwb25zZScpIHsNCgkJCQl0aGlzLnJlc3BvbnNlUGRmUmVuZGVyZWQgPSBpc1JlbmRlcmVkOw0KCQkJfSBlbHNlIGlmIChwZGZUeXBlID09PSAncHJvY3VyZW1lbnQnKSB7DQoJCQkJdGhpcy5wcm9jdXJlbWVudFBkZlJlbmRlcmVkID0gaXNSZW5kZXJlZDsNCgkJCX0NCgkJCQ0KCQkJaWYgKGlzUmVuZGVyZWQpIHsNCgkJCQljb25zb2xlLmxvZyhgJHtwZGZUeXBlID09PSAncmVzcG9uc2UnID8gJ+WTjeW6lCcgOiAn6YeH6LStJ33mlofku7bmuLLmn5PlrozmiJDvvIzlj6/ku6Xov5vooYzpobXpnaLot7PovaxgKTsNCgkJCX0NCgkJfSwNCgkJLyoqDQoJCSAqIOi3s+i9rOWIsOS6jOasoeaKpeS7tw0KCQkgKi8NCgkJZ29Ub1NlY29uZE9mZmVyKCkgew0KCQkJY29uc3QgcXVlcnkgPSB7DQoJCQkJcHJvamVjdElkOiB0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQsDQoJCQkJempobTogdGhpcy4kcm91dGUucXVlcnkuempobSwNCgkJCQlzY29yaW5nTWV0aG9kSXRlbUlkOiBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJ0ZW5kZXJPZmZlclNjb3JpbmdNZXRob2RJdGVtcyIpKSwNCgkJCX07DQoJCQl0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6ICIvc2Vjb25kT2ZmZXIiLCBxdWVyeSB9KTsNCgkJfSwNCgkJLyoqDQoJCSAqIOi3s+i9rOWIsOivouaghw0KCQkgKi8NCgkJZ29Ub0JpZElucXVpcnkoKSB7DQoJCQljb25zdCBxdWVyeSA9IHsNCgkJCQlwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwNCgkJCQl6amhtOiB0aGlzLiRyb3V0ZS5xdWVyeS56amhtLA0KCQkJCXNjb3JpbmdNZXRob2RJdGVtSWQ6IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oInRlbmRlck9mZmVyU2NvcmluZ01ldGhvZEl0ZW1zIikpLA0KCQkJfTsNCgkJCXRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogIi9iaWRJbnF1aXJ5IiwgcXVlcnkgfSk7DQoJCX0sDQoJCS8qKg0KCQkgKiDojrflj5blm6DntKDlr7nlupTpobXnoIENCgkJICovDQoJCWxvYWRGYWN0b3JzUGFnZU1hcCgpIHsNCgkJCXRoaXMuZmFjdG9yc1BhZ2VNYXAgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJlbnREb2NSZXNwb25zZVBhZ2UiKSk7DQoJCX0sDQoNCgkJLyoqDQoJCSAqIOS4i+i9veaWh+S7tg0KCQkgKiBAcGFyYW0ge09iamVjdH0gaXRlbSAtIOaWh+S7tuWvueixoQ0KCQkgKi8NCgkJZG93bmxvYWRGaWxlKGl0ZW0pIHsNCgkJCXRoaXMuJGRvd25sb2FkLnppcChpdGVtLmZpbGVQYXRoLCBpdGVtLmZpbGVOYW1lKTsNCgkJfSwNCg0KCQkvLyA9PT09PT09PT09IOaCrOWBnOebuOWFsyA9PT09PT09PT09DQoJCS8qKg0KCQkgKiDmmL7npLror4TliIbpobnmgqzmta7moYYNCgkJICogQHBhcmFtIHtPYmplY3R9IGZhY3Rvckl0ZW0g6K+E5YiG6aG55a+56LGhDQoJCSAqLw0KCQlzaG93RmFjdG9yVG9vbHRpcChmYWN0b3JJdGVtKSB7DQoJCQlpZiAoIWZhY3Rvckl0ZW0uaXRlbVJlbWFyaykgcmV0dXJuOyAvLyDlpoLmnpzmsqHmnInor4TlrqHlhoXlrrnliJnkuI3mmL7npLoNCg0KCQkJLy8g5riF6Zmk5LmL5YmN55qE5a6a5pe25ZmoDQoJCQlpZiAodGhpcy50b29sdGlwVGltZXIpIHsNCgkJCQljbGVhclRpbWVvdXQodGhpcy50b29sdGlwVGltZXIpOw0KCQkJfQ0KDQoJCQkvLyDlu7bov5/mmL7npLrmgqzmta7moYbvvIzpgb/lhY3lv6vpgJ/np7vliqjml7bpopHnuYHmmL7npLoNCgkJCXRoaXMudG9vbHRpcFRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7DQoJCQkJdGhpcy5ob3ZlcmVkRmFjdG9yTm9kZSA9IGZhY3Rvckl0ZW07DQoJCQl9LCAzMDApOyAvLyAzMDBtc+W7tui/nw0KCQl9LA0KDQoJCS8qKg0KCQkgKiDpmpDol4/or4TliIbpobnmgqzmta7moYYNCgkJICovDQoJCWhpZGVGYWN0b3JUb29sdGlwKCkgew0KCQkJLy8g5riF6Zmk5a6a5pe25ZmoDQoJCQlpZiAodGhpcy50b29sdGlwVGltZXIpIHsNCgkJCQljbGVhclRpbWVvdXQodGhpcy50b29sdGlwVGltZXIpOw0KCQkJCXRoaXMudG9vbHRpcFRpbWVyID0gbnVsbDsNCgkJCX0NCg0KCQkJLy8g5bu26L+f6ZqQ6JeP77yM57uZ55So5oi35pe26Ze056e75Yqo5Yiw5oKs5rWu5qGG5LiKDQoJCQlzZXRUaW1lb3V0KCgpID0+IHsNCgkJCQl0aGlzLmhvdmVyZWRGYWN0b3JOb2RlID0gbnVsbDsNCgkJCX0sIDEwMCk7DQoJCX0sDQoNCgkJLyoqDQoJCSAqIOa4hemZpOaCrOa1ruahhuWumuaXtuWZqO+8iOW9k+m8oOagh+enu+WKqOWIsOaCrOa1ruahhuS4iuaXtu+8iQ0KCQkgKi8NCgkJY2xlYXJUb29sdGlwVGltZXIoKSB7DQoJCQlpZiAodGhpcy50b29sdGlwVGltZXIpIHsNCgkJCQljbGVhclRpbWVvdXQodGhpcy50b29sdGlwVGltZXIpOw0KCQkJCXRoaXMudG9vbHRpcFRpbWVyID0gbnVsbDsNCgkJCX0NCgkJfQ0KCQ0KfSwNCg0KCW1vdW50ZWQoKSB7DQoJCXRoaXMuaW5pdFBhZ2UoKTsNCgkJdGhpcy5sb2FkRmFjdG9yc1BhZ2VNYXAoKTsNCgl9LA0KDQoJYmVmb3JlRGVzdHJveSgpIHsNCgkJLy8g5riF55CG5a6a5pe25ZmoDQoJCWlmICh0aGlzLnRvb2x0aXBUaW1lcikgew0KCQkJY2xlYXJUaW1lb3V0KHRoaXMudG9vbHRpcFRpbWVyKTsNCgkJCXRoaXMudG9vbHRpcFRpbWVyID0gbnVsbDsNCgkJfQ0KCX0sDQp9Ow0K"}, {"version": 3, "sources": ["one.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+MA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "one.vue", "sourceRoot": "src/views/expertReview/qualification", "sourcesContent": ["<template>\r\n\t<div class=\"main-container-one\">\r\n\t\t<div class=\"left-panel\">\r\n\t\t\t<div class=\"header-bar\">\r\n\t\t\t\t<div class=\"header-title\">\r\n\t\t\t\t\t<div>资格性评审</div>\r\n\t\t\t\t\t<div class=\"header-steps\">\r\n\t\t\t\t\t\t<div class=\"steps-tip\">该页面操作说明</div>\r\n\t\t\t\t\t\t<el-image class=\"steps-img\" :src=\"helpImageList[0]\" :preview-src-list=\"helpImageList\">\r\n\t\t\t\t\t\t</el-image>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 文件列表 -->\r\n\t\t\t\t<div class=\"fileList\" style=\"width: 200px; border-right: 1px solid #e6e6e6; border-left: 1px solid #e6e6e6; padding: 10px; overflow-y: auto;\">\r\n\t\t\t\t\t<div style=\"font-weight: bold; margin-bottom: 10px; color: #333;\">响应文件附件下载</div>\r\n\t\t\t\t\t<el-card\r\n\t\t\t\t\t\tv-for=\"(item, index) in attachmentsList\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\tclass=\"fileItem\"\r\n\t\t\t\t\t\tshadow=\"hover\"\r\n\t\t\t\t\t\**************=\"downloadFile(item)\"\r\n\t\t\t\t\t\tstyle=\"margin-bottom: 8px; cursor: pointer;\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div style=\"display: flex; align-items: center; padding: 5px;\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-document\" style=\"margin-right: 8px; color: #409EFF;\"></i>\r\n\t\t\t\t\t\t\t<span style=\"font-size: 12px; flex: 1; word-break: break-all;\">{{ item.fileName }}</span>\r\n\t\t\t\t\t\t\t<i class=\"el-icon-download\" style=\"margin-left: 8px; color: #999;\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-card>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div class=\"header-btns\">\r\n\t\t\t\t\t<el-button class=\"item-button\" @click=\"goToBidInquiry\">询标</el-button>\r\n\t\t\t\t\t<!-- <el-button class=\"item-button\" @click=\"secondOffer\">发起二次报价</el-button> -->\r\n\t\t\t\t\t<div class=\"header-btns-group\">\r\n\t\t\t\t\t\t<el-button\r\n\t\t\t\t\t\t\t:class=\"['item-button', activeButton === 'procurement' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']\"\r\n\t\t\t\t\t\t\t@click=\"showProcurementFile\">采购文件</el-button>\r\n\t\t\t\t\t\t<el-button\r\n\t\t\t\t\t\t\t:class=\"['item-button', activeButton === 'response' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']\"\r\n\t\t\t\t\t\t\t@click=\"showResponseFile\">响应文件</el-button>\r\n\t\t\t\t\t\t<el-button\r\n\t\t\t\t\t\t\t:class=\"['item-button', activeButton === 'contrast' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']\"\r\n\t\t\t\t\t\t\t@click=\"showFileContrast\">对比</el-button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div style=\"height:82%\">\r\n\t\t\t\t<!-- PDF预览区域 -->\r\n\t\t\t\t<div class=\"pdf-container\">\r\n\t\t\t\t\t<div v-show=\"isProcurementVisible\" :class=\"['pdf-view', { 'border-right': isDoubleView }]\">\r\n<!--\t\t\t\t\t\t<pdfView ref=\"procurement\" :pdfurl=\"procurementPdfUrl\" :uni_key=\"'procurement'\"></pdfView>-->\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<PdfViewImproved\r\n\t\t\t\t\t\t\tref=\"procurement\"\r\n\t\t\t\t\t\t\t:pdfurl=\"procurementPdfUrl\"\r\n\t\t\t\t\t\t\t:page-height=\"800\"\r\n\t\t\t\t\t\t\t:buffer-size=\"2\"\r\n\t\t\t\t\t\t\t@render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div v-show=\"isResponseVisible\" :class=\"['pdf-view', { 'border-left': isDoubleView }]\">\r\n<!--\t\t\t\t\t\t<pdfView ref=\"response\" :pdfurl=\"responsePdfUrl\" :uni_key=\"'response'\"></pdfView>-->\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<PdfViewImproved\r\n\t\t\t\t\t\t\tref=\"response\"\r\n\t\t\t\t\t\t\t:pdfurl=\"responsePdfUrl\"\r\n\t\t\t\t\t\t\t:page-height=\"800\"\r\n\t\t\t\t\t\t\t:buffer-size=\"2\"\r\n\t\t\t\t\t\t\t@render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"divider\"></div>\r\n\t\t<div class=\"right-panel\">\r\n\t\t\t<div class=\"right-header\">\r\n\t\t\t\t<el-select style=\"width:100%\" v-model=\"selectedSupplierName\" placeholder=\"请选择供应商\" @change=\"handleSupplierChange\">\r\n\t\t\t\t\t<el-option v-for=\"item in supplierOptions\" :key=\"item.bidderName\" :label=\"item.bidderName\" :value=\"item.bidderName\">\r\n\t\t\t\t\t</el-option>\r\n\t\t\t\t</el-select>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"right-content\" v-if=\"isResponseVisible\">\r\n\t\t\t\t<!-- PDF渲染状态提示 -->\r\n\t\t\t\t<div v-if=\"!responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t\t\t\t<i class=\"el-icon-loading\"></i>\r\n\t\t\t\t\t<span>响应文件正在渲染中，请稍候...</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div v-else class=\"render-status-tip success\">\r\n\t\t\t\t\t<i class=\"el-icon-success\"></i>\r\n\t\t\t\t\t<span>响应文件渲染完成，可以点击跳转</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div v-for=\"(item, index) in scoringSystem.uitems\" :key=\"index\" class=\"factor-item\"\r\n\t\t\t\t\t@mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t<!-- 悬浮框 -->\r\n\t\t\t\t\t<div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t\t\t\tclass=\"factor-tooltip\"\r\n\t\t\t\t\t\t@mouseenter=\"clearTooltipTimer\"\r\n\t\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t\t<div class=\"tooltip-header\">\r\n\t\t\t\t\t\t\t<div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t\t\t<i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<div class=\"factors\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclass=\"factor-title\"\r\n\t\t\t\t\t\t\t\t:class=\"{ 'disabled': !canJumpToPage() }\"\r\n\t\t\t\t\t\t\t\t@click=\"jumpToFactorPage(item)\">\r\n\t\t\t\t\t\t\t\t{{ item.itemName }}\r\n\t\t\t\t\t\t\t\t<i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"factor-radio-group\">\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<el-input v-if=\"(ratingStateMap[item.entMethodItemId].state == 0)\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n\t\t\t\t\t\t</el-input>\r\n\t\t\t\t\t\t<span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n\t\t\t\t\t\t\t{{checkResultLabel[item.itemName]}}</span>\r\n\t\t\t\t\t\t<div class=\"factor-divider\"></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"right-btns\">\r\n\t\t\t\t\t<!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"save\">保存</el-button></div> -->\r\n\t\t\t\t\t<div><el-button class=\"item-button-little\" @click=\"submitRating\">提交</el-button></div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"review-content\">\r\n\t\t\t\t\t<div class=\"review-title\">评审内容：</div>\r\n\t\t\t\t\t<div class=\"review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<div class=\"right-content\" v-else>\r\n\t\t\t\t<!-- PDF渲染状态提示 -->\r\n\t\t\t\t<div v-if=\"!procurementPdfRendered\" class=\"render-status-tip\">\r\n\t\t\t\t\t<i class=\"el-icon-loading\"></i>\r\n\t\t\t\t\t<span>采购文件正在渲染中，请稍候...</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div v-else class=\"render-status-tip success\">\r\n\t\t\t\t\t<i class=\"el-icon-success\"></i>\r\n\t\t\t\t\t<span>采购文件渲染完成，可以点击跳转</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div v-for=\"(item, index) in pageProcurement\" :key=\"index\" class=\"factor-item\"\r\n\t\t\t\t\t@mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t<!-- 悬浮框 -->\r\n\t\t\t\t\t<div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t\t\t\tclass=\"factor-tooltip\"\r\n\t\t\t\t\t\t@mouseenter=\"clearTooltipTimer\"\r\n\t\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t\t<div class=\"tooltip-header\">\r\n\t\t\t\t\t\t\t<div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t\t\t<i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<div class=\"factors\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclass=\"factor-title\"\r\n\t\t\t\t\t\t\t\t:class=\"{ 'disabled': !canJumpToPage() }\"\r\n\t\t\t\t\t\t\t\t@click=\"jumpToFactorPage(item)\">\r\n\t\t\t\t\t\t\t\t{{ item.itemName }}\r\n\t\t\t\t\t\t\t\t<i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"factor-radio-group\">\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<el-input v-if=\"(ratingStateMap[item.entMethodItemId].state == 0)\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n\t\t\t\t\t\t</el-input>\r\n\t\t\t\t\t\t<span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n\t\t\t\t\t\t\t{{checkResultLabel[item.itemName]}}</span>\r\n\t\t\t\t\t\t<div class=\"factor-divider\"></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"right-btns\">\r\n\t\t\t\t\t<!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"save\">保存</el-button></div> -->\r\n\t\t\t\t\t<div><el-button class=\"item-button-little\" @click=\"submitRating\">提交</el-button></div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"review-content\">\r\n\t\t\t\t\t<div class=\"review-title\">评审内容：</div>\r\n\t\t\t\t\t<div class=\"review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n\tsupplierInfo,\r\n\tapprovalProcess,\r\n\tscoringFactors,\r\n\tcheckReviewSummary,\r\n\tfilesById,\r\n} from \"@/api/expert/review\";\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\";\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\r\nimport { resDocReviewFactorsDecision } from \"@/api/docResponse/entInfo\";\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tsupplierOptions: [], // 供应商下拉选项\r\n\t\t\tscoringSystem: [], // 当前评分体系\r\n\t\t\tselectedFactorNode: {}, // 当前选中评分项\r\n\t\t\tselectedSupplierName: \"\", // 当前选中供应商名称\r\n\t\t\tselectedSupplier: {}, // 当前选中供应商对象\r\n\t\t\texpertInfo: {}, // 专家信息\r\n\t\t\tratingStateMap: {}, // 评分项状态与原因\r\n\t\t\tfileInfo: {}, // 文件信息\r\n\t\t\tisResponseVisible: false, // 是否显示响应文件\r\n\t\t\tisProcurementVisible: false, // 是否显示采购文件\r\n\t\t\tisDoubleView: false, // 是否对比显示\r\n\t\t\t\r\n\t\t\tentDocResponsePage: {}, // 响应文件页码信息\r\n\t\t\t\r\n\t\t\tfactorDetailList: [], // 评分项详情\r\n\t\t\tfactorsPageMap: {}, // 评分项页码信息\r\n\t\t\t\r\n\t\t\tentDocProcurementPage: {}, // 采购文件页码信息\r\n\t\t\tpageProcurement:[], // 采购文件的评分项\r\n\t\t\tattachmentsList: [], // 文件列表\r\n\t\t\t\r\n\t\t\tsupplierFactorPageMap: {}, // 当前供应商评分项页码\r\n\t\t\tresponsePdfUrl: null, // 响应文件PDF路径\r\n\t\t\tprocurementPdfUrl: null, // 采购文件PDF路径\r\n\r\n\t\t\t// 按钮状态管理\r\n\t\t\tactiveButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n\t\t\t// PDF渲染状态管理\r\n\t\t\tresponsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n\t\t\tprocurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n\t\t\thelpImageList: [\"/evalution/help.jpg\"], // 步骤图片\r\n\t\t\tfactorKeyMap: { // 评分项与后端字段映射\r\n\t\t\t\t\"特定资格要求\": \"zgzs\",\r\n\t\t\t\t\"响应内容\": \"jsplb\",\r\n\t\t\t\t\"采购需求\": \"jsplb\",\r\n\t\t\t\t\"供货期限\": \"ghqx\",\r\n\t\t\t\t\"投标报价\": \"tbbj\"\r\n\t\t\t},\r\n\t\t\tcheckResult: {}, // 系统初验结果\r\n\t\t\tcheckResultLabel: { // 系统初验结果名称\r\n\t\t\t\t\"符合《中华人民共和国政府采购法》第二十二条规定\": \"系统初验通过\",\r\n\t\t\t\t\"特定资格要求\": \"系统初验通过\",\r\n\t\t\t\t\"信用查询\": \"系统初验通过\",\r\n\t\t\t\t\"响应人名称\": \"系统初验通过\",\r\n\t\t\t\t\"响应内容\": \"系统初验通过\",\r\n\t\t\t\t\"采购需求\": \"系统初验通过\",\r\n\t\t\t\t\"供货期限\": \"系统初验通过\",\r\n\t\t\t\t\"投标报价\": \"系统初验通过\"\r\n\t\t\t},\r\n\r\n\t\t\t// 悬停状态管理\r\n\t\t\thoveredFactorNode: null, // 悬停时的评分项\r\n\t\t\ttooltipTimer: null, // 悬浮框显示定时器\r\n\t\t};\r\n\t},\r\n\r\n\tmethods: {\r\n\t\t// ========== 评分相关 ==========\r\n\t\t/**\r\n\t\t * 系统初验结果判断\r\n\t\t * @param {string} factorName 评分项名称\r\n\t\t * @returns {string} 1-通过 0-未通过\r\n\t\t */\r\n\t\tgetCheckResultState(factorName) {\r\n\t\t\tif (!this.checkResult || Object.keys(this.checkResult).length === 0) return \"\"; // 如果没有系统初验结果，则返回空\r\n\t\t\tlet state = \"1\";\r\n\t\t\tconst key = this.factorKeyMap[factorName];\r\n\t\t\tif (key) {\r\n\t\t\t\tstate = this.checkResult[key];\r\n\t\t\t\tif (factorName === \"投标报价\" && state === \"1\") {\r\n\t\t\t\t\tstate = this.checkResult[\"mxbjb\"];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (state === \"0\") {\r\n\t\t\t\tthis.checkResultLabel[factorName] = \"系统初验未通过\";\r\n\t\t\t} else {\r\n\t\t\t\tstate = \"1\";\r\n\t\t\t\tthis.checkResultLabel[factorName] = \"系统初验通过\";\r\n\t\t\t}\r\n\t\t\treturn state;\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 校验所有评分项是否填写完整\r\n\t\t * @returns {boolean} 是否全部填写\r\n\t\t */\r\n\t\tvalidateAllRatings() {\r\n\t\t\tfor (const item of this.scoringSystem.uitems) {\r\n\t\t\t\tconst state = this.ratingStateMap[item.entMethodItemId].state;\r\n\t\t\t\tconst reason = this.ratingStateMap[item.entMethodItemId].reason;\r\n\t\t\t\t// 评分结果未填写\r\n\t\t\t\tif (state === null || state === '') {\r\n\t\t\t\t\t// this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t\t// 不通过但未填写原因\r\n\t\t\t\tif (state === \"0\" && (!reason || reason.trim() === '')) {\r\n\t\t\t\t\tthis.$message.warning(`${item.itemName}评审不通过但未填写备注，不进行保存`);\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t},\r\n\r\n\t\t// 生成保存数据\r\n\t\tgenerateSaveData() {\r\n\t\t\tconst ratingCopy = JSON.parse(JSON.stringify(this.ratingStateMap)); // 评分项状态\r\n\t\t\tconst data = []; // 保存数据\r\n\t\t\tfor (const item of this.scoringSystem.uitems) { // 遍历评分项\r\n\t\t\t\tconst itemId = item.entMethodItemId; // 评分项ID\r\n\t\t\t\tconst evaluationResult = ratingCopy[itemId].state; // 评分项状态\r\n\t\t\t\tif (evaluationResult === null || evaluationResult === \"\") continue; // 如果评分项状态为空，则跳过\r\n\t\t\t\t// 注意：不通过原因的校验已经在validateAllRatings中处理，这里只需要构建数据\r\n\t\t\t\tconst evaluationRemark = ratingCopy[itemId].reason || \"\"; // 评分项备注\r\n\t\t\t\tdata.push({\r\n\t\t\t\t\tscoringMethodUitemId: itemId, // 评分项ID\r\n\t\t\t\t\texpertResultId: this.expertInfo.resultId, // 专家ID\r\n\t\t\t\t\tentId: this.selectedSupplier.bidderId, // 供应商ID\r\n\t\t\t\t\tevaluationResult, // 评分项状态\r\n\t\t\t\t\tevaluationRemark // 评分项备注\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\treturn data; // 返回保存数据\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 临时保存评分结果\r\n\t\t * @returns {Promise}\r\n\t\t */\r\n\t\tsaveRatingTemp() {\r\n\t\t\t// 先校验所有评分项是否填写完整\r\n\t\t\tif (!this.validateAllRatings()) {\r\n\t\t\t\treturn Promise.resolve({ code: 0, success: false }); // 校验失败\r\n\t\t\t}\r\n\r\n\t\t\tconst data = this.generateSaveData(); // 生成保存数据\r\n\t\t\tif (data.length > 0) {\r\n\t\t\t\treturn scoringFactors(data).then(response => {\r\n\t\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t\tthis.$message.success(\"保存成功\");\r\n\t\t\t\t\t\treturn { code: 200, success: true };\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t\t\treturn { code: response.code, success: false };\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch((error) => {\r\n\t\t\t\t\tthis.$message.error(\"保存失败\");\r\n\t\t\t\t\treturn { code: 0, success: false };\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\treturn Promise.resolve({ code: 200, success: true }); // 没有数据需要保存时也返回成功\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 提交评分结果\r\n\t\t */\r\n\t\tsubmitRating() {\r\n\t\t\tthis.saveRatingTemp().then((saveResult) => {\r\n\t\t\t\t// 检查保存结果，如果校验失败则不继续提交\r\n\t\t\t\tif (!saveResult || saveResult.success === false) {\r\n\t\t\t\t\treturn; // 校验失败，不继续提交流程\r\n\t\t\t\t}\r\n\t\t\t\tthis.checkAndSubmitReview();\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 检查并提交评审汇总\r\n\t\tcheckAndSubmitReview() {\r\n\t\t\t\tconst data = {\r\n\t\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\t\texpertResultId: this.expertInfo.resultId,\r\n\t\t\t\t\tscoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n\t\t\t\t};\r\n\t\t\t\tcheckReviewSummary(data).then((response) => {\r\n\t\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t\tthis.updateExpertScoreStatus(); // 修改专家进度状态\r\n\t\t\t\t\t\tthis.$emit(\"send\", \"two\"); // 跳转至二次报价\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 修改专家进度状态\r\n\t\tupdateExpertScoreStatus() {\r\n\t\t\tconst status = {\r\n\t\t\t\tevalExpertScoreInfoId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\")).evalExpertScoreInfoId,\r\n\t\t\t\tevalState: 1,\r\n\t\t\t};\r\n\t\t\teditEvalExpertScoreInfo(status).then((res) => {\r\n\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\tthis.$message.success(\"提交成功\");\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// ========== 初始化相关 ==========\r\n\t\t/**\r\n\t\t * 初始化页面数据\r\n\t\t */\r\n\t\tinitPage() {\r\n\t\t\tthis.initExpertInfo();// 初始化专家信息\r\n\t\t\tthis.initEntDocResponsePage(); // 初始化响应文件页码信息\r\n\t\t\tthis.initEntDocProcurementPage(); // 初始化采购文件页码信息\r\n\t\t\tthis.loadSupplierOptions();// 加载供应商下拉选项\r\n\t\t\tthis.loadScoringSystem(); // 加载评分体系\r\n\t\t\tthis.loadFiles(); // 加载文件信息\r\n\t\t},\r\n\t\t// 初始化专家信息\r\n\t\tinitExpertInfo() {\r\n\t\t\ttry {\r\n\t\t\t\tconst expertInfoStr = localStorage.getItem(\"expertInfo\");\r\n\t\t\t\tif (expertInfoStr) {\r\n\t\t\t\t\tthis.expertInfo = JSON.parse(expertInfoStr);\r\n\t\t\t\t\tconsole.log(\"专家信息已初始化\", this.expertInfo);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn(\"localStorage中未找到expertInfo\");\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"初始化专家信息失败:\", error);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 初始化响应文件页码信息\r\n\t\tinitEntDocResponsePage() {\r\n\t\t\tthis.entDocResponsePage = JSON.parse(localStorage.getItem(\"entDocResponsePage\"));\r\n\t\t},\r\n\t\t// 初始化采购文件页码信息\r\n\t\tinitEntDocProcurementPage() {\r\n\t\t\tthis.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\"));\r\n\t\t},\r\n\t\t// 加载供应商下拉选项\r\n\t\tloadSupplierOptions() {\r\n\t\t\tsupplierInfo({ projectId: this.$route.query.projectId }).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\tthis.supplierOptions = response.rows.filter(item => item.isAbandonedBid === 0); // 过滤掉被放弃的投标\r\n\t\t\t\t\tconsole.log(this.supplierOptions);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 加载评分体系及初始化评分项状态\r\n\t\tloadScoringSystem() {\r\n\t\t\tapprovalProcess(this.$route.query.projectId, this.expertInfo.resultId).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t// 文件列表\r\n\t\t\t\t\tthis.attachmentsList = response.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n\r\n\t\t\t\t\tthis.scoringSystem = response.data.scoringMethodUinfo.scoringMethodItems.find(\r\n\t\t\t\t\t\titem => item.scoringMethodItemId == this.$route.query.scoringMethodItemId\r\n\t\t\t\t\t); // 获取当前评分项\r\n\t\t\t\t\tlocalStorage.setItem(\r\n\t\t\t\t\t\t\"evalProjectEvaluationProcess\",\r\n\t\t\t\t\t\tJSON.stringify(this.scoringSystem.evalProjectEvaluationProcess)\r\n\t\t\t\t\t); // 保存评分体系\r\n\t\t\t\t\tconsole.log(this.scoringSystem);\r\n\t\t\t\t\tthis.initRatingStateMapBySystem(); // 初始化评分项状态\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 初始化评分项状态（根据评分体系）\r\n\t\tinitRatingStateMapBySystem() {\r\n\t\t\tthis.ratingStateMap = this.scoringSystem.uitems.reduce((acc, item) => {\r\n\t\t\t\tacc[item.entMethodItemId] = { state: null, reason: \"\" };\r\n\t\t\t\treturn acc;\r\n\t\t\t}, {}); // 初始化评分项状态\r\n\t\t},\r\n\t\t// 加载文件信息\r\n\t\tloadFiles() {\r\n\t\t\tfilesById(this.$route.query.projectId).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\tthis.fileInfo = response.data; // 文件信息\r\n\t\t\t\t\tif (this.fileInfo.tenderNoticeFilePath) {\r\n\t\t\t\t\t\tthis.procurementPdfUrl = this.fileInfo.tenderNoticeFilePath; // 采购文件\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 不自动设置响应文件URL，只在需要时才设置\r\n\t\t\t\t\t// if (this.fileInfo.file) {\r\n\t\t\t\t\t// \tthis.responsePdfUrl = this.fileInfo.file[0]; // 响应文件\r\n\t\t\t\t\t// }\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// ========== 供应商相关 ==========\r\n\t\t/**\r\n\t\t * 供应商切换时处理\r\n\t\t * @param {string} supplierName 供应商名称\r\n\t\t */\r\n\t\thandleSupplierChange(supplierName) {\r\n\t\t\tthis.isResponseVisible = true\r\n\t\t\tif (Object.keys(this.selectedSupplier).length !== 0) {\r\n\t\t\t\tthis.saveRatingTemp();\r\n\t\t\t}\r\n\t\t\tthis.selectedSupplier = this.supplierOptions.find(item => item.bidderName === supplierName) || {}; // 获取当前供应商\r\n\t\t\tthis.supplierFactorPageMap = this.factorsPageMap[this.selectedSupplier.bidderId] || {}; // 获取当前供应商评分项页码\r\n\t\t\tthis.loadSupplierFactorDetail(supplierName); // 加载当前供应商评分项详情\r\n\t\t\tthis.loadSupplierCheckResult(); // 加载当前供应商系统初验结果\r\n\t\t\tthis.showResponseFile(); // 显示响应文件\r\n\t\t},\r\n\t\t// 加载当前供应商评分项详情\r\n\t\tloadSupplierFactorDetail(bidderName) {\r\n\t\t\tthis.clearRatingStateMap();\r\n\t\t\t\r\n\t\t\tconst detailData = {\r\n\t\t\t\texpertResultId: this.expertInfo.resultId,\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tscoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n\t\t\t};\r\n\t\t\tgetDetailByPsxx(detailData).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\tthis.factorDetailList = response.data;\r\n\t\t\t\t\tconst factor = this.factorDetailList.find(item => item.bidderName === bidderName)?.evalExpertEvaluationDetails;\r\n\t\t\t\t\tif (factor) {\r\n\t\t\t\t\t\tthis.setRatingStateMapByFactor(factor);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.clearRatingStateMap();\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 根据评分详情设置评分项状态\r\n\t\tsetRatingStateMapByFactor(factor) {\r\n\t\t\tfactor.forEach(item => {\r\n\t\t\t\tthis.ratingStateMap[item.scoringMethodUitemId].reason = item.evaluationRemark;\r\n\t\t\t\tthis.ratingStateMap[item.scoringMethodUitemId].state = item.evaluationResult;\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 加载当前供应商系统初验结果\r\n\t\tloadSupplierCheckResult() {\r\n\t\t\tconst reviewData = {\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tentId: this.selectedSupplier.bidderId,\r\n\t\t\t};\r\n\t\t\tresDocReviewFactorsDecision(reviewData).then((res) => {\r\n\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\tthis.checkResult = res.data;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 初始化评分项状态（清空）\r\n\t\tclearRatingStateMap() {\r\n\t\t\tObject.keys(this.ratingStateMap).forEach(key => {\r\n\t\t\t\tthis.ratingStateMap[key].state = null;\r\n\t\t\t\tthis.ratingStateMap[key].reason = \"\";\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// ========== 文件相关 ==========\r\n\t\t/**\r\n\t\t * 显示响应文件\r\n\t\t */\r\n\t\tshowResponseFile() {\r\n\t\t\tif (Object.keys(this.selectedSupplier).length === 0) {\r\n\t\t\t\tthis.$message.warning(\"请选择供应商\");\r\n\t\t\t} else {\r\n\t\t\t\tthis.activeButton = 'response'; // 设置当前激活按钮\r\n\t\t\t\tthis.isDoubleView = false; // 关闭对比\r\n\t\t\t\tthis.isProcurementVisible = false; // 关闭采购文件\r\n\t\t\t\tthis.isResponseVisible = true; // 显示响应文件\r\n\t\t\t\t\r\n\t\t\t\t// 🎯 只在用户点击时才设置响应文件URL，开始渲染\r\n\t\t\t\tthis.responsePdfUrl = this.fileInfo.file[this.selectedSupplier.bidderId]; // 响应文件\r\n\t\t\t\t\r\n\t\t\t\t// 右侧评分项显示为响应文件的评分项\r\n\t\t\t\tif (Object.keys(this.selectedSupplier).length !== 0) {\r\n\t\t\t\t\tthis.saveRatingTemp();\r\n\t\t\t\t}\r\n\t\t\t\tthis.selectedSupplier = this.supplierOptions.find(item => item.bidderName === this.selectedSupplier.bidderName) || {}; // 获取当前供应商\r\n\t\t\t\tthis.supplierFactorPageMap = this.factorsPageMap[this.selectedSupplier.bidderId] || {}; // 获取当前供应商评分项页码\r\n\t\t\t\tthis.loadSupplierFactorDetail(this.selectedSupplier.bidderName); // 加载当前供应商评分项详情\r\n\t\t\t\tthis.loadSupplierCheckResult(); // 加载当前供应商系统初验结果\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 文件对比显示\r\n\t\t */\r\n\t\tshowFileContrast() {\r\n\t\t\tif (Object.keys(this.selectedSupplier).length === 0) {\r\n\t\t\t\tthis.$message.warning(\"请选择供应商\");\r\n\t\t\t} else {\r\n\t\t\t\tthis.activeButton = 'contrast'; // 设置当前激活按钮\r\n\t\t\t\tthis.isDoubleView = true;\r\n\t\t\t\tthis.isProcurementVisible = true;\r\n\t\t\t\tthis.isResponseVisible = true;\r\n\t\t\t\t\r\n\t\t\t\t// 🎯 只在用户点击对比时才设置响应文件URL，开始渲染\r\n\t\t\t\tthis.responsePdfUrl = this.fileInfo.file[this.selectedSupplier.bidderId]; // 响应文件\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 查看采购文件\r\n\t\t */\r\n\t\tshowProcurementFile() {\r\n\t\t\tthis.activeButton = 'procurement'; // 设置当前激活按钮\r\n\t\t\tthis.isDoubleView = false;\r\n\t\t\tthis.isResponseVisible = false;\r\n\t\t\tthis.isProcurementVisible = true;\r\n\t\t\t// 右侧评分项显示为采购文件的评分项\r\n\t\t\tlet pageProcurementArr = [];\r\n\t\t\tfor (let item in this.entDocProcurementPage){\r\n\t\t\t\tpageProcurementArr.push({\r\n\t\t\t\t\titemName: item,\r\n\t\t\t\t\tjumpToPage: this.entDocProcurementPage[item]\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconsole.log(this.scoringSystem.uitems);\r\n\t\t\tconsole.log(pageProcurementArr)\r\n\t\t\tthis.pageProcurement = [];\r\n\t\t\tfor (let i = 0; i < this.scoringSystem.uitems.length;i++){\r\n\t\t\t\tfor (let j = 0; j < pageProcurementArr.length;j++){\r\n\t\t\t\t\tif (this.scoringSystem.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n\t\t\t\t\t\tthis.pageProcurement.push({...this.scoringSystem.uitems[i],...pageProcurementArr[j]});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tconsole.log(this.pageProcurement)\r\n\t\t},\r\n\r\n\t\t// ========== 页面跳转相关 ==========\r\n\t\t/**\r\n\t\t * 跳转到评分项对应页码\r\n\t\t * @param {Object} factorItem 评分项对象\r\n\t\t */\r\n\t\tjumpToFactorPage(factorItem) {\r\n\t\t\t// 检查PDF是否渲染完成\r\n\t\t\tif (!this.canJumpToPage()) {\r\n\t\t\t\tthis.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.selectedFactorNode = factorItem; // 设置当前选中因子\r\n\r\n\t\t\t// 如果只显示采购文件，使用采购文件页码信息\r\n\t\t\tif (this.isProcurementVisible && !this.isResponseVisible) {\r\n\t\t\t\tif (!this.procurementPdfRendered) {\r\n\t\t\t\t\tthis.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (factorItem.jumpToPage) {\r\n\t\t\t\t\tthis.$refs.procurement.skipPage(factorItem.jumpToPage); // 采购文件跳页\r\n\t\t\t\t} else if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n\t\t\t\t\tthis.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果显示响应文件或对比模式，需要选择供应商\r\n\t\t\tif (!this.supplierFactorPageMap || Object.keys(this.supplierFactorPageMap).length === 0) {\r\n\t\t\t\tthis.$message.warning(\"请先选择供应商\"); // 未选供应商提示\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 跳转到响应文件对应页码\r\n\t\t\tif (this.isResponseVisible && this.$refs.response) {\r\n\t\t\t\tif (!this.responsePdfRendered) {\r\n\t\t\t\t\tthis.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.response.skipPage(this.supplierFactorPageMap[this.selectedFactorNode.itemName]); // 响应文件跳页\r\n\t\t\t}\r\n\r\n\t\t\t// 跳转到采购文件对应页码\r\n\t\t\tif (this.isProcurementVisible && this.$refs.procurement) {\r\n\t\t\t\tif (!this.procurementPdfRendered) {\r\n\t\t\t\t\tthis.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n\t\t\t\tif (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n\t\t\t\t\tthis.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n\t\t\t\t\t// 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 检查是否可以跳转页面\r\n\t\t * @returns {boolean} 是否可以跳转\r\n\t\t */\r\n\t\tcanJumpToPage() {\r\n\t\t\t// 如果只显示采购文件\r\n\t\t\tif (this.isProcurementVisible && !this.isResponseVisible) {\r\n\t\t\t\treturn this.procurementPdfRendered;\r\n\t\t\t}\r\n\t\t\t// 如果只显示响应文件\r\n\t\t\tif (this.isResponseVisible && !this.isProcurementVisible) {\r\n\t\t\t\treturn this.responsePdfRendered;\r\n\t\t\t}\r\n\t\t\t// 如果对比模式（两个都显示）\r\n\t\t\tif (this.isResponseVisible && this.isProcurementVisible) {\r\n\t\t\t\treturn this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t\t}\r\n\t\t\treturn false;\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 处理PDF渲染状态变化\r\n\t\t * @param {boolean} isRendered 是否渲染完成\r\n\t\t * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t\t */\r\n\t\thandlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t\t\tif (pdfType === 'response') {\r\n\t\t\t\tthis.responsePdfRendered = isRendered;\r\n\t\t\t} else if (pdfType === 'procurement') {\r\n\t\t\t\tthis.procurementPdfRendered = isRendered;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (isRendered) {\r\n\t\t\t\tconsole.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 跳转到二次报价\r\n\t\t */\r\n\t\tgoToSecondOffer() {\r\n\t\t\tconst query = {\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tzjhm: this.$route.query.zjhm,\r\n\t\t\t\tscoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")),\r\n\t\t\t};\r\n\t\t\tthis.$router.push({ path: \"/secondOffer\", query });\r\n\t\t},\r\n\t\t/**\r\n\t\t * 跳转到询标\r\n\t\t */\r\n\t\tgoToBidInquiry() {\r\n\t\t\tconst query = {\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tzjhm: this.$route.query.zjhm,\r\n\t\t\t\tscoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")),\r\n\t\t\t};\r\n\t\t\tthis.$router.push({ path: \"/bidInquiry\", query });\r\n\t\t},\r\n\t\t/**\r\n\t\t * 获取因素对应页码\r\n\t\t */\r\n\t\tloadFactorsPageMap() {\r\n\t\t\tthis.factorsPageMap = JSON.parse(localStorage.getItem(\"entDocResponsePage\"));\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 下载文件\r\n\t\t * @param {Object} item - 文件对象\r\n\t\t */\r\n\t\tdownloadFile(item) {\r\n\t\t\tthis.$download.zip(item.filePath, item.fileName);\r\n\t\t},\r\n\r\n\t\t// ========== 悬停相关 ==========\r\n\t\t/**\r\n\t\t * 显示评分项悬浮框\r\n\t\t * @param {Object} factorItem 评分项对象\r\n\t\t */\r\n\t\tshowFactorTooltip(factorItem) {\r\n\t\t\tif (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\r\n\t\t\t// 清除之前的定时器\r\n\t\t\tif (this.tooltipTimer) {\r\n\t\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\t}\r\n\r\n\t\t\t// 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t\tthis.tooltipTimer = setTimeout(() => {\r\n\t\t\t\tthis.hoveredFactorNode = factorItem;\r\n\t\t\t}, 300); // 300ms延迟\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 隐藏评分项悬浮框\r\n\t\t */\r\n\t\thideFactorTooltip() {\r\n\t\t\t// 清除定时器\r\n\t\t\tif (this.tooltipTimer) {\r\n\t\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\t\tthis.tooltipTimer = null;\r\n\t\t\t}\r\n\r\n\t\t\t// 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.hoveredFactorNode = null;\r\n\t\t\t}, 100);\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t\t */\r\n\t\tclearTooltipTimer() {\r\n\t\t\tif (this.tooltipTimer) {\r\n\t\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\t\tthis.tooltipTimer = null;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n},\r\n\r\n\tmounted() {\r\n\t\tthis.initPage();\r\n\t\tthis.loadFactorsPageMap();\r\n\t},\r\n\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.main-container-one {\r\n\tmin-height: 57vh;\r\n\tdisplay: flex;\r\n}\r\n.left-panel {\r\n\tmin-height: 57vh;\r\n\twidth: 79%;\r\n}\r\n.header-bar {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tborder-bottom: 2px solid #176ADB;\r\n\tpadding: 15px 20px;\r\n}\r\n.header-title {\r\n\tdisplay: flex;\r\n\theight: 36px;\r\n\tfont-weight: 700;\r\n\tfont-size: 24px;\r\n\tcolor: #333;\r\n}\r\n.header-steps {\r\n\tdisplay: grid;\r\n\tjustify-items: center;\r\n\tposition: relative;\r\n\tbottom: -30px;\r\n}\r\n.steps-tip {\r\n\tfont-size: 12px;\r\n}\r\n.steps-img {\r\n\twidth: 80px;\r\n\theight: 30px;\r\n\tmargin-right: 20px;\r\n}\r\n.header-btns {\r\n\ttext-align: right;\r\n}\r\n.header-btns-group {\r\n\tmargin-top: 20px;\r\n}\r\n.item-button.main {\r\n\tbackground-color: #176ADB;\r\n\tcolor: #fff;\r\n\tborder: 1px solid #176ADB;\r\n}\r\n.pdf-container {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\theight: 100%;\r\n\tmin-height: 600px;\r\n}\r\n.pdf-view {\r\n\twidth: 49%;\r\n}\r\n.border-right {\r\n\tborder-right: 1px solid #176ADB;\r\n}\r\n.border-left {\r\n\tborder-left: 1px solid #176ADB;\r\n}\r\n.divider {\r\n\tmin-height: 57vh;\r\n\twidth: 1%;\r\n\tbackground-color: #F5F5F5;\r\n}\r\n.right-panel {\r\n\tmin-height: 57vh;\r\n\twidth: 20%;\r\n}\r\n.right-header {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tborder-bottom: 2px solid #176ADB;\r\n\tpadding: 15px 20px;\r\n}\r\n.right-content {\r\n\tpadding: 15px 20px;\r\n}\r\n.factor-item {\r\n\tmargin-bottom: 10px;\r\n}\r\n.factors {\r\n\tdisplay: flex;\r\n\tjustify-content: flex-start;\r\n\tflex-wrap: wrap;\r\n\talign-items: center;\r\n\tmargin-bottom: 10px;\r\n}\r\n.factor-title {\r\n\tcursor: pointer;\r\n\tfont-family: SourceHanSansSC-Bold;\r\n\tfont-weight: 700;\r\n\tfont-size: 16px;\r\n\tcolor: #333;\r\n\tletter-spacing: 0;\r\n\twidth: auto;\r\n\ttext-align: left;\r\n\ttransition: all 0.3s ease;\r\n\tpadding: 4px 8px;\r\n\tborder-radius: 4px;\r\n\r\n\t&:hover {\r\n\t\tbackground-color: #f0f8ff;\r\n\t\tcolor: #176ADB;\r\n\t\ttransform: translateX(2px);\r\n\t}\r\n}\r\n.factor-radio-group {\r\n\tdisplay: flex;\r\n\twidth: 100%;\r\n\tjustify-content: flex-end;\r\n}\r\n.factor-divider {\r\n\theight: 1px;\r\n\tbackground-color: #DCDFE6;\r\n\tmargin-top: 10px;\r\n}\r\n.right-btns {\r\n\tdisplay: flex;\r\n\tmargin: 32px 0;\r\n\tjustify-content: space-evenly;\r\n}\r\n.review-content {\r\n\ttext-align: left;\r\n\tfont-size: 14px;\r\n}\r\n.review-title {\r\n\tfont-family: SourceHanSansSC-Bold;\r\n\tfont-weight: 700;\r\n\tfont-size: 15px;\r\n\tcolor: #176ADB;\r\n\tletter-spacing: 0;\r\n}\r\n.review-html {\r\n\tpadding: 6px 30px;\r\n}\r\n.item-button {\r\n\tborder: 1px solid #979797;\r\n\twidth: 150px;\r\n\theight: 36px;\r\n\tmargin: 0 10px;\r\n\tfont-weight: 700;\r\n\tfont-size: 17px;\r\n\tborder-radius: 6px;\r\n\tcolor: #333;\r\n\t&:hover {\r\n\t\tcolor: #333;\r\n\t}\r\n}\r\n.qualification-blue-btn {\r\n\tbackground-color: #176ADB !important;\r\n\tcolor: #fff !important;\r\n\tborder: 1px solid #176ADB !important;\r\n}\r\n.qualification-blue-btn-active {\r\n\tbackground-color: #FF6B35 !important;\r\n\tcolor: #fff !important;\r\n\tborder: 1px solid #FF6B35 !important;\r\n\tbox-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.item-button-little {\r\n\twidth: 124px;\r\n\theight: 36px;\r\n\tfont-weight: 700;\r\n\tfont-size: 18px;\r\n\tcolor: #fff;\r\n\tbackground-color: #176ADB;\r\n\t&:hover {\r\n\t\tcolor: #fff;\r\n\t}\r\n}\r\n.text {\r\n\t::v-deep .el-textarea__inner {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder-radius: 0;\r\n\t\tborder: 1px solid #f5f5f5;\r\n\t}\r\n}\r\n\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n\t.fileItem {\r\n\t\ttransition: all 0.3s ease;\r\n\t\t&:hover {\r\n\t\t\ttransform: translateY(-2px);\r\n\t\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\t\t}\r\n\r\n\t\t::v-deep .el-card__body {\r\n\t\t\tpadding: 0;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n</style>\r\n"]}]}