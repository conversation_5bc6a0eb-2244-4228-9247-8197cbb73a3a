{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business.vue", "mtime": 1753923515058}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["business.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "business.vue", "sourceRoot": "src/views/expertReview", "sourcesContent": ["<template>\n  <div>\n<!--    <BidHeadthree></BidHeadthree>-->\n\t  <div class=\"title\">专家评审系统</div>\n    <div class=\"info\">\n      <div class=\"content\">\n        <one v-if=\"node == 'one'\" @send=\"handleData\"></one>\n        <two v-if=\"node == 'two'\" @send=\"handleData\" :isLeader=\"isLeader\" :finish=\"finish\"></two>\n        <three v-if=\"node == 'three'\" @send=\"handleData\" :finish=\"finish\"></three>\n      </div>\n    </div>\n    <Foot></Foot>\n  </div>\n\n</template>\n\n<script>\nimport one from \"./business/one\";\nimport two from \"./business/two\";\nimport three from \"./business/three\";\nimport { getProject } from \"@/api/tender/project\";\nimport { expertInfoById } from \"@/api/expert/review\";\nimport { getEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\nimport expertReviewWebSocket from \"@/mixins/expertReviewWebSocket\";\n\nexport default {\n  components: { one, two, three },\n  mixins: [expertReviewWebSocket],\n  name: \"qualification\",\n  data() {\n    return {\n      projectName: \"测试项目\",\n      project: {},\n      node: \"one\",\n      finish: false,\n      leader: {},\n      isLeader: false,\n    };\n  },\n  methods: {\n    async init() {\n      try {\n        // 根据项目id查询项目信息\n        const projectResponse = await getProject(this.$route.query.projectId);\n        if (projectResponse.code === 200) {\n          this.project = projectResponse.data;\n        } else {\n          this.$message.warning(projectResponse.msg);\n        }\n\n        // 获取专家信息\n        const expertResponse = await expertInfoById({\n          projectId: this.$route.query.projectId,\n        });\n        if (expertResponse.code === 200) {\n          this.leader = expertResponse.data.find(\n            (item) => item.expertLeader === 1\n          );\n          console.log(\"this.leader\", this.leader);\n\n          if (this.leader && this.leader.zjhm === this.$route.query.zjhm) {\n            this.isLeader = true;\n          }\n        } else {\n          this.$message.warning(expertResponse.msg);\n        }\n\n        // 设置 finish 和 node 的逻辑\n        this.finish = this.$route.query.finish === \"true\";\n        console.log(\"this.finish\", this.finish, \"this.isLeader\", this.isLeader);\n\t      \n\t      // // 判断当前环境\n\t      if (process.env.NODE_ENV === \"development\") {\n\t\t      this.node = \"one\";\n\t\t      return\n\t      }\n\t\t\t\t\n        // 判断是否满足条件\n        if (this.finish && this.isLeader) {\n          this.node = \"three\";\n        } else if (this.finish && !this.isLeader) {\n          this.node = \"two\";\n        } else {\n          this.getEvalExpertStatus();\n        }\n      } catch (error) {\n        console.error(\"Error during API calls:\", error);\n        this.$message.error(\"An error occurred while fetching data.\");\n      }\n    },\n    // 查询专家评审节点信息\n    getEvalExpertStatus() {\n      // 查询专家评审节点信息\n      getEvalExpertScoreInfo({\n        projectEvaluationId: JSON.parse(\n          localStorage.getItem(\"evalProjectEvaluationProcess\")\n        ).projectEvaluationId,\n        expertResultId: JSON.parse(localStorage.getItem(\"expertResultId\")),\n        scoringMethodItemId: JSON.parse(\n          localStorage.getItem(\"evalProjectEvaluationProcess\")\n        ).scoringMethodItemId,\n      }).then((expertStatusResponse) => {\n        if (expertStatusResponse.code == 200) {\n          localStorage.setItem(\n            \"evalExpertScoreInfo\",\n            JSON.stringify(expertStatusResponse.data)\n          );\n          if (expertStatusResponse.data.evalState == 0) {\n            this.node = \"one\";\n          } else if (expertStatusResponse.data.evalState == 1) {\n            this.node = \"two\";\n          } else if (expertStatusResponse.data.evalState == 2) {\n            if(this.isLeader ){\n              this.node = \"three\";\n            }else{\n              this.node = \"two\";\n            }\n          }\n        }\n      });\n    },\n    // 跳转到二次报价\n    secondOffer() {\n      const query = {\n        projectId: this.$route.query.projectId,\n        zjhm: this.$route.query.zjhm,\n        scoringMethodItemId: JSON.parse(\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\n        ),\n      };\n      this.$router.push({ path: \"/secondOffer\", query: query });\n    },\n    handleData(data) {\n      this.node = data;\n    },\n    // 发送消息给所有专家\n    sendMessageToExperts(message) {\n      if (this.reviewWebSocket && this.reviewWebSocket.readyState === WebSocket.OPEN) {\n        this.reviewWebSocket.send(JSON.stringify(message));\n      }\n    },\n  },\n  mounted() {\n    this.init();\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.info {\n  background-color: #f5f5f5;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n.content {\n  background-color: #fff;\n  width: 90%;\n  min-height: 64vh;\n  margin: 20px 0;\n}\n.item {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  font-size: 18px;\n  margin-bottom: 80px;\n  .item-title {\n    width: 120px;\n    margin-right: 20px;\n    text-align: left;\n  }\n}\n.little-title {\n  color: rgba(80, 80, 80, 1);\n  font-size: 14px;\n}\n.item-button {\n  border: #333 1px solid;\n  width: 155px;\n  height: 48px;\n  margin: 20px 28px;\n  background-color: rgba(151, 253, 246, 1);\n  color: rgba(0, 0, 0, 1);\n  &:hover {\n    color: rgba(0, 0, 0, 1);\n  }\n}\n.item-button-little {\n  border: #333 1px solid;\n  width: 124px;\n  height: 32px;\n  background-color: rgba(151, 253, 246, 1);\n  color: rgba(0, 0, 0, 1);\n  &:hover {\n    color: rgba(0, 0, 0, 1);\n  }\n}\n.factors {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.title{\n\tbackground-color: #c8c9cc;\n\tpadding: 10px 5%;\n}\n</style>"]}]}