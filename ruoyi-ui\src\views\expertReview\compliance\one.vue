<template>
  <!-- 页面主容器，flex布局，分为左中右三部分 -->
  <div class="compliance-main">
    <!-- 左侧内容区，包含标题、操作按钮、PDF预览区 -->
    <div class="compliance-left">
      <!-- 顶部标题和操作按钮区 -->
      <div class="compliance-header">
        <!-- 标题及操作步骤图片 -->
        <div class="compliance-title-group">
          <div class="compliance-title">符合性评审</div> <!-- 页面主标题 -->
          <div class="compliance-step-img-group">
            <div class="compliance-step-text">该页面操作说明</div> <!-- 操作步骤说明文字 -->
            <el-image class="compliance-step-img" :src="helpImgList[0]" :preview-src-list="helpImgList">
            </el-image> <!-- 操作步骤图片，可点击放大 -->
          </div>
        </div>
	      
	      <!-- 文件列表 -->
	      <div class="fileList" style="width: 200px; border-right: 1px solid #e6e6e6; border-left: 1px solid #e6e6e6; padding: 10px; overflow-y: auto;">
		      <div style="font-weight: bold; margin-bottom: 10px; color: #333;">响应文件附件下载</div>
		      <el-card
			      v-for="(item, index) in attachmentsList"
			      :key="index"
			      class="fileItem"
			      shadow="hover"
			      @click.native="downloadFile(item)"
			      style="margin-bottom: 8px; cursor: pointer;"
		      >
			      <div style="display: flex; align-items: center; padding: 5px;">
				      <i class="el-icon-document" style="margin-right: 8px; color: #409EFF;"></i>
				      <span style="font-size: 12px; flex: 1; word-break: break-all;">{{ item.fileName }}</span>
				      <i class="el-icon-download" style="margin-left: 8px; color: #999;"></i>
			      </div>
		      </el-card>
	      </div>
	      
        <!-- 右侧操作按钮区 -->
        <div class="compliance-header-btns">
          <el-button class="item-button" @click="bidInquiry">询标</el-button> <!-- 跳转到询标页面 -->
          <!-- <el-button class="item-button" @click="secondOffer">发起二次报价</el-button> -->
          <div class="compliance-header-btns-bottom">
	          <el-button
		          :class="['item-button', activeButton === 'procurement' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']"
		          @click="viewPurchasing">采购文件</el-button> <!-- 显示采购文件PDF -->
	          
            <el-button
              :class="['item-button', activeButton === 'response' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']"
              @click="showResponseFile">响应文件</el-button> <!-- 显示响应文件PDF -->
           
            <el-button
              :class="['item-button', activeButton === 'contrast' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']"
              @click="showFileContrast">对比</el-button> <!-- 响应文件与采购文件对比 -->
          </div>
        </div>
      </div>
      <!-- PDF文件预览区，支持单文件和双文件对比 -->
      <div style="height:82%">
        <!-- PDF预览区域 - 保持原始尺寸 -->
        <div class="compliance-pdf-group">
	        <!-- 采购文件PDF预览 -->
	        <div v-show="isShowProcurement" :class="['compliance-pdf', isDoubleView ? 'compliance-pdf-border-left' : '']">
		        <!--            <pdfView ref="procurement" :pdfurl="procurementPdfUrl" :uni_key="'procurement'"></pdfView>-->
		        
		        <PdfViewImproved ref="procurement"  :pdfurl="procurementPdfUrl"  :page-height="800"
		                         :buffer-size="2" @render-status-change="(status) => handlePdfRenderStatusChange(status, 'procurement')"/>
	        
	        </div>
	        
          <!-- 响应文件PDF预览 -->
          <div v-show="isShowResponse" :class="['compliance-pdf', isDoubleView ? 'compliance-pdf-border-right' : '']">
<!--            <pdfView ref="response" :pdfurl="responsePdfUrl" :uni_key="'response'"></pdfView>-->
	          
	          <PdfViewImproved ref="response"  :pdfurl="responsePdfUrl"  :page-height="800" :buffer-size="2"
	                           @render-status-change="(status) => handlePdfRenderStatusChange(status, 'response')"/>
	          
          </div>
          
        </div>
      </div>
    </div>
    <!-- 中间分割线 -->
    <div class="compliance-divider"></div>
    <!-- 右侧评分区 -->
    <div class="compliance-right">
      <!-- 供应商选择下拉框 -->
      <div class="compliance-select-group">
        <el-select class="compliance-select" v-model="selectedSupplierName" placeholder="请选择供应商" @change="handleSupplierChange">
          <el-option v-for="item in supplierOptions" :key="item.bidderName" :label="item.bidderName" :value="item.bidderName">
          </el-option>
        </el-select>
      </div>
      <!-- 评分因子列表及操作区 -->
      <div class="compliance-factors-group" v-if="isShowResponse">
	      <!-- PDF渲染状态提示 -->
	      <div v-if="!responsePdfRendered" class="render-status-tip">
		      <i class="el-icon-loading"></i>
		      <span>响应文件正在渲染中，请稍候...</span>
	      </div>
	      <div v-else class="render-status-tip success">
		      <i class="el-icon-success"></i>
		      <span>响应文件渲染完成，可以点击跳转</span>
	      </div>
	      
        <!-- 判空后再渲染评分因子项列表，防止scoringMethod为null时报错 -->
        <template v-if="scoringMethod && scoringMethod.uitems">
          <div v-for="(item, index) in scoringMethod.uitems" :key="index"
               class="factor-item" style="margin-bottom:10px"
               @mouseenter="showFactorTooltip(item)"
               @mouseleave="hideFactorTooltip" >
	          <!-- 悬浮框 -->
	          <div v-if="hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId"
	               class="factor-tooltip"
	               @mouseenter="clearTooltipTimer"
	               @mouseleave="hideFactorTooltip">
		          <div class="tooltip-header">
			          <div class="tooltip-title">评审内容</div>
			          <i class="el-icon-close tooltip-close" @click="hideFactorTooltip"></i>
		          </div>
		          <div class="tooltip-content" v-html="item.itemRemark"></div>
	          </div>
	          
            <div>
              <div class="factors">
                <!-- 评分因子名称，点击可跳转PDF对应页码 -->
                <div class="compliance-factor-title-group factor-title" :class="{ 'disabled': !canJumpToPage() }">
                  <div class="compliance-factor-title" @click="handleShowFactorInfo(item)">
                    {{ item.itemName }}
	                  <i v-if="!canJumpToPage()" class="el-icon-loading" style="margin-left: 5px; font-size: 12px;"></i>
                  </div>
                </div>
                <!-- 评分单选按钮（通过/不通过） -->
                <div class="compliance-factor-radio-group">
                  <div>
                    <el-radio v-model="ratingStateMap[item.entMethodItemId].state" label="0"><span style="color:red">不通过</span></el-radio>
                    <el-radio v-model="ratingStateMap[item.entMethodItemId].state" label="1"><span style="color:green">通过</span></el-radio>
                  </div>
                </div>
              </div>
              <!-- 不通过时填写原因 -->
              <el-input v-if="ratingStateMap[item.entMethodItemId].state == 0" class="text" type="textarea" :rows="3" placeholder="未通过原因" v-model="ratingStateMap[item.entMethodItemId].reason">
              </el-input>
              <!-- 系统初验结果展示，绿色为通过，红色为未通过 -->
              <span v-if="Object.keys(checkResult).length > 0" :style="{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }">
                <i v-if="getCheckResultState(item.itemName)==='1'" class="el-icon-success"></i>
                <i v-if="getCheckResultState(item.itemName)==='0'" class="el-icon-warning"></i>
                {{checkResultNameMap[item.itemName]}}</span>
              <div class="compliance-factor-divider"></div>
            </div>
          </div>
        </template>
        <!-- 提交按钮区 -->
        <div class="compliance-submit-group">
          <!-- <div><el-button class="item-button-little" style="background-color:#F5F5F5;color:#176ADB" @click="show">保存</el-button></div> -->
          <div><el-button class="item-button-little compliance-submit-btn" @click="submit">提交</el-button></div>
        </div>
        <!-- 当前选中评分因子的详细说明 -->
        <div class="compliance-review-content">
          <div class="compliance-review-title">评审内容：</div>
          <div class="compliance-review-html" v-html="selectedFactorNode.itemRemark"></div>
        </div>
      </div>

	    <div class="compliance-factors-group" v-else>
		    <!-- PDF渲染状态提示 -->
		    <div v-if="!procurementPdfRendered" class="render-status-tip">
			    <i class="el-icon-loading"></i>
			    <span>采购文件正在渲染中，请稍候...</span>
		    </div>
		    <div v-else class="render-status-tip success">
			    <i class="el-icon-success"></i>
			    <span>采购文件渲染完成，可以点击跳转</span>
		    </div>
		    
		    <!-- 判空后再渲染评分因子项列表，防止scoringMethod为null时报错 -->
		    <template v-if="pageProcurement">
			    <div v-for="(item, index) in pageProcurement" :key="index" class="factor-item" style="margin-bottom:10px"
			         @mouseenter="showFactorTooltip(item)"
			         @mouseleave="hideFactorTooltip" >
				    <!-- 悬浮框 -->
				    <div v-if="hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId"
				         class="factor-tooltip"
				         @mouseenter="clearTooltipTimer"
				         @mouseleave="hideFactorTooltip">
					    <div class="tooltip-header">
						    <div class="tooltip-title">评审内容</div>
						    <i class="el-icon-close tooltip-close" @click="hideFactorTooltip"></i>
					    </div>
					    <div class="tooltip-content" v-html="item.itemRemark"></div>
				    </div>
				    <div>
					    <div class="factors">
						    <!-- 评分因子名称，点击可跳转PDF对应页码 -->
						    <div class="compliance-factor-title-group factor-title" :class="{ 'disabled': !canJumpToPage() }">
							    <div class="compliance-factor-title" @click="handleShowFactorInfo(item)">
								    {{ item.itemName }}
								    <i v-if="!canJumpToPage()" class="el-icon-loading" style="margin-left: 5px; font-size: 12px;"></i>
							    </div>
						    </div>
						    <!-- 评分单选按钮（通过/不通过） -->
						    <div class="compliance-factor-radio-group">
							    <div>
								    <el-radio v-model="ratingStateMap[item.entMethodItemId].state" label="0"><span style="color:red">不通过</span></el-radio>
								    <el-radio v-model="ratingStateMap[item.entMethodItemId].state" label="1"><span style="color:green">通过</span></el-radio>
							    </div>
						    </div>
					    </div>
					    <!-- 不通过时填写原因 -->
					    <el-input v-if="ratingStateMap[item.entMethodItemId].state == 0" class="text" type="textarea" :rows="3" placeholder="未通过原因" v-model="ratingStateMap[item.entMethodItemId].reason">
					    </el-input>
					    <!-- 系统初验结果展示，绿色为通过，红色为未通过 -->
					    <span v-if="Object.keys(checkResult).length > 0" :style="{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }">
                <i v-if="getCheckResultState(item.itemName)==='1'" class="el-icon-success"></i>
                <i v-if="getCheckResultState(item.itemName)==='0'" class="el-icon-warning"></i>
                {{checkResultNameMap[item.itemName]}}</span>
					    <div class="compliance-factor-divider"></div>
				    </div>
			    </div>
		    </template>
		    <!-- 提交按钮区 -->
		    <div class="compliance-submit-group">
			    <!-- <div><el-button class="item-button-little" style="background-color:#F5F5F5;color:#176ADB" @click="show">保存</el-button></div> -->
			    <div><el-button class="item-button-little compliance-submit-btn" @click="submit">提交</el-button></div>
		    </div>
		    <!-- 当前选中评分因子的详细说明 -->
		    <div class="compliance-review-content">
			    <div class="compliance-review-title">评审内容：</div>
			    <div class="compliance-review-html" v-html="selectedFactorNode.itemRemark"></div>
		    </div>
	    </div>

    </div>
  </div>
</template>

<script>
// 常量定义
const PASS = '1'; // 通过
const FAIL = '0'; // 不通过
const CHECK_PASS = '系统初验通过'; // 系统初验通过文本
const CHECK_FAIL = '系统初验未通过'; // 系统初验未通过文本

import {
  supplierInfo, // 获取供应商信息API
  approvalProcess, // 获取评分方法API
  scoringFactors, // 提交评分因子API
  checkReviewSummary, // 检查评审汇总API
  filesById, // 获取项目相关文件API
} from "@/api/expert/review"; // 导入专家评审相关API
import { getDetailByPsxx } from "@/api/evaluation/detail/"; // 获取评分详情API
import { editEvalExpertScoreInfo } from "@/api/evaluation/expertStatus"; // 编辑专家评分状态API
import { resDocReviewFactorsDecision } from "@/api/docResponse/entInfo"; // 获取响应文件评审因子决策API

export default {
  data() {
    return {
      supplierOptions: [], // 供应商下拉选项列表
      scoringMethod: null, // 当前评分方法对象
      selectedFactorNode: {}, // 当前选中的评分因子节点
      selectedSupplierName: '', // 当前选中的供应商名称
      selectedSupplier: {}, // 当前选中的供应商对象
      expertInfo: {}, // 当前专家信息
      ratingStateMap: {}, // 评分项状态映射（key为评分项ID，value为{state, reason}）
      projectFiles: {}, // 项目相关文件对象
      isShowResponse: false, // 是否显示响应文件
      isShowProcurement: false, // 是否显示采购文件
      isDoubleView: false, // 是否双文件对比模式
      factorDetailList: [], // 评分因子详细列表
      entDocResponsePage: null, // 企业响应文件页码信息
      factorsPageMap: null, // 供应商因子页码映射
      supplierFactorPage: null, // 当前供应商因子页码
      responsePdfUrl: null, // 响应文件PDF地址
      procurementPdfUrl: null, // 采购文件PDF地址

      // 按钮状态管理
      activeButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'

	    entDocProcurementPage: {}, // 采购文件页码信息
      pageProcurement:[], // 采购文件的评分项
      attachmentsList:[], // 文件列表
	    
	    // PDF渲染状态管理
	    responsePdfRendered: false, // 响应文件PDF是否渲染完成
	    procurementPdfRendered: false, // 采购文件PDF是否渲染完成

	    helpImgList: ["/evalution/help.jpg"], // 操作帮助图片列表
      // 评分项名称与后端字段映射
      factorCodeMap: {
        "特定资格要求": "zgzs",
        "响应内容": "jsplb",
        "采购需求": "jsplb",
        "供货期限": "ghqx",
        "投标报价": "tbbj"
      },
      checkResult: {}, // 系统初验结果对象
      // 系统初验结果名称映射
      checkResultNameMap: {
        "符合《中华人民共和国政府采购法》第二十二条规定": CHECK_PASS,
        "特定资格要求": CHECK_PASS,
        "信用查询": CHECK_PASS,
        "响应人名称": CHECK_PASS,
        "响应内容": CHECK_PASS,
        "采购需求": CHECK_PASS,
        "供货期限": CHECK_PASS,
        "投标报价": CHECK_PASS
      },
      // 本地缓存数据
      localExpertInfo: null, // 本地专家信息
      localEntDocResponsePage: null, // 本地响应文件页码
      localFactorsPageMap: null, // 本地因子页码映射
	    
	    // 悬停状态管理
	    hoveredFactorNode: null, // 悬停时的评分项
	    tooltipTimer: null, // 悬浮框显示定时器
    };
  },
  methods: {
    /**
     * 校验所有评分项是否填写完整
     * @returns {boolean} 是否全部填写
     */
    validateRatings() {
      for (const item of this.scoringMethod.uitems) { // 遍历所有评分项
        const state = this.ratingStateMap[item.entMethodItemId].state; // 获取评分状态
        const reason = this.ratingStateMap[item.entMethodItemId].reason; // 获取评分原因
        // 评分结果未填写
        if (state === null || state === '') {
          // this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`); // 提示未填写
          return true;
        }
        // 不通过但未填写原因
        if (state === FAIL && (!reason || reason.trim() === '')) {
          this.$message.warning(`${item.itemName}评审不通过但未填写备注，不进行保存`); // 提示未填写原因
          return false;
        }
      }
      return true; // 全部填写返回true
    },
    /**
     * 获取系统初验结果（通过/未通过）
     * @param {string} factorName 评分项名称
     * @returns {string} 1-通过 0-未通过
     */
    getCheckResultState(factorName) {
      if (!this.checkResult || Object.keys(this.checkResult).length === 0) return ''; // 没有初验结果直接返回空
      let code = this.factorCodeMap[factorName]; // 获取评分项对应的后端字段
      let check = PASS; // 默认通过
      if (code) {
        check = this.checkResult[code]; // 获取初验结果
        // 投标报价特殊处理
        if (factorName === "投标报价" && check === PASS) {
          check = this.checkResult['mxbjb']; // 明细报价表
        }
      }
      // 设置初验结果名称
      if (check === FAIL) {
        this.checkResultNameMap[factorName] = CHECK_FAIL; // 未通过
      } else {
        check = PASS;
        this.checkResultNameMap[factorName] = CHECK_PASS; // 通过
      }
      return check; // 返回初验结果
    },
    /**
     * 重置所有评分项的状态
     */
    resetRatingStateMap() {
      if (!this.scoringMethod) return; // 没有评分方法直接返回
      for (const key of Object.keys(this.ratingStateMap)) { // 遍历所有评分项
        this.ratingStateMap[key].state = null; // 重置状态
        this.ratingStateMap[key].reason = ''; // 重置原因
      }
    },
    /**
     * 临时保存评分结果到后端
     * 校验通过后才会保存
     * @returns {boolean} 保存是否成功
     */
    async saveTempRating() {
      if (!this.validateRatings()) return false; // 校验不通过不保存，返回false
      // 构造提交数据
      const data = this.scoringMethod.uitems.map(item => {
        const itemId = item.entMethodItemId; // 获取评分项ID
        return {
          scoringMethodUitemId: itemId, // 评分项ID
          expertResultId: this.expertInfo.resultId, // 专家结果ID
          entId: this.selectedSupplier.bidderId, // 供应商ID
          evaluationResult: this.ratingStateMap[itemId].state, // 评分结果
          evaluationRemark: this.ratingStateMap[itemId].reason || '' // 评分原因
        };
      }).filter(d => d.evaluationResult !== null && d.evaluationResult !== ''); // 过滤未填写的项
      if (data.length > 0) {
        try {
          const response = await scoringFactors(data); // 提交评分因子
          if (response.code === 200) {
            this.$message.success("保存成功"); // 保存成功提示
            return true; // 保存成功返回true
          } else {
            this.$message.warning(response.msg); // 保存失败提示
            return false; // 保存失败返回false
          }
        } catch (e) {
          this.$message.error("保存失败"); // 异常提示
          return false; // 异常返回false
        }
      }
      return true; // 没有数据需要保存时也返回true
    },
    /**
     * 供应商切换事件，切换时自动保存上一个供应商评分，并并发获取新供应商的评分详情和系统初验
     * @param {string} supplierName 供应商名称
     */
    async handleSupplierChange(supplierName) {
      // 切换前保存上一个供应商评分
      if (Object.keys(this.selectedSupplier).length !== 0) {
        await this.saveTempRating(); // 保存评分
      }
      // 查找当前选中的供应商对象
      this.selectedSupplier = this.supplierOptions.find(item => item.bidderName === supplierName); // 查找供应商
      // 获取当前供应商因子页码
      this.supplierFactorPage = this.factorsPageMap[this.selectedSupplier.bidderId]; // 获取页码
      // 并发获取评分详情和系统初验
      // 使用 Promise.allSettled 让两个请求独立执行，互不影响
      try {
        const [detailResult, checkResult] = await Promise.allSettled([
          getDetailByPsxx({
            expertResultId: this.expertInfo.resultId, // 专家结果ID
            projectId: this.$route.query.projectId, // 项目ID
            scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项ID
          }),
          resDocReviewFactorsDecision({
            projectId: this.$route.query.projectId, // 项目ID
            entId: this.selectedSupplier.bidderId, // 供应商ID
          })
        ]);

        // 处理评分详情请求结果
        if (detailResult.status === 'fulfilled') {
          const detailRes = detailResult.value;
          if (detailRes.code === 200) {
            this.factorDetailList = detailRes.data; // 评分详情列表
            const factor = this.factorDetailList.find(item => item.bidderName === supplierName)?.evalExpertEvaluationDetails; // 当前供应商评分详情
            this.resetRatingStateMap(); // 重置评分状态
            if (factor) {
              for (const item of factor) {
                this.ratingStateMap[item.scoringMethodUitemId].reason = item.evaluationRemark; // 设置评分原因
                this.ratingStateMap[item.scoringMethodUitemId].state = item.evaluationResult; // 设置评分结果
              }
            }
          } else {
            this.$message.warning(detailRes.msg); // 评分详情获取失败
          }
        } else {
          console.error("获取评分详情失败:", detailResult.reason);
          this.$message.error("获取评分详情失败"); // 评分详情请求异常
        }

        // 处理系统初验请求结果
        if (checkResult.status === 'fulfilled') {
          const checkRes = checkResult.value;
          if (checkRes.code === 200) {
            this.checkResult = checkRes.data; // 设置初验结果
          } else {
            console.error("获取系统初验结果失败:", checkRes.msg);
            this.$message.warning("获取系统初验结果失败"); // 系统初验获取失败
          }
        } else {
          console.error("系统初验请求失败:", checkResult.reason);
          this.$message.error("系统初验请求失败"); // 系统初验请求异常
        }
      } catch (e) {
        console.error("请求处理异常:", e);
        this.$message.error("获取供应商详情失败"); // 异常提示
      }
      // 默认显示响应文件
      this.showResponseFile();
    },
    /**
     * 显示响应文件PDF
     */
    showResponseFile() {
      if (!this.selectedSupplier || Object.keys(this.selectedSupplier).length === 0) {
        this.$message.warning("请选择供应商"); // 未选供应商提示
        return;
      }
      this.activeButton = 'response'; // 设置当前激活按钮
      this.isDoubleView = false; // 单文件模式
      this.isShowProcurement = false; // 不显示采购文件
      this.isShowResponse = true; // 显示响应文件
      this.responsePdfUrl = this.projectFiles.file[this.selectedSupplier.bidderId]; // 设置响应文件PDF地址
    },
    /**
     * 文件对比（双文件模式）
     */
    showFileContrast() {
      if (!this.selectedSupplier || Object.keys(this.selectedSupplier).length === 0) {
        this.$message.warning("请选择供应商"); // 未选供应商提示
        return;
      }
      this.activeButton = 'contrast'; // 设置当前激活按钮
      this.isDoubleView = true; // 双文件模式
      this.isShowProcurement = true; // 显示采购文件
      this.isShowResponse = true; // 显示响应文件
      this.responsePdfUrl = this.projectFiles.file[this.selectedSupplier.bidderId]; // 设置响应文件PDF地址
    },
    /**
     * 点击评分项名称，跳转到对应PDF页码
     * @param {Object} factorItem 当前评分因子项
     */
    handleShowFactorInfo(factorItem) {
	    // 检查PDF是否渲染完成
	    if (!this.canJumpToPage()) {
		    this.$message.warning("PDF页面正在渲染中，请稍候再试");
		    return;
	    }
			
      this.selectedFactorNode = factorItem; // 设置当前选中因子

      // 如果只显示采购文件，使用采购文件页码信息
      if (this.isShowProcurement && !this.isShowResponse) {
	      if (!this.procurementPdfRendered) {
		      this.$message.warning("采购文件正在渲染中，请稍候再试");
		      return;
	      }
				
        if (factorItem.jumpToPage) {
          this.$refs.procurement.skipPage(factorItem.jumpToPage); // 采购文件跳页
        } else if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {
          this.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页
        }
        return;
      }

      // 如果显示响应文件或对比模式，需要选择供应商
      if (!this.supplierFactorPage || Object.keys(this.supplierFactorPage).length === 0) {
        this.$message.warning("请先选择供应商"); // 未选供应商提示
        return;
      }

      // 跳转到响应文件对应页码
      if (this.isShowResponse && this.$refs.response) {
	      if (!this.responsePdfRendered) {
		      this.$message.warning("响应文件正在渲染中，请稍候再试");
		      return;
	      }
        this.$refs.response.skipPage(this.supplierFactorPage[this.selectedFactorNode.itemName]); // 响应文件跳页
      }

      // 跳转到采购文件对应页码
      if (this.isShowProcurement && this.$refs.procurement) {
	      if (!this.procurementPdfRendered) {
		      this.$message.warning("采购文件正在渲染中，请稍候再试");
		      return;
	      }
				
        // 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码
        if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {
          this.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页
        } else {
          // 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件
          // 这样可以避免采购文件和响应文件显示不同的内容造成混淆
        }
      }
    },
	  
	  /**
	   * 检查是否可以跳转页面
	   * @returns {boolean} 是否可以跳转
	   */
	  canJumpToPage() {
		  // 如果只显示采购文件
		  if (this.isShowProcurement && !this.isShowResponse) {
			  return this.procurementPdfRendered;
		  }
		  // 如果只显示响应文件
		  if (this.isShowResponse && !this.isShowProcurement) {
			  return this.responsePdfRendered;
		  }
		  // 如果对比模式（两个都显示）
		  if (this.isShowResponse && this.isShowProcurement) {
			  return this.responsePdfRendered && this.procurementPdfRendered;
		  }
		  return false;
	  },
	  /**
	   * 处理PDF渲染状态变化
	   * @param {boolean} isRendered 是否渲染完成
	   * @param {string} pdfType PDF类型：'response' 或 'procurement'
	   */
	  handlePdfRenderStatusChange(isRendered, pdfType) {
		  if (pdfType === 'response') {
			  this.responsePdfRendered = isRendered;
		  } else if (pdfType === 'procurement') {
			  this.procurementPdfRendered = isRendered;
		  }
		  
		  if (isRendered) {
			  console.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);
		  }
	  },
	  
    /**
     * 提交评分并修改专家进度
     */
    async submit() {
      // 先保存评分，如果保存失败则不继续提交
      const saveResult = await this.saveTempRating();
      if (!saveResult) {
        // 保存失败，不继续提交流程
        return;
      }

      const data = {
        projectId: this.$route.query.projectId, // 项目ID
        expertResultId: this.expertInfo.resultId, // 专家结果ID
        scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项ID
      };
      try {
        const response = await checkReviewSummary(data); // 检查评审汇总

        if (response.code === 200) {
          // 修改专家进度
          const status = {
            evalExpertScoreInfoId: JSON.parse(localStorage.getItem("evalExpertScoreInfo")).evalExpertScoreInfoId, // 专家评分信息ID
            evalState: 1, // 进度状态
          };
          const res = await editEvalExpertScoreInfo(status); // 编辑专家评分状态
          if (res.code === 200) {
            this.$message.success("提交成功"); // 提交成功提示
          }
          this.$emit("send", "two"); // 发送事件
        } else {
          this.$message.warning(response.msg); // 提交失败提示
        }
      } catch (e) {
        this.$message.error("提交失败"); // 异常提示
      }
    },
    /**
     * 显示采购文件PDF
     */
    viewPurchasing() {
      this.activeButton = 'procurement'; // 设置当前激活按钮
      this.isDoubleView = false; // 单文件模式
      this.isShowResponse = false; // 不显示响应文件
      this.isShowProcurement = true; // 显示采购文件
      // 右侧评分项显示为采购文件的评分项
      let pageProcurementArr = []; // 采购文件评分项数组

      for (let item in this.entDocProcurementPage){
        pageProcurementArr.push({
          itemName: item,
          jumpToPage: this.entDocProcurementPage[item]
        })
      }

      console.log(this.scoringMethod.uitems);
      console.log(pageProcurementArr)
      this.pageProcurement = [];
      for (let i = 0; i < this.scoringMethod.uitems.length;i++){
        for (let j = 0; j < pageProcurementArr.length;j++){
          if (this.scoringMethod.uitems[i].itemName == pageProcurementArr[j].itemName){
            this.pageProcurement.push({...this.scoringMethod.uitems[i],...pageProcurementArr[j]});
          }
        }
      }
      console.log(this.pageProcurement)
    },
    /**
     * 跳转到二次报价页面
     */
    secondOffer() {
      const query = {
        projectId: this.$route.query.projectId, // 项目ID
        zjhm: this.$route.query.zjhm, // 专家证件号码
        scoringMethodItemId: JSON.parse(localStorage.getItem("tenderOfferScoringMethodItems")), // 二次报价评分方法项ID
      };
      this.$router.push({ path: "/secondOffer", query }); // 跳转页面
    },
    /**
     * 跳转到询标页面
     */
    bidInquiry() {
      const query = {
        projectId: this.$route.query.projectId, // 项目ID
        zjhm: this.$route.query.zjhm, // 专家证件号码
        scoringMethodItemId: JSON.parse(localStorage.getItem("tenderOfferScoringMethodItems")), // 询标评分方法项ID
      };
      this.$router.push({ path: "/bidInquiry", query }); // 跳转页面
    },
    /**
     * 获取因素对应页码（从本地缓存）
     */
    getFactorsPage() {
      this.factorsPageMap = JSON.parse(localStorage.getItem("entDocResponsePage")); // 获取因子页码映射
    },
    /**
     * 初始化专家和本地数据，只在mounted时调用一次
     */
    initLocalData() {
      try {
        this.localExpertInfo = JSON.parse(localStorage.getItem("expertInfo") || "{}"); // 获取本地专家信息
        this.localEntDocResponsePage = JSON.parse(localStorage.getItem("entDocResponsePage") || "{}"); // 获取本地响应文件页码
        this.localFactorsPageMap = JSON.parse(localStorage.getItem("entDocResponsePage") || "{}"); // 获取本地因子页码映射
        this.expertInfo = this.localExpertInfo; // 设置专家信息
        this.entDocResponsePage = this.localEntDocResponsePage; // 设置响应文件页码
        this.factorsPageMap = this.localFactorsPageMap; // 设置因子页码映射
        console.log("本地数据已初始化", { expertInfo: this.expertInfo });
      } catch (error) {
        console.error("初始化本地数据失败:", error);
      }
    },

    /**
     * 初始化专家信息（用于响应专家信息更新）
     */
    initExpertInfo() {
      try {
        const expertInfoStr = localStorage.getItem("expertInfo");
        if (expertInfoStr) {
          this.localExpertInfo = JSON.parse(expertInfoStr);
          this.expertInfo = this.localExpertInfo;
          console.log("专家信息已刷新", this.expertInfo);
        }
      } catch (error) {
        console.error("刷新专家信息失败:", error);
      }
    },
    /**
     * 页面初始化，加载供应商、评分方法、文件等（并发请求）
     */
    async initPage() {
      this.initLocalData(); // 初始化本地数据
      try {
        // 并发获取供应商、评分方法、文件
        const [supplierRes, approvalRes, filesRes] = await Promise.all([
          supplierInfo({ projectId: this.$route.query.projectId }), // 获取供应商
          approvalProcess(this.$route.query.projectId, this.expertInfo.resultId), // 获取评分方法
          filesById(this.$route.query.projectId) // 获取项目文件
        ]);
        // 处理供应商
        if (supplierRes.code === 200) {
          this.supplierOptions = supplierRes.rows.filter(item => item.isAbandonedBid == 0); // 过滤未弃标供应商
        } else {
          this.$message.warning(supplierRes.msg); // 获取失败提示
        }
        // 处理评分方法
        if (approvalRes.code === 200) {
	        this.attachmentsList = approvalRes.data.busiTenderNotice.attachments.filter(item => item.fileType == "0");
          this.scoringMethod = approvalRes.data.scoringMethodUinfo.scoringMethodItems.find(
            item => item.scoringMethodItemId == this.$route.query.scoringMethodItemId
          ); // 获取当前评分方法
          localStorage.setItem("evalProjectEvaluationProcess", JSON.stringify(this.scoringMethod.evalProjectEvaluationProcess)); // 缓存评分流程
          this.ratingStateMap = this.scoringMethod.uitems.reduce((acc, item) => {
            acc[item.entMethodItemId] = { state: null, reason: '' }; // 初始化评分状态
            return acc;
          }, {});
        } else {
          this.$message.warning(approvalRes.msg); // 获取失败提示
        }
        // 处理文件
        if (filesRes.code === 200) {
          this.projectFiles = filesRes.data; // 设置项目文件
          if (this.projectFiles.tenderNoticeFilePath) {
            this.procurementPdfUrl = this.projectFiles.tenderNoticeFilePath; // 设置采购文件PDF
          }
          // if (this.projectFiles.file) {
          //   this.responsePdfUrl = this.projectFiles.file[0]; // 设置响应文件PDF
          // }
        } else {
          this.$message.warning(filesRes.msg); // 获取失败提示
        }
      } catch (e) {
        this.$message.error("页面初始化失败"); // 异常提示
      }
    },
	  
	  downloadFile(item){
		  this.$download.zip(item.filePath,item.fileName);
	  },
	  
	  
	  // ========== 悬停相关 ==========
	  /**
	   * 显示评分项悬浮框
	   * @param {Object} factorItem 评分项对象
	   */
	  showFactorTooltip(factorItem) {
		  if (!factorItem.itemRemark) return; // 如果没有评审内容则不显示
		  
		  // 清除之前的定时器
		  if (this.tooltipTimer) {
			  clearTimeout(this.tooltipTimer);
		  }
		  
		  // 延迟显示悬浮框，避免快速移动时频繁显示
		  this.tooltipTimer = setTimeout(() => {
			  this.hoveredFactorNode = factorItem;
		  }, 300); // 300ms延迟
	  },
	  
	  /**
	   * 隐藏评分项悬浮框
	   */
	  hideFactorTooltip() {
		  // 清除定时器
		  if (this.tooltipTimer) {
			  clearTimeout(this.tooltipTimer);
			  this.tooltipTimer = null;
		  }
		  
		  // 延迟隐藏，给用户时间移动到悬浮框上
		  setTimeout(() => {
			  this.hoveredFactorNode = null;
		  }, 100);
	  },
	  
	  /**
	   * 清除悬浮框定时器（当鼠标移动到悬浮框上时）
	   */
	  clearTooltipTimer() {
		  if (this.tooltipTimer) {
			  clearTimeout(this.tooltipTimer);
			  this.tooltipTimer = null;
		  }
	  }
  },
  mounted() {
	  this.entDocProcurementPage = JSON.parse(localStorage.getItem("entDocProcurementPage")); // 初始化采购文件页码信息
    this.initPage(); // 页面挂载时初始化数据
    this.getFactorsPage(); // 获取因子页码
  },
	beforeDestroy() {
		// 清理定时器
		if (this.tooltipTimer) {
			clearTimeout(this.tooltipTimer);
			this.tooltipTimer = null;
		}
	},
};
</script>

<style lang="scss" scoped>
.compliance-main {
  min-height: 57vh;
  display: flex;
}
.compliance-left {
  min-height: 57vh;
  width: 79%;
}
.compliance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid #176ADB;
  padding: 15px 20px;
}
.compliance-title-group {
  display: flex;
  height: 36px;
  font-weight: 700;
  font-size: 24px;
  color: #333;
}
.compliance-title {
  // nothing extra
}
.compliance-step-img-group {
  display: grid;
  justify-items: center;
  position: relative;
  bottom: -30px;
}
.compliance-step-text {
  font-size: 12px;
}
.compliance-step-img {
  width: 80px;
  height: 30px;
  margin-right: 20px;
}
.compliance-header-btns {
  text-align: right;
}
.compliance-header-btns-bottom {
  margin-top: 20px;
}
.compliance-blue-btn {
  background-color: #176ADB !important;
  color: #fff !important;
  border: 1px solid #176ADB !important;
}
.compliance-blue-btn-active {
  background-color: #FF6B35 !important;
  color: #fff !important;
  border: 1px solid #FF6B35 !important;
  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;
}
.compliance-pdf-group {
  display: flex;
  justify-content: center;
  height: 82%;
}
.compliance-pdf {
  width: 49%;
}
.compliance-pdf-border-right {
  border-right: 1px solid #176ADB;
}
.compliance-pdf-border-left {
  border-left: 1px solid #176ADB;
}
.compliance-divider {
  min-height: 57vh;
  width: 1%;
  background-color: #F5F5F5;
}
.compliance-right {
  min-height: 57vh;
  width: 20%;
}
.compliance-select-group {
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 2px solid #176ADB;
  padding: 15px 20px;
}
.compliance-select {
  width: 100%;
}
.compliance-factors-group {
  padding: 15px 20px;
}
.compliance-factor-title-group {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: left;
  width: 98%;
}
.compliance-factor-title {
  cursor: pointer;
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 16px;
  color: #333;
  letter-spacing: 0;
}
.compliance-factor-radio-group {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}
.compliance-factor-divider {
  height: 1px;
  background-color: #DCDFE6;
  margin-top: 10px;
}
.compliance-submit-group {
  display: flex;
  margin: 32px 0;
  justify-content: space-evenly;
}
.compliance-submit-btn {
  background-color: #176ADB;
}
.compliance-review-content {
  text-align: left;
  font-size: 14px;
}
.compliance-review-title {
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 15px;
  color: #176ADB;
  letter-spacing: 0;
}
.compliance-review-html {
  padding: 6px 30px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
	  transition: all 0.3s ease;
	  padding: 4px 8px;
	  border-radius: 4px;
	  
	  &:hover {
		  background-color: #f0f8ff;
		  color: #176ADB;
		  transform: translateX(2px);
	  }
  }
}
.item-button {
  border: 1px solid #979797;
  width: 150px;
  height: 36px;
  margin: 0 10px;
  font-weight: 700;
  font-size: 17px;
  border-radius: 6px;
  color: #333;
  &:hover {
    color: #333;
  }
}
.item-button-little {
  width: 124px;
  height: 36px;
  font-weight: 700;
  font-size: 18px;
  color: #fff;
  &:hover {
    color: #fff;
  }
}
.factors {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 10px;
}
.text {
  ::v-deep .el-textarea__inner {
    background-color: #f5f5f5;
    border-radius: 0;
    border: 1px solid #f5f5f5;
  }
}
.fileList {
	display: flex;
	align-items: center;
	gap: 20px;
	flex: 1;
	flex-wrap: wrap;
	.fileItem {
		transition: all 0.3s ease;
		&:hover {
			transform: translateY(-2px);
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		}
		
		::v-deep .el-card__body {
			padding: 0;
		}
	}
}

// PDF渲染状态提示样式
.render-status-tip {
	display: flex;
	align-items: center;
	padding: 10px 15px;
	margin-bottom: 15px;
	border-radius: 4px;
	background-color: #fff7e6;
	border: 1px solid #ffd591;
	color: #d48806;
	font-size: 14px;
	
	i {
		margin-right: 8px;
		font-size: 16px;
	}
	
	&.success {
		background-color: #f6ffed;
		border-color: #b7eb8f;
		color: #52c41a;
	}
}

// 禁用状态的评分项标题样式
.factor-title.disabled {
	color: #999 !important;
	cursor: not-allowed !important;
	opacity: 0.6;
	
	&:hover {
		color: #999 !important;
	}
}

// 悬浮框样式
.factor-tooltip {
	position: absolute;
	right: 100%; /* 显示在父元素左侧 */
	top: 0;
	margin-right: 10px; /* 与评分项的间距 */
	background: #fff;
	border: 1px solid #e4e7ed;
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	width: 400px;
	max-height: 300px;
	overflow: hidden;
	z-index: 9999;
	
	.tooltip-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 12px 16px;
		background-color: #f5f7fa;
		border-bottom: 1px solid #e4e7ed;
		
		.tooltip-title {
			font-weight: 600;
			font-size: 14px;
			color: #176ADB;
		}
		
		.tooltip-close {
			cursor: pointer;
			color: #909399;
			font-size: 14px;
			
			&:hover {
				color: #176ADB;
			}
		}
	}
	
	.tooltip-content {
		padding: 16px;
		font-size: 14px;
		line-height: 1.6;
		color: #333;
		max-height: 240px;
		overflow-y: auto;
		
		// 美化滚动条
		&::-webkit-scrollbar {
			width: 6px;
		}
		
		&::-webkit-scrollbar-track {
			background: #f1f1f1;
			border-radius: 3px;
		}
		
		&::-webkit-scrollbar-thumb {
			background: #c1c1c1;
			border-radius: 3px;
			
			&:hover {
				background: #a8a8a8;
			}
		}
	}
}

// 评分项容器相对定位
.factor-item {
	position: relative;
}

</style>
