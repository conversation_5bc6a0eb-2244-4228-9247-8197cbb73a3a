{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business\\one.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business\\one.vue", "mtime": 1753923382623}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBzdXBwbGllckluZm8sDQogIGFwcHJvdmFsUHJvY2VzcywNCiAgc2NvcmluZ0ZhY3RvcnMsDQogIGNoZWNrUmV2aWV3U3VtbWFyeSwNCiAgZmlsZXNCeUlkLA0KfSBmcm9tICJAL2FwaS9leHBlcnQvcmV2aWV3IjsNCmltcG9ydCB7IGdldERldGFpbEJ5UHN4eCB9IGZyb20gIkAvYXBpL2V2YWx1YXRpb24vZGV0YWlsLyI7DQppbXBvcnQgeyByZUV2YWx1YXRlIH0gZnJvbSAiQC9hcGkvZXhwZXJ0L3JldmlldyI7DQppbXBvcnQgeyBlZGl0RXZhbEV4cGVydFNjb3JlSW5mbyB9IGZyb20gIkAvYXBpL2V2YWx1YXRpb24vZXhwZXJ0U3RhdHVzIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBvcHRpb25zOiBbXSwNCiAgICAgIHNjb3JpbmdTeXN0ZW06IFtdLA0KICAgICAgc2VsZWN0Tm9kZToge30sDQogICAgICBzdXBwbGllcjogIiIsDQogICAgICBzZWxlY3RTdXBwbGllcjoge30sDQogICAgICBleHBlcnRJbmZvOiB7fSwNCiAgICAgIGRlZmF1bHRSYXRpbmdBcnJheToge30sDQogICAgICBmaWxlOiB7fSwNCiAgICAgIHJlc3BvbnNlU2hvdzogZmFsc2UsDQogICAgICBwcm9jdXJlbWVudFNob3c6IGZhbHNlLA0KICAgICAgZG91YmxlOiBmYWxzZSwNCg0KICAgICAgZW50RG9jUmVzcG9uc2VQYWdlOiB7fSwNCiAgICAgIGZhY3RvcnNQYWdlOiB7fSwNCiAgICAgIGJpZGRlckZhY3Rvcjoge30sDQogICAgICByZXNwb25zZVBkZjogbnVsbCwNCiAgICAgIHByb2N1cmVtZW50UGRmOiBudWxsLA0KCSAgICBjdXJyZW50TWF4U2NvcmU6IG51bGwsDQoNCiAgICAgIC8vIOaMiemSrueKtuaAgeeuoeeQhg0KICAgICAgYWN0aXZlQnV0dG9uOiAncmVzcG9uc2UnLCAvLyDlvZPliY3mv4DmtLvnmoTmjInpkq7vvJoncmVzcG9uc2Un44CBJ3Byb2N1cmVtZW50J+OAgSdjb250cmFzdCcNCg0KICAgICAgLy8g6YeH6LSt5paH5Lu255u45YWz5pWw5o2uDQogICAgICBlbnREb2NQcm9jdXJlbWVudFBhZ2U6IHt9LCAvLyDph4fotK3mlofku7bpobXnoIHkv6Hmga8NCiAgICAgIHBhZ2VQcm9jdXJlbWVudDogW10sIC8vIOmHh+i0reaWh+S7tueahOivhOWIhumhuQ0KICAgICAgYXR0YWNobWVudHNMaXN0OltdLCAvLyDmlofku7bliJfooagNCgkgICAgDQoJICAgIC8vIFBERua4suafk+eKtuaAgeeuoeeQhg0KCSAgICByZXNwb25zZVBkZlJlbmRlcmVkOiBmYWxzZSwgLy8g5ZON5bqU5paH5Lu2UERG5piv5ZCm5riy5p+T5a6M5oiQDQoJICAgIHByb2N1cmVtZW50UGRmUmVuZGVyZWQ6IGZhbHNlLCAvLyDph4fotK3mlofku7ZQREbmmK/lkKbmuLLmn5PlrozmiJANCg0KICAgICAgc3JjTGlzdDogWyIvZXZhbHV0aW9uL2hlbHAuanBnIl0sDQoJICAgIA0KCSAgICAvLyDmgqzlgZznirbmgIHnrqHnkIYNCgkgICAgaG92ZXJlZEZhY3Rvck5vZGU6IG51bGwsIC8vIOaCrOWBnOaXtueahOivhOWIhumhuQ0KCSAgICB0b29sdGlwVGltZXI6IG51bGwsIC8vIOaCrOa1ruahhuaYvuekuuWumuaXtuWZqA0KICAgIH07DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKioNCiAgICAgKiDpmZDliLbovpPlhaXmoYblj6rog73ovpPlhaXmlbDlrZflkozlsI/mlbDngrkNCiAgICAgKiBAcGFyYW0ge0V2ZW50fSBldmVudCAtIOmUruebmOS6i+S7tg0KICAgICAqLw0KICAgIG9ubHlOdW1iZXIoZXZlbnQpIHsNCiAgICAgIC8vIOiOt+WPluaMiemUrueahOWtl+espueggQ0KICAgICAgY29uc3QgY2hhckNvZGUgPSBldmVudC53aGljaCB8fCBldmVudC5rZXlDb2RlOw0KDQogICAgICAvLyDlhYHorrjnmoTlrZfnrKbvvJrmlbDlrZcoNDgtNTcp44CB5bCP5pWw54K5KDQ2KeOAgemAgOagvCg4KeOAgeWIoOmZpCg0NinjgIFUYWIoOSnjgIFFbnRlcigxMynjgIHmlrnlkJHplK4oMzctNDApDQogICAgICBpZiAoDQogICAgICAgIChjaGFyQ29kZSA+PSA0OCAmJiBjaGFyQ29kZSA8PSA1NykgfHwgLy8g5pWw5a2XIDAtOQ0KICAgICAgICBjaGFyQ29kZSA9PT0gNDYgfHwgLy8g5bCP5pWw54K5DQogICAgICAgIGNoYXJDb2RlID09PSA4IHx8ICAvLyDpgIDmoLzplK4NCiAgICAgICAgY2hhckNvZGUgPT09IDkgfHwgIC8vIFRhYumUrg0KICAgICAgICBjaGFyQ29kZSA9PT0gMTMgfHwgLy8gRW50ZXLplK4NCiAgICAgICAgKGNoYXJDb2RlID49IDM3ICYmIGNoYXJDb2RlIDw9IDQwKSAvLyDmlrnlkJHplK4NCiAgICAgICkgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCg0KICAgICAgLy8g6Zi75q2i5YW25LuW5a2X56ym6L6T5YWlDQogICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpOw0KICAgICAgcmV0dXJuIGZhbHNlOw0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDlpITnkIbliIbmlbDovpPlhaXvvIznoa7kv53lj6rog73ovpPlhaXmnInmlYjnmoTmlbDlrZcNCiAgICAgKiBAcGFyYW0ge3N0cmluZ30gaXRlbUlkIC0g6K+E5Lyw6aG5SUQNCiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdmFsdWUgLSDovpPlhaXlgLwNCiAgICAgKi8NCiAgICBoYW5kbGVTY29yZUlucHV0KGl0ZW1JZCwgdmFsdWUpIHsNCiAgICAgIC8vIOenu+mZpOmdnuaVsOWtl+Wtl+espu+8iOS/neeVmeWwj+aVsOeCue+8iQ0KICAgICAgbGV0IGNsZWFuVmFsdWUgPSB2YWx1ZS5yZXBsYWNlKC9bXlxkLl0vZywgJycpOw0KDQogICAgICAvLyDnoa7kv53lj6rmnInkuIDkuKrlsI/mlbDngrkNCiAgICAgIGNvbnN0IHBhcnRzID0gY2xlYW5WYWx1ZS5zcGxpdCgnLicpOw0KICAgICAgaWYgKHBhcnRzLmxlbmd0aCA+IDIpIHsNCiAgICAgICAgY2xlYW5WYWx1ZSA9IHBhcnRzWzBdICsgJy4nICsgcGFydHMuc2xpY2UoMSkuam9pbignJyk7DQogICAgICB9DQoNCiAgICAgIC8vIOmZkOWItuWwj+aVsOeCueWQjuacgOWkmjLkvY0NCiAgICAgIGlmIChwYXJ0cy5sZW5ndGggPT09IDIgJiYgcGFydHNbMV0ubGVuZ3RoID4gMikgew0KICAgICAgICBjbGVhblZhbHVlID0gcGFydHNbMF0gKyAnLicgKyBwYXJ0c1sxXS5zdWJzdHJpbmcoMCwgMik7DQogICAgICB9DQoNCiAgICAgIC8vIOabtOaWsOWAvA0KICAgICAgdGhpcy5kZWZhdWx0UmF0aW5nQXJyYXlbaXRlbUlkXS5zdGF0ZSA9IGNsZWFuVmFsdWU7DQoNCiAgICAgIC8vIOiwg+eUqOWOn+acieeahOmqjOivgeaWueazlQ0KICAgICAgdGhpcy52YWxpZGF0ZVNjb3JlKGl0ZW1JZCwgY2xlYW5WYWx1ZSk7DQogICAgfSwNCg0KICAgIGluaXQoKSB7DQogICAgICBjb25zdCBleHBlcnRJbmZvID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZXhwZXJ0SW5mbyIpKTsNCiAgICAgIHRoaXMuZW50RG9jUmVzcG9uc2VQYWdlID0gSlNPTi5wYXJzZSgNCiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oImVudERvY1Jlc3BvbnNlUGFnZSIpDQogICAgICApOw0KICAgICAgLy8g5Yid5aeL5YyW6YeH6LSt5paH5Lu26aG156CB5L+h5oGvDQogICAgICB0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZSA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oImVudERvY1Byb2N1cmVtZW50UGFnZSIpKTsNCiAgICAgIHN1cHBsaWVySW5mbyh7IHByb2plY3RJZDogdGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkLCBpc0FiYW5kb25lZEJpZDogMCB9KS50aGVuKA0KICAgICAgICAocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMub3B0aW9ucyA9IHJlc3BvbnNlLnJvd3MuZmlsdGVyKGl0ZW0gPT4gaXRlbS5pc0FiYW5kb25lZEJpZCA9PSAwKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKHJlc3BvbnNlLm1zZyk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICApOw0KICAgICAgYXBwcm92YWxQcm9jZXNzKHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwgZXhwZXJ0SW5mby5yZXN1bHRJZCkudGhlbigNCiAgICAgICAgKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICAvLyDmlofku7bliJfooagNCiAgICAgICAgICAgIHRoaXMuYXR0YWNobWVudHNMaXN0ID0gcmVzcG9uc2UuZGF0YS5idXNpVGVuZGVyTm90aWNlLmF0dGFjaG1lbnRzLmZpbHRlcihpdGVtID0+IGl0ZW0uZmlsZVR5cGUgPT0gIjAiKTsNCg0KICAgICAgICAgICAgdGhpcy5zY29yaW5nU3lzdGVtID0NCiAgICAgICAgICAgICAgcmVzcG9uc2UuZGF0YS5zY29yaW5nTWV0aG9kVWluZm8uc2NvcmluZ01ldGhvZEl0ZW1zLmZpbmQoDQogICAgICAgICAgICAgICAgKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgICAgIHJldHVybiAoDQogICAgICAgICAgICAgICAgICAgIGl0ZW0uc2NvcmluZ01ldGhvZEl0ZW1JZCA9PQ0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRyb3V0ZS5xdWVyeS5zY29yaW5nTWV0aG9kSXRlbUlkDQogICAgICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgKTsNCiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKA0KICAgICAgICAgICAgICAiZXZhbFByb2plY3RFdmFsdWF0aW9uUHJvY2VzcyIsDQogICAgICAgICAgICAgIEpTT04uc3RyaW5naWZ5KHRoaXMuc2NvcmluZ1N5c3RlbS5ldmFsUHJvamVjdEV2YWx1YXRpb25Qcm9jZXNzKQ0KICAgICAgICAgICAgKTsNCg0KICAgICAgICAgICAgdGhpcy5kZWZhdWx0UmF0aW5nQXJyYXkgPSB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zLnJlZHVjZSgNCiAgICAgICAgICAgICAgKGFjYywgXywgaW5kZXgpID0+IHsNCiAgICAgICAgICAgICAgICBhY2NbdGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtc1tpbmRleF0uZW50TWV0aG9kSXRlbUlkXSA9IHsNCiAgICAgICAgICAgICAgICAgIHN0YXRlOiBudWxsLA0KICAgICAgICAgICAgICAgICAgcmVhc29uOiAiIiwNCiAgICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgICAgIHJldHVybiBhY2M7DQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIHt9DQogICAgICAgICAgICApOw0KICAgICAgICAgICAgY29uc29sZS5sb2coInRoaXMuc2NvcmluZ1N5c3RlbS5pdGVtcyIsIHRoaXMuc2NvcmluZ1N5c3RlbS51aXRlbXMpOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzZ2FlLndhcm5pbmcocmVzcG9uc2UubXNnKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICk7DQogICAgICBmaWxlc0J5SWQodGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmZpbGUgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAgIGlmICh0aGlzLmZpbGUudGVuZGVyTm90aWNlRmlsZVBhdGggIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICB0aGlzLnByb2N1cmVtZW50UGRmID0gdGhpcy5maWxlLnRlbmRlck5vdGljZUZpbGVQYXRoOw0KICAgICAgICAgIH0NCiAgICAgICAgICAvLyBpZiAodGhpcy5maWxlLmZpbGUgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgLy8gICB0aGlzLnJlc3BvbnNlUGRmID0gdGhpcy5maWxlLmZpbGVbMF07DQogICAgICAgICAgLy8gfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhyZXNwb25zZS5tc2cpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIC8vIOWIneWni+WMluS4k+WutuS/oeaBrw0KICAgICAgdGhpcy5pbml0RXhwZXJ0SW5mbygpOw0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDliJ3lp4vljJbkuJPlrrbkv6Hmga8NCiAgICAgKi8NCiAgICBpbml0RXhwZXJ0SW5mbygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IGl0ZW1TdHJpbmcgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZXhwZXJ0SW5mbyIpOw0KICAgICAgICBpZiAoaXRlbVN0cmluZykgew0KICAgICAgICAgIHRoaXMuZXhwZXJ0SW5mbyA9IEpTT04ucGFyc2UoaXRlbVN0cmluZyk7DQogICAgICAgICAgY29uc29sZS5sb2coIuS4k+WutuS/oeaBr+W3suWIneWni+WMliIsIHRoaXMuZXhwZXJ0SW5mbyk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCJsb2NhbFN0b3JhZ2XkuK3mnKrmib7liLBleHBlcnRJbmZvIik7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWIneWni+WMluS4k+WutuS/oeaBr+Wksei0pToiLCBlcnJvcik7DQogICAgICB9DQogICAgfSwNCg0KICAgIGhhbmRsZUNoYW5nZSh2YWx1ZSkgew0KICAgICAgaWYoT2JqZWN0LmtleXModGhpcy5zZWxlY3RTdXBwbGllcikubGVuZ3RoICE9IDApew0KICAgICAgICB0aGlzLnRtcFNhdmUoKTsNCiAgICAgIH0NCiAgICAgIHRoaXMuc2VsZWN0U3VwcGxpZXIgPSB0aGlzLm9wdGlvbnMuZmluZCgoaXRlbSkgPT4gew0KICAgICAgICByZXR1cm4gaXRlbS5iaWRkZXJOYW1lID09IHZhbHVlOw0KICAgICAgfSk7DQoNCiAgICAgIC8vIOagueaNrmJpZGRlcmlk6I635Y+W5L6b5bqU5ZWG5Zug57Sg5Y+K5YW25a+55bqU6aG156CBDQogICAgICB0aGlzLmJpZGRlckZhY3RvciA9IHRoaXMuZmFjdG9yc1BhZ2VbdGhpcy5zZWxlY3RTdXBwbGllci5iaWRkZXJJZF07DQoNCiAgICAgIGNvbnN0IGRhdGEgPSB7DQogICAgICAgIGV4cGVydFJlc3VsdElkOiB0aGlzLmV4cGVydEluZm8ucmVzdWx0SWQsDQogICAgICAgIHByb2plY3RJZDogdGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkLA0KICAgICAgICBzY29yaW5nTWV0aG9kSXRlbUlkOiB0aGlzLiRyb3V0ZS5xdWVyeS5zY29yaW5nTWV0aG9kSXRlbUlkLA0KICAgICAgfTsNCiAgICAgIGdldERldGFpbEJ5UHN4eChkYXRhKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmZhY3Rvckxpc3QgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAgIGNvbnN0IGZhY3RvciA9IHRoaXMuZmFjdG9yTGlzdC5maW5kKChpdGVtKSA9PiB7DQogICAgICAgICAgICByZXR1cm4gaXRlbS5iaWRkZXJOYW1lID09IHZhbHVlOw0KICAgICAgICAgIH0pLmV2YWxFeHBlcnRFdmFsdWF0aW9uRGV0YWlsczsNCiAgICAgICAgICBpZiAoZmFjdG9yICE9IG51bGwpIHsNCiAgICAgICAgICAgIGZhY3Rvci5tYXAoKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgdGhpcy5kZWZhdWx0UmF0aW5nQXJyYXlbaXRlbS5zY29yaW5nTWV0aG9kVWl0ZW1JZF0ucmVhc29uID0NCiAgICAgICAgICAgICAgICBpdGVtLmV2YWx1YXRpb25SZW1hcms7DQogICAgICAgICAgICAgIHRoaXMuZGVmYXVsdFJhdGluZ0FycmF5W2l0ZW0uc2NvcmluZ01ldGhvZFVpdGVtSWRdLnN0YXRlID0NCiAgICAgICAgICAgICAgICBpdGVtLmV2YWx1YXRpb25SZXN1bHQ7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgT2JqZWN0LmtleXModGhpcy5kZWZhdWx0UmF0aW5nQXJyYXkpLmZvckVhY2goKGtleSkgPT4gew0KICAgICAgICAgICAgICB0aGlzLmRlZmF1bHRSYXRpbmdBcnJheVtrZXldLnN0YXRlID0gbnVsbDsNCiAgICAgICAgICAgICAgdGhpcy5kZWZhdWx0UmF0aW5nQXJyYXlba2V5XS5yZWFzb24gPSAiIjsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcocmVzcG9uc2UubXNnKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICB0aGlzLnNob3dSZXNwb25zZUZpbGUoKTsNCiAgICB9LA0KCSAgdmFsaWRhdGVTY29yZShpdGVtSWQsIGV2ZW50KSB7DQoJCSAgY29uc3QgaW5wdXRWYWx1ZSA9IHBhcnNlRmxvYXQoZXZlbnQpOw0KCQkgIGNvbnNvbGUubG9nKCJpbnB1dFZhbHVlIiwgaW5wdXRWYWx1ZSk7DQoJCSAgDQoJCSAgLy8g6I635Y+W5b2T5YmN6K+E5YiG6aG555qE5pyA5aSn5YiG5YC8DQoJCSAgY29uc3QgY3VycmVudEl0ZW0gPSB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zLmZpbmQoaXRlbSA9PiBpdGVtLmVudE1ldGhvZEl0ZW1JZCA9PT0gaXRlbUlkKTsNCgkJICBsZXQgbWF4U2NvcmUgPSBudWxsOw0KCQkgIA0KCQkgIGlmIChjdXJyZW50SXRlbSkgew0KCQkJICAvLyDlpoLmnpzmnInliIbmlbDmjKHkvY3vvIzkvb/nlKjmjKHkvY3kuK3nmoTmnIDlpKflgLwNCgkJCSAgaWYgKGN1cnJlbnRJdGVtLnNjb3JlTGV2ZWwgJiYgY3VycmVudEl0ZW0uc2NvcmVMZXZlbC5sZW5ndGggPiAwKSB7DQoJCQkJICBjb25zdCBzY29yZUxldmVscyA9IGN1cnJlbnRJdGVtLnNjb3JlTGV2ZWwuc3BsaXQoJywnKS5tYXAoaXRlbSA9PiBwYXJzZUZsb2F0KGl0ZW0udHJpbSgpKSkuZmlsdGVyKGl0ZW0gPT4gIWlzTmFOKGl0ZW0pKTsNCgkJCQkgIGlmIChzY29yZUxldmVscy5sZW5ndGggPiAwKSB7DQoJCQkJCSAgbWF4U2NvcmUgPSBNYXRoLm1heCguLi5zY29yZUxldmVscyk7DQoJCQkJICB9DQoJCQkgIH0gZWxzZSB7DQoJCQkJICAvLyDlkKbliJnkvb/nlKjphY3nva7nmoTmnIDlpKfliIblgLwNCgkJCQkgIG1heFNjb3JlID0gcGFyc2VGbG9hdChjdXJyZW50SXRlbS5zY29yZSk7DQoJCQkgIH0NCgkJICB9DQoJCSAgDQoJCSAgY29uc29sZS5sb2coIm1heFNjb3JlIiwgbWF4U2NvcmUpOw0KCQkgIA0KCQkgIGlmICghaXNOYU4oaW5wdXRWYWx1ZSkgJiYgbWF4U2NvcmUgIT09IG51bGwpIHsNCgkJCSAgaWYgKGlucHV0VmFsdWUgPiBtYXhTY29yZSkgew0KCQkJCSAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGDovpPlhaXliIbmlbDkuI3og73otoXov4cke21heFNjb3JlfeWIhu+8jOivt+mHjeaWsOi+k+WFpWApOw0KCQkJCSAgLy8g5bCG6L6T5YWl5YC86ZmQ5Yi25Li65pyA5aSn5YiG5pWw5YC8DQoJCQkJICB0aGlzLmRlZmF1bHRSYXRpbmdBcnJheVtpdGVtSWRdLnN0YXRlID0gIiI7DQoJCQkgIH0gZWxzZSBpZiAoaW5wdXRWYWx1ZSA8IDApIHsNCgkJCQkgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6L6T5YWl5YiG5pWw5LiN6IO95bCP5LqOMOWIhiIpOw0KCQkJCSAgdGhpcy5kZWZhdWx0UmF0aW5nQXJyYXlbaXRlbUlkXS5zdGF0ZSA9ICIiOw0KCQkJICB9DQoJCSAgfQ0KCSAgfSwNCiAgICBzaG93UmVzcG9uc2VGaWxlKCkgew0KICAgICAgaWYgKE9iamVjdC5rZXlzKHRoaXMuc2VsZWN0U3VwcGxpZXIpLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeS+m+W6lOWVhiIpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5hY3RpdmVCdXR0b24gPSAncmVzcG9uc2UnOyAvLyDorr7nva7lvZPliY3mv4DmtLvmjInpkq4NCiAgICAgICAgdGhpcy5kb3VibGUgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5wcm9jdXJlbWVudFNob3cgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5yZXNwb25zZVNob3cgPSB0cnVlOw0KICAgICAgICB0aGlzLnJlc3BvbnNlUGRmID0gdGhpcy5maWxlLmZpbGVbdGhpcy5zZWxlY3RTdXBwbGllci5iaWRkZXJJZF07DQogICAgICB9DQogICAgfSwNCiAgICBmaWxlQ29udHJhc3QoKSB7DQogICAgICBpZiAoT2JqZWN0LmtleXModGhpcy5zZWxlY3RTdXBwbGllcikubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36YCJ5oup5L6b5bqU5ZWGIik7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmFjdGl2ZUJ1dHRvbiA9ICdjb250cmFzdCc7IC8vIOiuvue9ruW9k+WJjea/gOa0u+aMiemSrg0KICAgICAgICB0aGlzLmRvdWJsZSA9IHRydWU7DQogICAgICAgIHRoaXMucHJvY3VyZW1lbnRTaG93ID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5yZXNwb25zZVNob3cgPSB0cnVlOw0KICAgICAgICB0aGlzLnJlc3BvbnNlUGRmID0gdGhpcy5maWxlLmZpbGVbdGhpcy5zZWxlY3RTdXBwbGllci5iaWRkZXJJZF07DQogICAgICB9DQogICAgfSwNCiAgICBzaG93SW5mbyhpdGVtKSB7DQoJICAgIC8vIOajgOafpVBERuaYr+WQpua4suafk+WujOaIkA0KCSAgICBpZiAoIXRoaXMuY2FuSnVtcFRvUGFnZSgpKSB7DQoJCSAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIlBERumhtemdouato+WcqOa4suafk+S4re+8jOivt+eojeWAmeWGjeivlSIpOw0KCQkgICAgcmV0dXJuOw0KCSAgICB9DQoJCQkNCiAgICAgIHRoaXMuc2VsZWN0Tm9kZSA9IGl0ZW07DQoNCiAgICAgIC8vIOWmguaenOWPquaYvuekuumHh+i0reaWh+S7tu+8jOS9v+eUqOmHh+i0reaWh+S7tumhteeggeS/oeaBrw0KICAgICAgaWYgKHRoaXMucHJvY3VyZW1lbnRTaG93ICYmICF0aGlzLnJlc3BvbnNlU2hvdykgew0KCSAgICAgIGlmICghdGhpcy5wcm9jdXJlbWVudFBkZlJlbmRlcmVkKSB7DQoJCSAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6YeH6LSt5paH5Lu25q2j5Zyo5riy5p+T5Lit77yM6K+356iN5YCZ5YaN6K+VIik7DQoJCSAgICAgIHJldHVybjsNCgkgICAgICB9DQoJCQkJDQogICAgICAgIGlmIChpdGVtLmp1bXBUb1BhZ2UpIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLnByb2N1cmVtZW50LnNraXBQYWdlKGl0ZW0uanVtcFRvUGFnZSk7DQogICAgICAgIH0gZWxzZSBpZiAodGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2UgJiYgdGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2VbaXRlbS5pdGVtTmFtZV0pIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLnByb2N1cmVtZW50LnNraXBQYWdlKHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlW2l0ZW0uaXRlbU5hbWVdKTsNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOaYvuekuuWTjeW6lOaWh+S7tuaIluWvueavlOaooeW8j++8jOmcgOimgemAieaLqeS+m+W6lOWVhg0KICAgICAgaWYgKE9iamVjdC5rZXlzKHRoaXMuYmlkZGVyRmFjdG9yKS5sZW5ndGggIT0gMCkgew0KICAgICAgICAvLyDot7PovazliLDlk43lupTmlofku7blr7nlupTpobXnoIENCiAgICAgICAgaWYgKHRoaXMucmVzcG9uc2VTaG93ICYmIHRoaXMuJHJlZnMucmVzcG9uc2UpIHsNCgkgICAgICAgIGlmICghdGhpcy5yZXNwb25zZVBkZlJlbmRlcmVkKSB7DQoJCSAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLlk43lupTmlofku7bmraPlnKjmuLLmn5PkuK3vvIzor7fnqI3lgJnlho3or5UiKTsNCgkJICAgICAgICByZXR1cm47DQoJICAgICAgICB9DQoJCQkJCQ0KICAgICAgICAgIHRoaXMuJHJlZnMucmVzcG9uc2Uuc2tpcFBhZ2UoDQogICAgICAgICAgICB0aGlzLmJpZGRlckZhY3Rvclt0aGlzLnNlbGVjdE5vZGUuaXRlbU5hbWVdDQogICAgICAgICAgKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOi3s+i9rOWIsOmHh+i0reaWh+S7tuWvueW6lOmhteeggQ0KICAgICAgICBpZiAodGhpcy5wcm9jdXJlbWVudFNob3cgJiYgdGhpcy4kcmVmcy5wcm9jdXJlbWVudCkgew0KCSAgICAgICAgaWYgKCF0aGlzLnByb2N1cmVtZW50UGRmUmVuZGVyZWQpIHsNCgkJICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIumHh+i0reaWh+S7tuato+WcqOa4suafk+S4re+8jOivt+eojeWAmeWGjeivlSIpOw0KCQkgICAgICAgIHJldHVybjsNCgkgICAgICAgIH0NCgkJCQkJDQogICAgICAgICAgLy8g5Zyo5a+55q+U5qih5byP5LiL77yM6YeH6LSt5paH5Lu25bqU6K+l6Lez6L2s5Yiw6YeH6LSt5paH5Lu255qE5a+55bqU6aG156CB77yM6ICM5LiN5piv5L6b5bqU5ZWG55qE6aG156CBDQogICAgICAgICAgaWYgKHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlICYmIHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlW2l0ZW0uaXRlbU5hbWVdKSB7DQogICAgICAgICAgICB0aGlzLiRyZWZzLnByb2N1cmVtZW50LnNraXBQYWdlKHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlW2l0ZW0uaXRlbU5hbWVdKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g5Zyo5a+55q+U5qih5byP5LiL77yM5aaC5p6c5rKh5pyJ6YeH6LSt5paH5Lu26aG156CB5L+h5oGv77yM5YiZ5Y+q6Lez6L2s5ZON5bqU5paH5Lu255qE6aG156CB77yM5LiN6Lez6L2s6YeH6LSt5paH5Lu2DQogICAgICAgICAgICAvLyDov5nmoLflj6/ku6Xpgb/lhY3ph4fotK3mlofku7blkozlk43lupTmlofku7bmmL7npLrkuI3lkIznmoTlhoXlrrnpgKDmiJDmt7fmt4YNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35YWI6YCJ5oup5L6b5bqU5ZWGIik7DQogICAgICB9DQogICAgfSwNCiAgICBpbml0RGVmYXVsdFJhdGluZ0FycmF5KCl7DQogICAgICBPYmplY3Qua2V5cyh0aGlzLmRlZmF1bHRSYXRpbmdBcnJheSkuZm9yRWFjaCgoa2V5KSA9PiB7DQogICAgICAgIHRoaXMuZGVmYXVsdFJhdGluZ0FycmF5W2tleV0uc3RhdGUgPSBudWxsOw0KICAgICAgICB0aGlzLmRlZmF1bHRSYXRpbmdBcnJheVtrZXldLnJlYXNvbiA9ICIiOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKioNCiAgICAgKiDmoKHpqozmiYDmnInor4TliIbpobnmmK/lkKbloavlhpnlrozmlbQNCiAgICAgKiBAcmV0dXJucyB7Ym9vbGVhbn0g5piv5ZCm5YWo6YOo5aGr5YaZDQogICAgICovDQogICAgdmFsaWRhdGVBbGxSYXRpbmdzKCkgew0KICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHRoaXMuc2NvcmluZ1N5c3RlbS51aXRlbXMpIHsNCiAgICAgICAgY29uc3Qgc3RhdGUgPSB0aGlzLmRlZmF1bHRSYXRpbmdBcnJheVtpdGVtLmVudE1ldGhvZEl0ZW1JZF0uc3RhdGU7DQoNCiAgICAgICAgLy8g6K+E5YiG57uT5p6c5pyq5aGr5YaZDQogICAgICAgIGlmIChzdGF0ZSA9PT0gbnVsbCB8fCBzdGF0ZSA9PT0gJycgfHwgc3RhdGUgPT09IHVuZGVmaW5lZCkgew0KICAgICAgICAgIC8vIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhg6K+35aGr5YaZ6K+E5YiG6aG577yaJHtpdGVtLml0ZW1OYW1lfSDnmoTor4TliIbnu5PmnpxgKTsNCiAgICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWvueS6juWIhuaVsOivhOWIhu+8jOajgOafpeaYr+WQpuS4uuacieaViOaVsOWAvA0KICAgICAgICBpZiAoIWl0ZW0uc2NvcmVMZXZlbCB8fCBpdGVtLnNjb3JlTGV2ZWwubGVuZ3RoID09PSAwIHx8IGl0ZW0uc2NvcmVMZXZlbCA9PT0gbnVsbCB8fCBpdGVtLnNjb3JlTGV2ZWwgPT09IHVuZGVmaW5lZCkgew0KICAgICAgICAgIGNvbnN0IHNjb3JlID0gcGFyc2VGbG9hdChzdGF0ZSk7DQogICAgICAgICAgaWYgKGlzTmFOKHNjb3JlKSB8fCBzY29yZSA8IDApIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhgJHtpdGVtLml0ZW1OYW1lfeeahOivhOWIhuW/hemhu+aYr+acieaViOeahOaVsOWAvOS4lOS4jeiDveWwj+S6jjBgKTsNCiAgICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgICB9DQogICAgICAgICAgLy8g5qOA5p+l5YiG5pWw5piv5ZCm6LaF6L+H5pyA5aSn5YC8DQogICAgICAgICAgY29uc3QgbWF4U2NvcmUgPSB0aGlzLmdldE1heFNjb3JlKGl0ZW0pOw0KICAgICAgICAgIGlmIChzY29yZSA+IG1heFNjb3JlKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoYCR7aXRlbS5pdGVtTmFtZX3nmoTor4TliIbkuI3og73otoXov4cke21heFNjb3JlfeWIhmApOw0KICAgICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlr7nkuo7mnInmjKHkvY3nmoTor4TliIbvvIzmo4Dmn6XmmK/lkKblnKjlhYHorrjnmoTmjKHkvY3ojIPlm7TlhoUNCiAgICAgICAgICBjb25zdCBzY29yZUxldmVscyA9IGl0ZW0uc2NvcmVMZXZlbC5zcGxpdCgnLCcpLm1hcChsZXZlbCA9PiBsZXZlbC50cmltKCkpOw0KICAgICAgICAgIGlmICghc2NvcmVMZXZlbHMuaW5jbHVkZXMoc3RhdGUudG9TdHJpbmcoKSkpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhgJHtpdGVtLml0ZW1OYW1lfeeahOivhOWIhuW/hemhu+mAieaLqeaMh+WumueahOaMoeS9je+8miR7aXRlbS5zY29yZUxldmVsfWApOw0KICAgICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHRydWU7DQogICAgfSwNCg0KICAgIHRtcFNhdmUoKXsNCiAgICAgIGNvbnNvbGUubG9nKCItLS0tLS0t5byA5aeL5L+d5a2Y6K+E5a6h57uT5p6cLS0tLS0tLS0tLS0tLS0tLSIpOw0KDQogICAgICAvLyDlhYjmoKHpqozmiYDmnInor4TliIbpobnmmK/lkKbloavlhpnlrozmlbQNCiAgICAgIGlmICghdGhpcy52YWxpZGF0ZUFsbFJhdGluZ3MoKSkgew0KICAgICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKHsgY29kZTogMCwgc3VjY2VzczogZmFsc2UgfSk7IC8vIOagoemqjOWksei0pQ0KICAgICAgfQ0KDQogICAgICB2YXIgcmF0aW5nQXJyYXkgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuZGVmYXVsdFJhdGluZ0FycmF5KSk7DQoNCiAgICAgIHZhciBkYXRhID0gW107DQogICAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgdGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtcy5sZW5ndGg7IGluZGV4KyspIHsNCiAgICAgICAgY29uc3QgaXRlbSA9IHRoaXMuc2NvcmluZ1N5c3RlbS51aXRlbXNbaW5kZXhdOw0KICAgICAgICBjb25zdCBpdGVtSWQgPSBpdGVtLmVudE1ldGhvZEl0ZW1JZDsNCiAgICAgICAgLy8g6I635Y+W5b2T5YmN6aG55a+55bqU55qE6K+E5YiG57uT5p6cDQogICAgICAgIGNvbnN0IGV2YWx1YXRpb25SZXN1bHQgPSByYXRpbmdBcnJheVtpdGVtSWRdLnN0YXRlOw0KICAgICAgICBpZiAoZXZhbHVhdGlvblJlc3VsdCA9PT0gbnVsbCB8fCBldmFsdWF0aW9uUmVzdWx0ID09PSAiIikgew0KICAgICAgICAgIC8vIOWmguaenOivhOWIhue7k+aenOS4uuepuu+8jOWImeS4jeS/neWtmOatpOadoeS/oeaBrw0KICAgICAgICAgIGNvbnNvbGUubG9nKCItLS0tLS0t6K+E5YiG57uT5p6c5Li656m677yM5LiN5L+d5a2Y5q2k5p2h5L+h5oGvLS0tLS0tLS0tLS0tLS0tLSIpOw0KICAgICAgICAgIGNvbnRpbnVlOw0KICAgICAgICB9DQogICAgICAgICAgLy8g6I635Y+W5b2T5YmN6aG55a+55bqU55qE6K+E5YiG5aSH5rOo77yM6L+b6KGM6Z2e56m65Yik5pat77yM5Li656m65YiZ6LWL5LqI6buY6K6k5YC877yI6L+Z6YeM6K6+5Li656m65a2X56ym5Liy77yJDQogICAgICAgICAgY29uc3QgZXZhbHVhdGlvblJlbWFyayA9IHJhdGluZ0FycmF5W2l0ZW1JZF0ucmVhc29uIHx8ICIiOw0KICAgICAgICAgIGRhdGEucHVzaCh7DQogICAgICAgICAgICBzY29yaW5nTWV0aG9kVWl0ZW1JZDogaXRlbUlkLA0KICAgICAgICAgICAgZXhwZXJ0UmVzdWx0SWQ6IHRoaXMuZXhwZXJ0SW5mby5yZXN1bHRJZCwNCiAgICAgICAgICAgIGVudElkOiB0aGlzLnNlbGVjdFN1cHBsaWVyLmJpZGRlcklkLA0KICAgICAgICAgICAgZXZhbHVhdGlvblJlc3VsdDogZXZhbHVhdGlvblJlc3VsdCwNCiAgICAgICAgICAgIGV2YWx1YXRpb25SZW1hcms6IGV2YWx1YXRpb25SZW1hcmsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgICBpZihkYXRhLmxlbmd0aD4wKXsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCItLS0tLS0t5byA5aeL5ZCO5Y+w5L+d5a2Y6K+E5a6h57uT5p6cLS0tLS0tLS0tLS0tLS0tLSIpOw0KICAgICAgICAgICAgcmV0dXJuIHNjb3JpbmdGYWN0b3JzKGRhdGEpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlc3BvbnNlLm1zZyk7DQogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5L+d5a2Y5oiQ5YqfIik7DQogICAgICAgICAgICAgICAgcmV0dXJuIHsgY29kZTogMjAwLCBzdWNjZXNzOiB0cnVlIH07DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKHJlc3BvbnNlLm1zZyk7DQogICAgICAgICAgICAgICAgcmV0dXJuIHsgY29kZTogcmVzcG9uc2UuY29kZSwgc3VjY2VzczogZmFsc2UgfTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSkuY2F0Y2goKGVycm9yKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuS/neWtmOWksei0pSIpOw0KICAgICAgICAgICAgICByZXR1cm4geyBjb2RlOiAwLCBzdWNjZXNzOiBmYWxzZSB9Ow0KICAgICAgICAgICAgfSk7DQogICAgICAgIH1lbHNlew0KICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoeyBjb2RlOiAyMDAsIHN1Y2Nlc3M6IHRydWUgfSk7IC8vIOayoeacieaVsOaNrumcgOimgeS/neWtmOaXtuS5n+i/lOWbnuaIkOWKnw0KICAgICAgICB9DQogICAgfSwNCiAgICBzYXZlKCkgew0KICAgICAgaWYgKHRoaXMuc3VwcGxpZXIgPT0gIiIpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nkvpvlupTllYYiKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vY29uc3QgZGF0YSA9IHRoaXMuZ2VuZXJhdGluZ1NhdmVkRGF0YSgpOw0KICAgICAgICB2YXIgZGF0YSA9IFtdOw0KICAgICAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgdGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtcy5sZW5ndGg7IGluZGV4KyspIHsNCiAgICAgICAgICBjb25zdCBpdGVtID0gdGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtc1tpbmRleF07DQogICAgICAgICAgY29uc3QgaXRlbUlkID0gaXRlbS5lbnRNZXRob2RJdGVtSWQ7DQogICAgICAgICAgLy8g6I635Y+W5b2T5YmN6aG55a+55bqU55qE6K+E5YiG57uT5p6cDQogICAgICAgICAgY29uc3QgZXZhbHVhdGlvblJlc3VsdCA9IHRoaXMuZGVmYXVsdFJhdGluZ0FycmF5W2l0ZW1JZF0uc3RhdGU7DQogICAgICAgICAgY29uc29sZS5sb2coZXZhbHVhdGlvblJlc3VsdCkNCiAgICAgICAgICBpZiAoZXZhbHVhdGlvblJlc3VsdCA9PT0gbnVsbCB8fCBldmFsdWF0aW9uUmVzdWx0ID09PSAiIikgew0KICAgICAgICAgICAgLy8g5aaC5p6c6K+E5YiG57uT5p6c5Li656m677yM5by55Ye65o+Q56S677yM5o+Q56S65YaF5a655YyF5ZCr6K+l6aG555qEaXRlbU5hbWUNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhg6K+35aGr5YaZ6K+E5YiG6aG577yaJHtpdGVtLml0ZW1OYW1lfSDnmoTor4TliIbnu5PmnpxgKTsNCiAgICAgICAgICAgIHJldHVybjsgLy8g55u05o6l6L+U5Zue77yM5LiN5YaN57un57ut5p6E5bu65pWw5o2u77yM562J5b6F55So5oi35aGr5YaZ5a6M5pW0DQogICAgICAgICAgfQ0KICAgICAgICAgIC8vIOiOt+WPluW9k+WJjemhueWvueW6lOeahOivhOWIhuWkh+azqO+8jOi/m+ihjOmdnuepuuWIpOaWre+8jOS4uuepuuWImei1i+S6iOm7mOiupOWAvO+8iOi/memHjOiuvuS4uuepuuWtl+espuS4su+8iQ0KICAgICAgICAgIGNvbnN0IGV2YWx1YXRpb25SZW1hcmsgPSB0aGlzLmRlZmF1bHRSYXRpbmdBcnJheVtpdGVtSWRdLnJlYXNvbiB8fCAiIjsNCiAgICAgICAgICBkYXRhLnB1c2goew0KICAgICAgICAgICAgc2NvcmluZ01ldGhvZFVpdGVtSWQ6IGl0ZW1JZCwNCiAgICAgICAgICAgIGV4cGVydFJlc3VsdElkOiB0aGlzLmV4cGVydEluZm8ucmVzdWx0SWQsDQogICAgICAgICAgICBlbnRJZDogdGhpcy5zZWxlY3RTdXBwbGllci5iaWRkZXJJZCwNCiAgICAgICAgICAgIGV2YWx1YXRpb25SZXN1bHQ6IGV2YWx1YXRpb25SZXN1bHQsDQogICAgICAgICAgICBldmFsdWF0aW9uUmVtYXJrOiBldmFsdWF0aW9uUmVtYXJrDQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgICAgc2NvcmluZ0ZhY3RvcnMoZGF0YSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2VzcyhyZXNwb25zZS5tc2cpOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcocmVzcG9uc2UubXNnKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g55Sf5oiQ5L+d5a2Y5pWw5o2uDQogICAgZ2VuZXJhdGluZ1NhdmVkRGF0YSgpIHsNCiAgICAgIHZhciBkYXRhID0gW107DQogICAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgdGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtcy5sZW5ndGg7IGluZGV4KyspIHsNCiAgICAgICAgZGF0YS5wdXNoKHsNCiAgICAgICAgICBzY29yaW5nTWV0aG9kVWl0ZW1JZDoNCiAgICAgICAgICAgIHRoaXMuc2NvcmluZ1N5c3RlbS51aXRlbXNbaW5kZXhdLmVudE1ldGhvZEl0ZW1JZCwNCiAgICAgICAgICBleHBlcnRSZXN1bHRJZDogdGhpcy5leHBlcnRJbmZvLnJlc3VsdElkLA0KICAgICAgICAgIGVudElkOiB0aGlzLnNlbGVjdFN1cHBsaWVyLmJpZGRlcklkLA0KICAgICAgICAgIGV2YWx1YXRpb25SZXN1bHQ6DQogICAgICAgICAgICB0aGlzLmRlZmF1bHRSYXRpbmdBcnJheVsNCiAgICAgICAgICAgICAgdGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtc1tpbmRleF0uZW50TWV0aG9kSXRlbUlkDQogICAgICAgICAgICBdLnN0YXRlLA0KICAgICAgICAgIGV2YWx1YXRpb25SZW1hcms6DQogICAgICAgICAgICB0aGlzLmRlZmF1bHRSYXRpbmdBcnJheVsNCiAgICAgICAgICAgICAgdGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtc1tpbmRleF0uZW50TWV0aG9kSXRlbUlkDQogICAgICAgICAgICBdLnJlYXNvbiwNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICByZXR1cm4gZGF0YTsNCiAgICB9LA0KICAgIHN1Ym1pdCgpIHsNCiAgICAgICAgdGhpcy50bXBTYXZlKCkudGhlbigoc2F2ZVJlc3VsdCkgPT4gew0KICAgICAgICAgIC8vIOajgOafpeS/neWtmOe7k+aenO+8jOWmguaenOagoemqjOWksei0peWImeS4jee7p+e7reaPkOS6pA0KICAgICAgICAgIGlmICghc2F2ZVJlc3VsdCB8fCBzYXZlUmVzdWx0LnN1Y2Nlc3MgPT09IGZhbHNlKSB7DQogICAgICAgICAgICByZXR1cm47IC8vIOagoemqjOWksei0pe+8jOS4jee7p+e7reaPkOS6pOa1geeoiw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGNvbnN0IGRhdGEgPSB7DQogICAgICAgICAgICBwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwNCiAgICAgICAgICAgIGV4cGVydFJlc3VsdElkOiB0aGlzLmV4cGVydEluZm8ucmVzdWx0SWQsDQogICAgICAgICAgICBzY29yaW5nTWV0aG9kSXRlbUlkOiB0aGlzLiRyb3V0ZS5xdWVyeS5zY29yaW5nTWV0aG9kSXRlbUlkLA0KICAgICAgICAgIH07DQogICAgICAgICAgY2hlY2tSZXZpZXdTdW1tYXJ5KGRhdGEpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIC8vIOS/ruaUueS4k+Wutui/m+W6pg0KICAgICAgICAgICAgY29uc3Qgc3RhdHVzID0gew0KICAgICAgICAgICAgICBldmFsRXhwZXJ0U2NvcmVJbmZvSWQ6IEpTT04ucGFyc2UoDQogICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oImV2YWxFeHBlcnRTY29yZUluZm8iKQ0KICAgICAgICAgICAgICApLmV2YWxFeHBlcnRTY29yZUluZm9JZCwNCiAgICAgICAgICAgICAgZXZhbFN0YXRlOiAxLA0KICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIGVkaXRFdmFsRXhwZXJ0U2NvcmVJbmZvKHN0YXR1cykudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaPkOS6pOaIkOWKnyIpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIHRoaXMuJGVtaXQoInNlbmQiLCAidHdvIik7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhyZXNwb25zZS5tc2cpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqDQogICAgICog5pi+56S66YeH6LSt5paH5Lu2UERGDQogICAgICovDQogICAgdmlld1B1cmNoYXNpbmcoKSB7DQogICAgICB0aGlzLmFjdGl2ZUJ1dHRvbiA9ICdwcm9jdXJlbWVudCc7IC8vIOiuvue9ruW9k+WJjea/gOa0u+aMiemSrg0KICAgICAgdGhpcy5kb3VibGUgPSBmYWxzZTsgLy8g5Y2V5paH5Lu25qih5byPDQogICAgICB0aGlzLnJlc3BvbnNlU2hvdyA9IGZhbHNlOyAvLyDkuI3mmL7npLrlk43lupTmlofku7YNCiAgICAgIHRoaXMucHJvY3VyZW1lbnRTaG93ID0gdHJ1ZTsgLy8g5pi+56S66YeH6LSt5paH5Lu2DQoNCiAgICAgIC8vIOWPs+S+p+ivhOWIhumhueaYvuekuuS4uumHh+i0reaWh+S7tueahOivhOWIhumhuQ0KICAgICAgbGV0IHBhZ2VQcm9jdXJlbWVudEFyciA9IFtdOyAvLyDph4fotK3mlofku7bor4TliIbpobnmlbDnu4QNCiAgICAgIGZvciAobGV0IGl0ZW0gaW4gdGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2Upew0KICAgICAgICBwYWdlUHJvY3VyZW1lbnRBcnIucHVzaCh7DQogICAgICAgICAgaXRlbU5hbWU6IGl0ZW0sDQogICAgICAgICAganVtcFRvUGFnZTogdGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2VbaXRlbV0NCiAgICAgICAgfSkNCiAgICAgIH0NCg0KICAgICAgY29uc29sZS5sb2codGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtcyk7DQogICAgICBjb25zb2xlLmxvZyhwYWdlUHJvY3VyZW1lbnRBcnIpDQogICAgICB0aGlzLnBhZ2VQcm9jdXJlbWVudCA9IFtdOw0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zLmxlbmd0aDtpKyspew0KICAgICAgICBmb3IgKGxldCBqID0gMDsgaiA8IHBhZ2VQcm9jdXJlbWVudEFyci5sZW5ndGg7aisrKXsNCiAgICAgICAgICBpZiAodGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtc1tpXS5pdGVtTmFtZSA9PSBwYWdlUHJvY3VyZW1lbnRBcnJbal0uaXRlbU5hbWUpew0KICAgICAgICAgICAgdGhpcy5wYWdlUHJvY3VyZW1lbnQucHVzaCh7Li4udGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtc1tpXSwuLi5wYWdlUHJvY3VyZW1lbnRBcnJbal19KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGNvbnNvbGUubG9nKHRoaXMucGFnZVByb2N1cmVtZW50KQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDot7PovazliLDph4fotK3mlofku7blr7nlupTpobXnoIENCiAgICAgKiBAcGFyYW0ge09iamVjdH0gaXRlbSAtIOivhOWIhumhueWvueixoQ0KICAgICAqLw0KICAgIGp1bXBUb1Byb2N1cmVtZW50UGFnZShpdGVtKSB7DQogICAgICBpZiAoaXRlbS5qdW1wVG9QYWdlICYmIHRoaXMuJHJlZnMucHJvY3VyZW1lbnQpIHsNCiAgICAgICAgdGhpcy4kcmVmcy5wcm9jdXJlbWVudC5za2lwUGFnZShpdGVtLmp1bXBUb1BhZ2UpOw0KICAgICAgfQ0KICAgIH0sDQoJICANCgkgIC8qKg0KCSAgICog5qOA5p+l5piv5ZCm5Y+v5Lul6Lez6L2s6aG16Z2iDQoJICAgKiBAcmV0dXJucyB7Ym9vbGVhbn0g5piv5ZCm5Y+v5Lul6Lez6L2sDQoJICAgKi8NCgkgIGNhbkp1bXBUb1BhZ2UoKSB7DQoJCSAgLy8g5aaC5p6c5Y+q5pi+56S66YeH6LSt5paH5Lu2DQoJCSAgaWYgKHRoaXMucHJvY3VyZW1lbnRTaG93ICYmICF0aGlzLnJlc3BvbnNlU2hvdykgew0KCQkJICByZXR1cm4gdGhpcy5wcm9jdXJlbWVudFBkZlJlbmRlcmVkOw0KCQkgIH0NCgkJICAvLyDlpoLmnpzlj6rmmL7npLrlk43lupTmlofku7YNCgkJICBpZiAodGhpcy5yZXNwb25zZVNob3cgJiYgIXRoaXMucHJvY3VyZW1lbnRTaG93KSB7DQoJCQkgIHJldHVybiB0aGlzLnJlc3BvbnNlUGRmUmVuZGVyZWQ7DQoJCSAgfQ0KCQkgIC8vIOWmguaenOWvueavlOaooeW8j++8iOS4pOS4qumDveaYvuekuu+8iQ0KCQkgIGlmICh0aGlzLnJlc3BvbnNlU2hvdyAmJiB0aGlzLnByb2N1cmVtZW50U2hvdykgew0KCQkJICByZXR1cm4gdGhpcy5yZXNwb25zZVBkZlJlbmRlcmVkICYmIHRoaXMucHJvY3VyZW1lbnRQZGZSZW5kZXJlZDsNCgkJICB9DQoJCSAgcmV0dXJuIGZhbHNlOw0KCSAgfSwNCgkgIC8qKg0KCSAgICog5aSE55CGUERG5riy5p+T54q25oCB5Y+Y5YyWDQoJICAgKiBAcGFyYW0ge2Jvb2xlYW59IGlzUmVuZGVyZWQg5piv5ZCm5riy5p+T5a6M5oiQDQoJICAgKiBAcGFyYW0ge3N0cmluZ30gcGRmVHlwZSBQREbnsbvlnovvvJoncmVzcG9uc2UnIOaIliAncHJvY3VyZW1lbnQnDQoJICAgKi8NCgkgIGhhbmRsZVBkZlJlbmRlclN0YXR1c0NoYW5nZShpc1JlbmRlcmVkLCBwZGZUeXBlKSB7DQoJCSAgaWYgKHBkZlR5cGUgPT09ICdyZXNwb25zZScpIHsNCgkJCSAgdGhpcy5yZXNwb25zZVBkZlJlbmRlcmVkID0gaXNSZW5kZXJlZDsNCgkJICB9IGVsc2UgaWYgKHBkZlR5cGUgPT09ICdwcm9jdXJlbWVudCcpIHsNCgkJCSAgdGhpcy5wcm9jdXJlbWVudFBkZlJlbmRlcmVkID0gaXNSZW5kZXJlZDsNCgkJICB9DQoJCSAgDQoJCSAgaWYgKGlzUmVuZGVyZWQpIHsNCgkJCSAgY29uc29sZS5sb2coYCR7cGRmVHlwZSA9PT0gJ3Jlc3BvbnNlJyA/ICflk43lupQnIDogJ+mHh+i0rSd95paH5Lu25riy5p+T5a6M5oiQ77yM5Y+v5Lul6L+b6KGM6aG16Z2i6Lez6L2sYCk7DQoJCSAgfQ0KCSAgfSwNCgkgIA0KICAgIC8vIOi3s+i9rOWIsOS6jOasoeaKpeS7tw0KICAgIHNlY29uZE9mZmVyKCkgew0KICAgICAgY29uc3QgcXVlcnkgPSB7DQogICAgICAgIHByb2plY3RJZDogdGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkLA0KICAgICAgICB6amhtOiB0aGlzLiRyb3V0ZS5xdWVyeS56amhtLA0KICAgICAgICBzY29yaW5nTWV0aG9kSXRlbUlkOiBKU09OLnBhcnNlKA0KICAgICAgICAgIGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJ0ZW5kZXJPZmZlclNjb3JpbmdNZXRob2RJdGVtcyIpDQogICAgICAgICksDQogICAgICB9Ow0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAiL3NlY29uZE9mZmVyIiwgcXVlcnk6IHF1ZXJ5IH0pOw0KICAgIH0sDQogICAgLy8g6Lez6L2s5Yiw6K+i5qCHDQogICAgYmlkSW5xdWlyeSgpIHsNCiAgICAgIGNvbnN0IHF1ZXJ5ID0gew0KICAgICAgICBwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwNCiAgICAgICAgempobTogdGhpcy4kcm91dGUucXVlcnkuempobSwNCiAgICAgICAgc2NvcmluZ01ldGhvZEl0ZW1JZDogSlNPTi5wYXJzZSgNCiAgICAgICAgICBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgidGVuZGVyT2ZmZXJTY29yaW5nTWV0aG9kSXRlbXMiKQ0KICAgICAgICApLA0KICAgICAgfTsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogIi9iaWRJbnF1aXJ5IiwgcXVlcnk6IHF1ZXJ5IH0pOw0KICAgIH0sDQogICAgLy8g6I635Y+W5Zug57Sg5a+55bqU6aG156CBDQogICAgZ2V0RmFjdG9yc1BhZ2UoKSB7DQogICAgICB0aGlzLmZhY3RvcnNQYWdlID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZW50RG9jUmVzcG9uc2VQYWdlIikpOw0KICAgIH0sDQogICAgZG93bmxvYWRGaWxlKGl0ZW0pew0KICAgICAgdGhpcy4kZG93bmxvYWQuemlwKGl0ZW0uZmlsZVBhdGgsaXRlbS5maWxlTmFtZSk7DQogICAgfSwNCiAgICANCiAgICAvKioNCiAgICAgKiDojrflj5bor4TliIbpobnnmoTmnIDlpKfliIblgLwNCiAgICAgKiBAcGFyYW0ge09iamVjdH0gaXRlbSAtIOivhOWIhumhueWvueixoQ0KICAgICAqIEByZXR1cm5zIHtudW1iZXJ9IOacgOWkp+WIhuWAvA0KICAgICAqLw0KICAgIGdldE1heFNjb3JlKGl0ZW0pIHsNCiAgICAgIGlmICghaXRlbSkgcmV0dXJuIDA7DQogICAgICANCiAgICAgIC8vIOWmguaenOacieWIhuaVsOaMoeS9je+8jOS9v+eUqOaMoeS9jeS4reeahOacgOWkp+WAvA0KICAgICAgaWYgKGl0ZW0uc2NvcmVMZXZlbCAmJiBpdGVtLnNjb3JlTGV2ZWwubGVuZ3RoID4gMCAmJiBpdGVtLnNjb3JlTGV2ZWwgIT09IG51bGwgJiYgaXRlbS5zY29yZUxldmVsICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgY29uc3Qgc2NvcmVMZXZlbHMgPSBpdGVtLnNjb3JlTGV2ZWwuc3BsaXQoJywnKS5tYXAobGV2ZWwgPT4gcGFyc2VGbG9hdChsZXZlbC50cmltKCkpKS5maWx0ZXIobGV2ZWwgPT4gIWlzTmFOKGxldmVsKSk7DQogICAgICAgIGlmIChzY29yZUxldmVscy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgcmV0dXJuIE1hdGgubWF4KC4uLnNjb3JlTGV2ZWxzKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDlkKbliJnkvb/nlKjphY3nva7nmoTmnIDlpKfliIblgLwNCiAgICAgIHJldHVybiBwYXJzZUZsb2F0KGl0ZW0uc2NvcmUpIHx8IDA7DQogICAgfSwNCgkgIA0KCSAgLy8gPT09PT09PT09PSDmgqzlgZznm7jlhbMgPT09PT09PT09PQ0KCSAgLyoqDQoJICAgKiDmmL7npLror4TliIbpobnmgqzmta7moYYNCgkgICAqIEBwYXJhbSB7T2JqZWN0fSBmYWN0b3JJdGVtIOivhOWIhumhueWvueixoQ0KCSAgICovDQoJICBzaG93RmFjdG9yVG9vbHRpcChmYWN0b3JJdGVtKSB7DQoJCSAgaWYgKCFmYWN0b3JJdGVtLml0ZW1SZW1hcmspIHJldHVybjsgLy8g5aaC5p6c5rKh5pyJ6K+E5a6h5YaF5a655YiZ5LiN5pi+56S6DQoJCSAgDQoJCSAgLy8g5riF6Zmk5LmL5YmN55qE5a6a5pe25ZmoDQoJCSAgaWYgKHRoaXMudG9vbHRpcFRpbWVyKSB7DQoJCQkgIGNsZWFyVGltZW91dCh0aGlzLnRvb2x0aXBUaW1lcik7DQoJCSAgfQ0KCQkgIA0KCQkgIC8vIOW7tui/n+aYvuekuuaCrOa1ruahhu+8jOmBv+WFjeW/q+mAn+enu+WKqOaXtumikee5geaYvuekug0KCQkgIHRoaXMudG9vbHRpcFRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7DQoJCQkgIHRoaXMuaG92ZXJlZEZhY3Rvck5vZGUgPSBmYWN0b3JJdGVtOw0KCQkgIH0sIDMwMCk7IC8vIDMwMG1z5bu26L+fDQoJICB9LA0KCSAgDQoJICAvKioNCgkgICAqIOmakOiXj+ivhOWIhumhueaCrOa1ruahhg0KCSAgICovDQoJICBoaWRlRmFjdG9yVG9vbHRpcCgpIHsNCgkJICAvLyDmuIXpmaTlrprml7blmagNCgkJICBpZiAodGhpcy50b29sdGlwVGltZXIpIHsNCgkJCSAgY2xlYXJUaW1lb3V0KHRoaXMudG9vbHRpcFRpbWVyKTsNCgkJCSAgdGhpcy50b29sdGlwVGltZXIgPSBudWxsOw0KCQkgIH0NCgkJICANCgkJICAvLyDlu7bov5/pmpDol4/vvIznu5nnlKjmiLfml7bpl7Tnp7vliqjliLDmgqzmta7moYbkuIoNCgkJICBzZXRUaW1lb3V0KCgpID0+IHsNCgkJCSAgdGhpcy5ob3ZlcmVkRmFjdG9yTm9kZSA9IG51bGw7DQoJCSAgfSwgMTAwKTsNCgkgIH0sDQoJICANCgkgIC8qKg0KCSAgICog5riF6Zmk5oKs5rWu5qGG5a6a5pe25Zmo77yI5b2T6byg5qCH56e75Yqo5Yiw5oKs5rWu5qGG5LiK5pe277yJDQoJICAgKi8NCgkgIGNsZWFyVG9vbHRpcFRpbWVyKCkgew0KCQkgIGlmICh0aGlzLnRvb2x0aXBUaW1lcikgew0KCQkJICBjbGVhclRpbWVvdXQodGhpcy50b29sdGlwVGltZXIpOw0KCQkJICB0aGlzLnRvb2x0aXBUaW1lciA9IG51bGw7DQoJCSAgfQ0KCSAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMuaW5pdCgpOw0KICAgIHRoaXMuZ2V0RmFjdG9yc1BhZ2UoKTsNCiAgfSwNCgkNCgliZWZvcmVEZXN0cm95KCkgew0KCQkvLyDmuIXnkIblrprml7blmagNCgkJaWYgKHRoaXMudG9vbHRpcFRpbWVyKSB7DQoJCQljbGVhclRpbWVvdXQodGhpcy50b29sdGlwVGltZXIpOw0KCQkJdGhpcy50b29sdGlwVGltZXIgPSBudWxsOw0KCQl9DQoJfSwNCn07DQo="}, {"version": 3, "sources": ["one.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAu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file": "one.vue", "sourceRoot": "src/views/expertReview/business", "sourcesContent": ["<template>\r\n  <div class=\"business-review-container\">\r\n    <div class=\"business-review-main\">\r\n      <div class=\"business-review-header\">\r\n        <div class=\"business-review-title-group\">\r\n          <div class=\"business-review-title\">商务标评审</div>\r\n          <div class=\"business-review-step-group\">\r\n            <div class=\"business-review-step-text\">该页面操作说明</div>\r\n            <el-image class=\"business-review-step-img\" :src=\"srcList[0]\" :preview-src-list=\"srcList\">\r\n            </el-image>\r\n          </div>\r\n        </div>\r\n\t      \r\n\t      <!-- 文件列表 -->\r\n\t      <div class=\"fileList\" style=\"width: 200px; border-right: 1px solid #e6e6e6; border-left: 1px solid #e6e6e6; padding: 10px; overflow-y: auto;\">\r\n\t\t      <div style=\"font-weight: bold; margin-bottom: 10px; color: #333;\">响应文件附件下载</div>\r\n\t\t      <el-card\r\n\t\t\t      v-for=\"(item, index) in attachmentsList\"\r\n\t\t\t      :key=\"index\"\r\n\t\t\t      class=\"fileItem\"\r\n\t\t\t      shadow=\"hover\"\r\n\t\t\t      @click.native=\"downloadFile(item)\"\r\n\t\t\t      style=\"margin-bottom: 8px; cursor: pointer;\"\r\n\t\t      >\r\n\t\t\t      <div style=\"display: flex; align-items: center; padding: 5px;\">\r\n\t\t\t\t      <i class=\"el-icon-document\" style=\"margin-right: 8px; color: #409EFF;\"></i>\r\n\t\t\t\t      <span style=\"font-size: 12px; flex: 1; word-break: break-all;\">{{ item.fileName }}</span>\r\n\t\t\t\t      <i class=\"el-icon-download\" style=\"margin-left: 8px; color: #999;\"></i>\r\n\t\t\t      </div>\r\n\t\t      </el-card>\r\n\t      </div>\r\n\t      \r\n        <div class=\"business-review-header-btns\">\r\n          <el-button class=\"item-button\" @click=\"bidInquiry\">询标</el-button>\r\n          <el-button class=\"item-button\" v-if=\"expertInfo.expertLeader==1\" @click=\"secondOffer\">发起二次报价</el-button>\r\n          <div class=\"business-review-header-btns-group\">\r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'procurement' ? 'business-blue-btn-active' : 'business-blue-btn']\"\r\n              @click=\"viewPurchasing\">采购文件</el-button>\r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'response' ? 'business-blue-btn-active' : 'business-blue-btn']\"\r\n              @click=\"showResponseFile()\">响应文件</el-button>\r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'contrast' ? 'business-blue-btn-active' : 'business-blue-btn']\"\r\n              @click=\"fileContrast\">对比</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div style=\"height:82%\">\r\n        <!-- PDF预览区域 - 保持原始尺寸 -->\r\n        <div class=\"business-review-pdf-group\">\r\n\t        <div v-show=\"procurementShow\" :class=\"['business-review-pdf', double ? 'business-review-pdf-border-left' : '']\">\r\n\t\t        <!--            <pdfView ref=\"procurement\" :pdfurl=\"procurementPdf\" :uni_key=\"'procurement'\"></pdfView>-->\r\n\t\t        <PdfViewImproved ref=\"procurement\"  :pdfurl=\"procurementPdf\"  :page-height=\"800\" :buffer-size=\"2\"  @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"/>\r\n\t        \r\n\t        </div>\r\n\t        \r\n          <div v-show=\"responseShow\" :class=\"['business-review-pdf', double ? 'business-review-pdf-border-right' : '']\">\r\n<!--            <pdfView ref=\"response\" :pdfurl=\"responsePdf\" :uni_key=\"'response'\"></pdfView>-->\r\n\t          <PdfViewImproved ref=\"response\"  :pdfurl=\"responsePdf\"  :page-height=\"800\" :buffer-size=\"2\" @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"/>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"business-review-divider\"></div>\r\n    <div class=\"business-review-side\">\r\n      <div class=\"business-review-select-group\">\r\n        <el-select class=\"business-review-select\" v-model=\"supplier\" placeholder=\"请选择供应商\" @change=\"handleChange\">\r\n          <el-option v-for=\"item in options\" :key=\"item.bidderName\" :label=\"item.bidderName\" :value=\"item.bidderName\">\r\n          </el-option>\r\n        </el-select>\r\n      </div>\r\n      <div class=\"business-review-side-content\">\r\n        <!-- 响应文件评分项显示 -->\r\n\t      <template v-if=\"responseShow || double\">\r\n\t\t      <!-- PDF渲染状态提示 -->\r\n\t\t      <div v-if=\"!responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t\t      <i class=\"el-icon-loading\"></i>\r\n\t\t\t      <span>响应文件正在渲染中，请稍候...</span>\r\n\t\t      </div>\r\n\t\t      <div v-else class=\"render-status-tip success\">\r\n\t\t\t      <i class=\"el-icon-success\"></i>\r\n\t\t\t      <span>响应文件渲染完成，可以点击跳转</span>\r\n\t\t      </div>\r\n\t\t      <div v-for=\"(item, index) in scoringSystem.uitems\" :key=\"'response-' + index\"\r\n\t\t           class=\"factor-item business-review-factor-item\"\r\n\t\t           @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t      <!-- 悬浮框 -->\r\n\t\t\t      <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t           class=\"factor-tooltip\"\r\n\t\t\t           @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t      <div class=\"tooltip-header\">\r\n\t\t\t\t\t      <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t      <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t      </div>\r\n\t\t\t\t      <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t      </div>\r\n\t\t\t      \r\n\t\t\t      <div>\r\n\t\t\t\t      <div class=\"factors\">\r\n\t\t\t\t\t      <div class=\"business-review-factor-title-group\">\r\n\t\t\t\t\t\t      <div class=\"business-review-factor-title\" @click=\"showInfo(item)\">\r\n\t\t\t\t\t\t\t      {{ item.itemName }}\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t\t      <div class=\"business-review-factor-score-group\">\r\n\t\t\t\t\t\t      <div v-if=\"!(item.scoreLevel.length == 0 || item.scoreLevel == null || item.scoreLevel == undefined)\">\r\n\t\t\t\t\t\t\t      <el-radio v-for=\"(score,index) in item.scoreLevel.split(',')\" :key=\"index\" v-model=\"defaultRatingArray[item.entMethodItemId].state\" :label=\"score\"><span class=\"business-review-factor-score\">{{ score }}</span></el-radio>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t\t      <div v-else>\r\n\t\t\t\t\t\t\t      <el-input\r\n\t\t\t\t\t\t\t\t      placeholder=\"请输入分数\"\r\n\t\t\t\t\t\t\t\t      v-model=\"defaultRatingArray[item.entMethodItemId].state\"\r\n\t\t\t\t\t\t\t\t      @input=\"handleScoreInput(item.entMethodItemId, $event)\"\r\n\t\t\t\t\t\t\t\t      @keypress=\"onlyNumber\"\r\n\t\t\t\t\t\t\t\t      type=\"number\"\r\n\t\t\t\t\t\t\t\t      step=\"0.1\"\r\n\t\t\t\t\t\t\t\t      min=\"0\">\r\n\t\t\t\t\t\t\t      </el-input>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t      </div>\r\n\t\t\t      </div>\r\n\t\t      </div>\r\n\t      </template>\r\n\t      <template v-else-if=\"procurementShow\" >\r\n\t\t      <!-- PDF渲染状态提示 -->\r\n\t\t      <div v-if=\"!procurementPdfRendered\" class=\"render-status-tip\">\r\n\t\t\t      <i class=\"el-icon-loading\"></i>\r\n\t\t\t      <span>采购文件正在渲染中，请稍候...</span>\r\n\t\t      </div>\r\n\t\t      <div v-else class=\"render-status-tip success\">\r\n\t\t\t      <i class=\"el-icon-success\"></i>\r\n\t\t\t      <span>采购文件渲染完成，可以点击跳转</span>\r\n\t\t      </div>\r\n\t\t      \r\n\t\t      <!-- 采购文件评分项显示 -->\r\n\t\t      <div v-for=\"(item, index) in pageProcurement\" :key=\"'procurement-' + index\"\r\n\t\t           class=\"factor-item business-review-factor-item\"\r\n\t\t           @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t      <!-- 悬浮框 -->\r\n\t\t\t      <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t           class=\"factor-tooltip\"\r\n\t\t\t           @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t           @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t      <div class=\"tooltip-header\">\r\n\t\t\t\t\t      <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t      <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t      </div>\r\n\t\t\t\t      <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t      </div>\r\n\t\t\t      \r\n\t\t\t      <div>\r\n\t\t\t\t      <div class=\"factors\">\r\n\t\t\t\t\t      <div class=\"business-review-factor-title-group\">\r\n\t\t\t\t\t\t      <div class=\"business-review-factor-title\" @click=\"jumpToProcurementPage(item)\">\r\n\t\t\t\t\t\t\t      {{ item.itemName }}\r\n\t\t\t\t\t\t\t      <span style=\"font-size: 12px;color: red;\"> 最高{{ getMaxScore(item) }}分</span>\r\n\t\t\t\t\t\t      </div>\r\n\t\t\t\t\t      </div>\r\n\t\t\t\t      </div>\r\n\t\t\t      </div>\r\n\t\t      </div>\r\n\t      </template>\r\n       \r\n\t      <div class=\"business-review-submit-group\">\r\n          <!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"save\">保存</el-button></div> -->\r\n          <div><el-button class=\"item-button-little business-review-submit-btn\" @click=\"submit\">提交</el-button></div>\r\n        </div>\r\n\r\n        <div class=\"business-review-content\">\r\n          <div class=\"business-review-content-title\">评审内容：</div>\r\n          <div class=\"business-review-content-remark\" v-html=\"selectNode.itemRemark\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  supplierInfo,\r\n  approvalProcess,\r\n  scoringFactors,\r\n  checkReviewSummary,\r\n  filesById,\r\n} from \"@/api/expert/review\";\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\";\r\nimport { reEvaluate } from \"@/api/expert/review\";\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      options: [],\r\n      scoringSystem: [],\r\n      selectNode: {},\r\n      supplier: \"\",\r\n      selectSupplier: {},\r\n      expertInfo: {},\r\n      defaultRatingArray: {},\r\n      file: {},\r\n      responseShow: false,\r\n      procurementShow: false,\r\n      double: false,\r\n\r\n      entDocResponsePage: {},\r\n      factorsPage: {},\r\n      bidderFactor: {},\r\n      responsePdf: null,\r\n      procurementPdf: null,\r\n\t    currentMaxScore: null,\r\n\r\n      // 按钮状态管理\r\n      activeButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n      // 采购文件相关数据\r\n      entDocProcurementPage: {}, // 采购文件页码信息\r\n      pageProcurement: [], // 采购文件的评分项\r\n      attachmentsList:[], // 文件列表\r\n\t    \r\n\t    // PDF渲染状态管理\r\n\t    responsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n\t    procurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n      srcList: [\"/evalution/help.jpg\"],\r\n\t    \r\n\t    // 悬停状态管理\r\n\t    hoveredFactorNode: null, // 悬停时的评分项\r\n\t    tooltipTimer: null, // 悬浮框显示定时器\r\n    };\r\n  },\r\n  methods: {\r\n    /**\r\n     * 限制输入框只能输入数字和小数点\r\n     * @param {Event} event - 键盘事件\r\n     */\r\n    onlyNumber(event) {\r\n      // 获取按键的字符码\r\n      const charCode = event.which || event.keyCode;\r\n\r\n      // 允许的字符：数字(48-57)、小数点(46)、退格(8)、删除(46)、Tab(9)、Enter(13)、方向键(37-40)\r\n      if (\r\n        (charCode >= 48 && charCode <= 57) || // 数字 0-9\r\n        charCode === 46 || // 小数点\r\n        charCode === 8 ||  // 退格键\r\n        charCode === 9 ||  // Tab键\r\n        charCode === 13 || // Enter键\r\n        (charCode >= 37 && charCode <= 40) // 方向键\r\n      ) {\r\n        return true;\r\n      }\r\n\r\n      // 阻止其他字符输入\r\n      event.preventDefault();\r\n      return false;\r\n    },\r\n\r\n    /**\r\n     * 处理分数输入，确保只能输入有效的数字\r\n     * @param {string} itemId - 评估项ID\r\n     * @param {string} value - 输入值\r\n     */\r\n    handleScoreInput(itemId, value) {\r\n      // 移除非数字字符（保留小数点）\r\n      let cleanValue = value.replace(/[^\\d.]/g, '');\r\n\r\n      // 确保只有一个小数点\r\n      const parts = cleanValue.split('.');\r\n      if (parts.length > 2) {\r\n        cleanValue = parts[0] + '.' + parts.slice(1).join('');\r\n      }\r\n\r\n      // 限制小数点后最多2位\r\n      if (parts.length === 2 && parts[1].length > 2) {\r\n        cleanValue = parts[0] + '.' + parts[1].substring(0, 2);\r\n      }\r\n\r\n      // 更新值\r\n      this.defaultRatingArray[itemId].state = cleanValue;\r\n\r\n      // 调用原有的验证方法\r\n      this.validateScore(itemId, cleanValue);\r\n    },\r\n\r\n    init() {\r\n      const expertInfo = JSON.parse(localStorage.getItem(\"expertInfo\"));\r\n      this.entDocResponsePage = JSON.parse(\r\n        localStorage.getItem(\"entDocResponsePage\")\r\n      );\r\n      // 初始化采购文件页码信息\r\n      this.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\"));\r\n      supplierInfo({ projectId: this.$route.query.projectId, isAbandonedBid: 0 }).then(\r\n        (response) => {\r\n          if (response.code == 200) {\r\n            this.options = response.rows.filter(item => item.isAbandonedBid == 0);\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        }\r\n      );\r\n      approvalProcess(this.$route.query.projectId, expertInfo.resultId).then(\r\n        (response) => {\r\n          if (response.code == 200) {\r\n            // 文件列表\r\n            this.attachmentsList = response.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n\r\n            this.scoringSystem =\r\n              response.data.scoringMethodUinfo.scoringMethodItems.find(\r\n                (item) => {\r\n                  return (\r\n                    item.scoringMethodItemId ==\r\n                    this.$route.query.scoringMethodItemId\r\n                  );\r\n                }\r\n              );\r\n            localStorage.setItem(\r\n              \"evalProjectEvaluationProcess\",\r\n              JSON.stringify(this.scoringSystem.evalProjectEvaluationProcess)\r\n            );\r\n\r\n            this.defaultRatingArray = this.scoringSystem.uitems.reduce(\r\n              (acc, _, index) => {\r\n                acc[this.scoringSystem.uitems[index].entMethodItemId] = {\r\n                  state: null,\r\n                  reason: \"\",\r\n                };\r\n                return acc;\r\n              },\r\n              {}\r\n            );\r\n            console.log(\"this.scoringSystem.items\", this.scoringSystem.uitems);\r\n          } else {\r\n            this.$messgae.warning(response.msg);\r\n          }\r\n        }\r\n      );\r\n      filesById(this.$route.query.projectId).then((response) => {\r\n        if (response.code == 200) {\r\n          this.file = response.data;\r\n          if (this.file.tenderNoticeFilePath != undefined) {\r\n            this.procurementPdf = this.file.tenderNoticeFilePath;\r\n          }\r\n          // if (this.file.file != undefined) {\r\n          //   this.responsePdf = this.file.file[0];\r\n          // }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      // 初始化专家信息\r\n      this.initExpertInfo();\r\n    },\r\n\r\n    /**\r\n     * 初始化专家信息\r\n     */\r\n    initExpertInfo() {\r\n      try {\r\n        const itemString = localStorage.getItem(\"expertInfo\");\r\n        if (itemString) {\r\n          this.expertInfo = JSON.parse(itemString);\r\n          console.log(\"专家信息已初始化\", this.expertInfo);\r\n        } else {\r\n          console.warn(\"localStorage中未找到expertInfo\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"初始化专家信息失败:\", error);\r\n      }\r\n    },\r\n\r\n    handleChange(value) {\r\n      if(Object.keys(this.selectSupplier).length != 0){\r\n        this.tmpSave();\r\n      }\r\n      this.selectSupplier = this.options.find((item) => {\r\n        return item.bidderName == value;\r\n      });\r\n\r\n      // 根据bidderid获取供应商因素及其对应页码\r\n      this.bidderFactor = this.factorsPage[this.selectSupplier.bidderId];\r\n\r\n      const data = {\r\n        expertResultId: this.expertInfo.resultId,\r\n        projectId: this.$route.query.projectId,\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n      };\r\n      getDetailByPsxx(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.factorList = response.data;\r\n          const factor = this.factorList.find((item) => {\r\n            return item.bidderName == value;\r\n          }).evalExpertEvaluationDetails;\r\n          if (factor != null) {\r\n            factor.map((item) => {\r\n              this.defaultRatingArray[item.scoringMethodUitemId].reason =\r\n                item.evaluationRemark;\r\n              this.defaultRatingArray[item.scoringMethodUitemId].state =\r\n                item.evaluationResult;\r\n            });\r\n          } else {\r\n            Object.keys(this.defaultRatingArray).forEach((key) => {\r\n              this.defaultRatingArray[key].state = null;\r\n              this.defaultRatingArray[key].reason = \"\";\r\n            });\r\n          }\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      this.showResponseFile();\r\n    },\r\n\t  validateScore(itemId, event) {\r\n\t\t  const inputValue = parseFloat(event);\r\n\t\t  console.log(\"inputValue\", inputValue);\r\n\t\t  \r\n\t\t  // 获取当前评分项的最大分值\r\n\t\t  const currentItem = this.scoringSystem.uitems.find(item => item.entMethodItemId === itemId);\r\n\t\t  let maxScore = null;\r\n\t\t  \r\n\t\t  if (currentItem) {\r\n\t\t\t  // 如果有分数挡位，使用挡位中的最大值\r\n\t\t\t  if (currentItem.scoreLevel && currentItem.scoreLevel.length > 0) {\r\n\t\t\t\t  const scoreLevels = currentItem.scoreLevel.split(',').map(item => parseFloat(item.trim())).filter(item => !isNaN(item));\r\n\t\t\t\t  if (scoreLevels.length > 0) {\r\n\t\t\t\t\t  maxScore = Math.max(...scoreLevels);\r\n\t\t\t\t  }\r\n\t\t\t  } else {\r\n\t\t\t\t  // 否则使用配置的最大分值\r\n\t\t\t\t  maxScore = parseFloat(currentItem.score);\r\n\t\t\t  }\r\n\t\t  }\r\n\t\t  \r\n\t\t  console.log(\"maxScore\", maxScore);\r\n\t\t  \r\n\t\t  if (!isNaN(inputValue) && maxScore !== null) {\r\n\t\t\t  if (inputValue > maxScore) {\r\n\t\t\t\t  this.$message.warning(`输入分数不能超过${maxScore}分，请重新输入`);\r\n\t\t\t\t  // 将输入值限制为最大分数值\r\n\t\t\t\t  this.defaultRatingArray[itemId].state = \"\";\r\n\t\t\t  } else if (inputValue < 0) {\r\n\t\t\t\t  this.$message.warning(\"输入分数不能小于0分\");\r\n\t\t\t\t  this.defaultRatingArray[itemId].state = \"\";\r\n\t\t\t  }\r\n\t\t  }\r\n\t  },\r\n    showResponseFile() {\r\n      if (Object.keys(this.selectSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        this.activeButton = 'response'; // 设置当前激活按钮\r\n        this.double = false;\r\n        this.procurementShow = false;\r\n        this.responseShow = true;\r\n        this.responsePdf = this.file.file[this.selectSupplier.bidderId];\r\n      }\r\n    },\r\n    fileContrast() {\r\n      if (Object.keys(this.selectSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        this.activeButton = 'contrast'; // 设置当前激活按钮\r\n        this.double = true;\r\n        this.procurementShow = true;\r\n        this.responseShow = true;\r\n        this.responsePdf = this.file.file[this.selectSupplier.bidderId];\r\n      }\r\n    },\r\n    showInfo(item) {\r\n\t    // 检查PDF是否渲染完成\r\n\t    if (!this.canJumpToPage()) {\r\n\t\t    this.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t    return;\r\n\t    }\r\n\t\t\t\r\n      this.selectNode = item;\r\n\r\n      // 如果只显示采购文件，使用采购文件页码信息\r\n      if (this.procurementShow && !this.responseShow) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        if (item.jumpToPage) {\r\n          this.$refs.procurement.skipPage(item.jumpToPage);\r\n        } else if (this.entDocProcurementPage && this.entDocProcurementPage[item.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[item.itemName]);\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 如果显示响应文件或对比模式，需要选择供应商\r\n      if (Object.keys(this.bidderFactor).length != 0) {\r\n        // 跳转到响应文件对应页码\r\n        if (this.responseShow && this.$refs.response) {\r\n\t        if (!this.responsePdfRendered) {\r\n\t\t        this.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t        return;\r\n\t        }\r\n\t\t\t\t\t\r\n          this.$refs.response.skipPage(\r\n            this.bidderFactor[this.selectNode.itemName]\r\n          );\r\n        }\r\n\r\n        // 跳转到采购文件对应页码\r\n        if (this.procurementShow && this.$refs.procurement) {\r\n\t        if (!this.procurementPdfRendered) {\r\n\t\t        this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t        return;\r\n\t        }\r\n\t\t\t\t\t\r\n          // 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n          if (this.entDocProcurementPage && this.entDocProcurementPage[item.itemName]) {\r\n            this.$refs.procurement.skipPage(this.entDocProcurementPage[item.itemName]);\r\n          } else {\r\n            // 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n            // 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n          }\r\n        }\r\n      } else {\r\n        this.$message.warning(\"请先选择供应商\");\r\n      }\r\n    },\r\n    initDefaultRatingArray(){\r\n      Object.keys(this.defaultRatingArray).forEach((key) => {\r\n        this.defaultRatingArray[key].state = null;\r\n        this.defaultRatingArray[key].reason = \"\";\r\n      });\r\n    },\r\n    /**\r\n     * 校验所有评分项是否填写完整\r\n     * @returns {boolean} 是否全部填写\r\n     */\r\n    validateAllRatings() {\r\n      for (const item of this.scoringSystem.uitems) {\r\n        const state = this.defaultRatingArray[item.entMethodItemId].state;\r\n\r\n        // 评分结果未填写\r\n        if (state === null || state === '' || state === undefined) {\r\n          // this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n          return true;\r\n        }\r\n\r\n        // 对于分数评分，检查是否为有效数值\r\n        if (!item.scoreLevel || item.scoreLevel.length === 0 || item.scoreLevel === null || item.scoreLevel === undefined) {\r\n          const score = parseFloat(state);\r\n          if (isNaN(score) || score < 0) {\r\n            this.$message.warning(`${item.itemName}的评分必须是有效的数值且不能小于0`);\r\n            return false;\r\n          }\r\n          // 检查分数是否超过最大值\r\n          const maxScore = this.getMaxScore(item);\r\n          if (score > maxScore) {\r\n            this.$message.warning(`${item.itemName}的评分不能超过${maxScore}分`);\r\n            return false;\r\n          }\r\n        } else {\r\n          // 对于有挡位的评分，检查是否在允许的挡位范围内\r\n          const scoreLevels = item.scoreLevel.split(',').map(level => level.trim());\r\n          if (!scoreLevels.includes(state.toString())) {\r\n            this.$message.warning(`${item.itemName}的评分必须选择指定的挡位：${item.scoreLevel}`);\r\n            return false;\r\n          }\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n\r\n    tmpSave(){\r\n      console.log(\"-------开始保存评审结果----------------\");\r\n\r\n      // 先校验所有评分项是否填写完整\r\n      if (!this.validateAllRatings()) {\r\n        return Promise.resolve({ code: 0, success: false }); // 校验失败\r\n      }\r\n\r\n      var ratingArray = JSON.parse(JSON.stringify(this.defaultRatingArray));\r\n\r\n      var data = [];\r\n      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        const item = this.scoringSystem.uitems[index];\r\n        const itemId = item.entMethodItemId;\r\n        // 获取当前项对应的评分结果\r\n        const evaluationResult = ratingArray[itemId].state;\r\n        if (evaluationResult === null || evaluationResult === \"\") {\r\n          // 如果评分结果为空，则不保存此条信息\r\n          console.log(\"-------评分结果为空，不保存此条信息----------------\");\r\n          continue;\r\n        }\r\n          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n          const evaluationRemark = ratingArray[itemId].reason || \"\";\r\n          data.push({\r\n            scoringMethodUitemId: itemId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            entId: this.selectSupplier.bidderId,\r\n            evaluationResult: evaluationResult,\r\n            evaluationRemark: evaluationRemark\r\n          });\r\n        }\r\n        if(data.length>0){\r\n            console.log(\"-------开始后台保存评审结果----------------\");\r\n            return scoringFactors(data).then((response) => {\r\n              console.log(response.msg);\r\n              if (response.code == 200) {\r\n                this.$message.success(\"保存成功\");\r\n                return { code: 200, success: true };\r\n              } else {\r\n                this.$message.warning(response.msg);\r\n                return { code: response.code, success: false };\r\n              }\r\n            }).catch((error) => {\r\n              this.$message.error(\"保存失败\");\r\n              return { code: 0, success: false };\r\n            });\r\n        }else{\r\n          return Promise.resolve({ code: 200, success: true }); // 没有数据需要保存时也返回成功\r\n        }\r\n    },\r\n    save() {\r\n      if (this.supplier == \"\") {\r\n        this.$message.warning(\"请选择供应商\");\r\n      } else {\r\n        //const data = this.generatingSavedData();\r\n        var data = [];\r\n        for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n          const item = this.scoringSystem.uitems[index];\r\n          const itemId = item.entMethodItemId;\r\n          // 获取当前项对应的评分结果\r\n          const evaluationResult = this.defaultRatingArray[itemId].state;\r\n          console.log(evaluationResult)\r\n          if (evaluationResult === null || evaluationResult === \"\") {\r\n            // 如果评分结果为空，弹出提示，提示内容包含该项的itemName\r\n            this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n            return; // 直接返回，不再继续构建数据，等待用户填写完整\r\n          }\r\n          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）\r\n          const evaluationRemark = this.defaultRatingArray[itemId].reason || \"\";\r\n          data.push({\r\n            scoringMethodUitemId: itemId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            entId: this.selectSupplier.bidderId,\r\n            evaluationResult: evaluationResult,\r\n            evaluationRemark: evaluationRemark\r\n          });\r\n        }\r\n        scoringFactors(data).then((response) => {\r\n          if (response.code == 200) {\r\n            this.$message.success(response.msg);\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 生成保存数据\r\n    generatingSavedData() {\r\n      var data = [];\r\n      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {\r\n        data.push({\r\n          scoringMethodUitemId:\r\n            this.scoringSystem.uitems[index].entMethodItemId,\r\n          expertResultId: this.expertInfo.resultId,\r\n          entId: this.selectSupplier.bidderId,\r\n          evaluationResult:\r\n            this.defaultRatingArray[\r\n              this.scoringSystem.uitems[index].entMethodItemId\r\n            ].state,\r\n          evaluationRemark:\r\n            this.defaultRatingArray[\r\n              this.scoringSystem.uitems[index].entMethodItemId\r\n            ].reason,\r\n        });\r\n      }\r\n      return data;\r\n    },\r\n    submit() {\r\n        this.tmpSave().then((saveResult) => {\r\n          // 检查保存结果，如果校验失败则不继续提交\r\n          if (!saveResult || saveResult.success === false) {\r\n            return; // 校验失败，不继续提交流程\r\n          }\r\n\r\n          const data = {\r\n            projectId: this.$route.query.projectId,\r\n            expertResultId: this.expertInfo.resultId,\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n          };\r\n          checkReviewSummary(data).then((response) => {\r\n            if (response.code == 200) {\r\n            // 修改专家进度\r\n            const status = {\r\n              evalExpertScoreInfoId: JSON.parse(\r\n                localStorage.getItem(\"evalExpertScoreInfo\")\r\n              ).evalExpertScoreInfoId,\r\n              evalState: 1,\r\n            };\r\n            editEvalExpertScoreInfo(status).then((res) => {\r\n              if (res.code == 200) {\r\n                this.$message.success(\"提交成功\");\r\n              }\r\n            });\r\n            this.$emit(\"send\", \"two\");\r\n          } else {\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      })\r\n    },\r\n    /**\r\n     * 显示采购文件PDF\r\n     */\r\n    viewPurchasing() {\r\n      this.activeButton = 'procurement'; // 设置当前激活按钮\r\n      this.double = false; // 单文件模式\r\n      this.responseShow = false; // 不显示响应文件\r\n      this.procurementShow = true; // 显示采购文件\r\n\r\n      // 右侧评分项显示为采购文件的评分项\r\n      let pageProcurementArr = []; // 采购文件评分项数组\r\n      for (let item in this.entDocProcurementPage){\r\n        pageProcurementArr.push({\r\n          itemName: item,\r\n          jumpToPage: this.entDocProcurementPage[item]\r\n        })\r\n      }\r\n\r\n      console.log(this.scoringSystem.uitems);\r\n      console.log(pageProcurementArr)\r\n      this.pageProcurement = [];\r\n      for (let i = 0; i < this.scoringSystem.uitems.length;i++){\r\n        for (let j = 0; j < pageProcurementArr.length;j++){\r\n          if (this.scoringSystem.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n            this.pageProcurement.push({...this.scoringSystem.uitems[i],...pageProcurementArr[j]});\r\n          }\r\n        }\r\n      }\r\n      console.log(this.pageProcurement)\r\n    },\r\n\r\n    /**\r\n     * 跳转到采购文件对应页码\r\n     * @param {Object} item - 评分项对象\r\n     */\r\n    jumpToProcurementPage(item) {\r\n      if (item.jumpToPage && this.$refs.procurement) {\r\n        this.$refs.procurement.skipPage(item.jumpToPage);\r\n      }\r\n    },\r\n\t  \r\n\t  /**\r\n\t   * 检查是否可以跳转页面\r\n\t   * @returns {boolean} 是否可以跳转\r\n\t   */\r\n\t  canJumpToPage() {\r\n\t\t  // 如果只显示采购文件\r\n\t\t  if (this.procurementShow && !this.responseShow) {\r\n\t\t\t  return this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  // 如果只显示响应文件\r\n\t\t  if (this.responseShow && !this.procurementShow) {\r\n\t\t\t  return this.responsePdfRendered;\r\n\t\t  }\r\n\t\t  // 如果对比模式（两个都显示）\r\n\t\t  if (this.responseShow && this.procurementShow) {\r\n\t\t\t  return this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  return false;\r\n\t  },\r\n\t  /**\r\n\t   * 处理PDF渲染状态变化\r\n\t   * @param {boolean} isRendered 是否渲染完成\r\n\t   * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t   */\r\n\t  handlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t\t  if (pdfType === 'response') {\r\n\t\t\t  this.responsePdfRendered = isRendered;\r\n\t\t  } else if (pdfType === 'procurement') {\r\n\t\t\t  this.procurementPdfRendered = isRendered;\r\n\t\t  }\r\n\t\t  \r\n\t\t  if (isRendered) {\r\n\t\t\t  console.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t\t  }\r\n\t  },\r\n\t  \r\n    // 跳转到二次报价\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query: query });\r\n    },\r\n    // 跳转到询标\r\n    bidInquiry() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      this.$router.push({ path: \"/bidInquiry\", query: query });\r\n    },\r\n    // 获取因素对应页码\r\n    getFactorsPage() {\r\n      this.factorsPage = JSON.parse(localStorage.getItem(\"entDocResponsePage\"));\r\n    },\r\n    downloadFile(item){\r\n      this.$download.zip(item.filePath,item.fileName);\r\n    },\r\n    \r\n    /**\r\n     * 获取评分项的最大分值\r\n     * @param {Object} item - 评分项对象\r\n     * @returns {number} 最大分值\r\n     */\r\n    getMaxScore(item) {\r\n      if (!item) return 0;\r\n      \r\n      // 如果有分数挡位，使用挡位中的最大值\r\n      if (item.scoreLevel && item.scoreLevel.length > 0 && item.scoreLevel !== null && item.scoreLevel !== undefined) {\r\n        const scoreLevels = item.scoreLevel.split(',').map(level => parseFloat(level.trim())).filter(level => !isNaN(level));\r\n        if (scoreLevels.length > 0) {\r\n          return Math.max(...scoreLevels);\r\n        }\r\n      }\r\n      \r\n      // 否则使用配置的最大分值\r\n      return parseFloat(item.score) || 0;\r\n    },\r\n\t  \r\n\t  // ========== 悬停相关 ==========\r\n\t  /**\r\n\t   * 显示评分项悬浮框\r\n\t   * @param {Object} factorItem 评分项对象\r\n\t   */\r\n\t  showFactorTooltip(factorItem) {\r\n\t\t  if (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\t\t  \r\n\t\t  // 清除之前的定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t  this.tooltipTimer = setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = factorItem;\r\n\t\t  }, 300); // 300ms延迟\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 隐藏评分项悬浮框\r\n\t   */\r\n\t  hideFactorTooltip() {\r\n\t\t  // 清除定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t  setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = null;\r\n\t\t  }, 100);\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t   */\r\n\t  clearTooltipTimer() {\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t  }\r\n  },\r\n  mounted() {\r\n    this.init();\r\n    this.getFactorsPage();\r\n  },\r\n\t\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.business-review-container {\r\n  min-height: 57vh;\r\n  display: flex;\r\n}\r\n.business-review-main {\r\n  min-height: 57vh;\r\n  width: 79%;\r\n}\r\n.business-review-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.business-review-title-group {\r\n  display: flex;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333333;\r\n}\r\n.business-review-title {\r\n  /* 保持原样 */\r\n}\r\n.business-review-step-group {\r\n  display: grid;\r\n  justify-items: center;\r\n  position: relative;\r\n  bottom: -30px;\r\n}\r\n.business-review-step-text {\r\n  font-size: 12px;\r\n}\r\n.business-review-step-img {\r\n  width: 80px;\r\n  height: 30px;\r\n  margin-right: 20px;\r\n}\r\n.business-review-header-btns {\r\n  text-align: right;\r\n}\r\n.business-review-header-btns-group {\r\n  margin-top: 20px;\r\n}\r\n.business-review-header-btns-group .item-button {\r\n  background-color: #176ADB;\r\n  color: #FFFFFF;\r\n  border: 1px solid #176ADB;\r\n}\r\n.business-review-pdf-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  height: 82%;\r\n}\r\n.business-review-pdf {\r\n  width: 49%;\r\n}\r\n.business-review-pdf-border-right {\r\n  border-right: 1px solid #176ADB;\r\n}\r\n.business-review-pdf-border-left {\r\n  border-left: 1px solid #176ADB;\r\n}\r\n.business-review-divider {\r\n  min-height: 57vh;\r\n  width: 1%;\r\n  background-color: #F5F5F5;\r\n}\r\n.business-review-side {\r\n  min-height: 57vh;\r\n  width: 20%;\r\n}\r\n.business-review-select-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.business-review-select {\r\n  width: 100%;\r\n}\r\n.business-review-side-content {\r\n  padding: 15px 20px;\r\n}\r\n.business-review-factor-item {\r\n  margin-bottom: 10px;\r\n}\r\n.business-review-factor-title-group {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  text-align: left;\r\n  width: 98%;\r\n}\r\n.business-review-factor-title {\r\n  cursor: pointer;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 500;\r\n  font-size: 16px;\r\n  color: #333333;\r\n  letter-spacing: 0;\r\n}\r\n.business-review-factor-score-group {\r\n  display: flex;\r\n  width: 100%;\r\n  justify-content: flex-end;\r\n  padding: 10px;\r\n}\r\n.business-review-factor-score {\r\n  color: green;\r\n  font-size: 16px;\r\n}\r\n.business-review-submit-group {\r\n  display: flex;\r\n  margin: 32px 0;\r\n  justify-content: space-evenly;\r\n}\r\n.business-review-submit-btn {\r\n  background-color: #176ADB;\r\n}\r\n.business-review-content {\r\n  text-align: left;\r\n  font-size: 14px;\r\n}\r\n.business-review-content-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 15px;\r\n  color: #176ADB;\r\n  letter-spacing: 0;\r\n}\r\n.business-review-content-remark {\r\n  padding: 6px 30px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n\t  transition: all 0.3s ease;\r\n\t  padding: 4px 8px;\r\n\t  border-radius: 4px;\r\n\t  \r\n\t  &:hover {\r\n\t\t  background-color: #f0f8ff;\r\n\t\t  color: #176ADB;\r\n\t\t  transform: translateX(2px);\r\n\t  }\r\n  }\r\n}\r\n.item-button {\r\n  border: 1px solid #979797;\r\n  width: 150px;\r\n  height: 36px;\r\n  margin: 0 10px;\r\n  font-weight: 700;\r\n  font-size: 17px;\r\n  border-radius: 6px;\r\n  color: #333333;\r\n  &:hover {\r\n    color: #333333;\r\n  }\r\n}\r\n.business-blue-btn {\r\n  background-color: #176ADB !important;\r\n  color: #fff !important;\r\n  border: 1px solid #176ADB !important;\r\n}\r\n.business-blue-btn-active {\r\n  background-color: #FF6B35 !important;\r\n  color: #fff !important;\r\n  border: 1px solid #FF6B35 !important;\r\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.item-button-little {\r\n  width: 124px;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #fff;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n  .fileItem {\r\n    transition: all 0.3s ease;\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    }\r\n\r\n    ::v-deep .el-card__body {\r\n      padding: 0;\r\n    }\r\n  }\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\t\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\t\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\t\t\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\t\t\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\t\t\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n</style>\r\n"]}]}